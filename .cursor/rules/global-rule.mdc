---
description: 
globs: 
alwaysApply: true
---
# CRM-CS-Task-Server项目代码规范

## 1. 项目概述

CRM-CS-Task-Server是好买基金CRM系统的任务服务组件，负责各类定时任务、数据处理和自动化工作流程。该组件基于Spring Boot、Dubbo架构开发，使用Oracle数据库，为CRM系统提供后台任务调度支持。

## 2. 技术栈规范

### 2.1 基础技术栈
- 编程语言: Java 8
- 框架: Spring Boot
- 微服务: Dubbo 2.5.3
- 配置中心: Nacos
- 数据库: Oracle
- 缓存: Redis
- 数据库ORM: MyBatis
- 数据库连接池: Druid
- 消息队列: 内部消息服务

### 2.2 依赖版本控制
项目依赖版本应严格遵循pom.xml中定义的版本号，重要依赖版本如下：
- Dubbo: 2.5.3
- JUnit: 4.13.2
- Redis Cache Client: 3.0-RELEASE

## 3. 项目结构规范

### 3.1 包结构

```
com.howbuy.cs
├── job                     // 定时任务
├── task                    // 任务处理核心模块
│   ├── buss                // 业务逻辑层
│   ├── dao                 // 数据访问层接口
│   ├── mapper              // MyBatis映射文件
│   ├── model               // 数据模型
│   ├── service             // 服务层
│   └── util                // 工具类
├── cacheService            // 缓存服务
├── common                  // 通用组件
│   ├── Constant            // 常量定义
│   ├── enums               // 枚举类
│   ├── exception           // 异常定义
│   └── base                // 基础组件
└── bookcust                // 预约客户管理
    ├── buss                // 预约客户业务逻辑层
    ├── dao                 // 预约客户数据访问层
    ├── model               // 预约客户数据模型
    └── service             // 预约客户服务层
```

### 3.2 资源结构

```
resources
├── applicationContext.xml  // Spring上下文配置
├── bootstrap.properties    // 引导配置
├── log4j2.xml             // 日志配置
└── META-INF               // 元数据
```

## 4. 命名规范

### 4.1 类命名规范

- **Service接口**: 以`Service`结尾，如`AgentDistributeTaskService`
- **Service实现类**: 以`ServiceImpl`结尾，如`AgentDistributeTaskServiceImpl`
- **数据访问层接口**: 以`Dao`或`Mapper`结尾，如`TenpayCustDao`
- **业务逻辑类**: 以`Buss`结尾，如`BookingCustInfoBuss`
- **任务类**: 以`Task`结尾，如`StockSplitTask`
- **定时任务类**: 以`Job`结尾，如`AgentDistributeTaskJob`
- **模型类**: 实体类名应与表名相关，保持语义一致，如`CmConsultant`
- **枚举类**: 以`Enum`结尾，如`ConscardBussCodeEnum`
- **常量类**: 以`Constant`结尾，如`CacheKeyConstant`
- **工具类**: 以`Util`或`Utils`结尾，如`DateTimeUtil`

### 4.2 方法命名规范

- **查询方法**: 以`query`、`find`、`get`开头
- **新增方法**: 以`add`、`save`、`insert`开头
- **修改方法**: 以`update`、`modify`开头
- **删除方法**: 以`delete`、`remove`开头
- **执行方法**: 以`execute`、`process`、`handle`开头
- **判断方法**: 以`is`、`has`、`can`开头
- **业务方法**: 使用动词+名词，表达明确的业务含义

### 4.3 变量命名规范

- 使用驼峰命名法(camelCase)
- 常量使用全大写，单词间下划线分隔，如`ONE_DAY_SECOND`
- 布尔类型变量使用`is`、`has`、`can`等前缀
- 集合类型变量使用复数形式，如`customerList`
- 避免使用拼音或中文拼音首字母缩写

## 5. 代码风格规范

### 5.1 基本规范
- 缩进: 使用4个空格
- 行长度: 不超过120个字符
- 注释: 使用中文注释，保持简洁明了
- 文件编码: UTF-8

### 5.2 导包顺序
1. 静态导入
2. java
3. javax
4. org
5. com

### 5.3 代码格式
- 遵循阿里巴巴Java开发手册规范
- 类成员变量顺序: 静态变量 -> 实例变量 -> 构造方法 -> 普通方法
- 方法参数过多时应换行，每行一个参数
- 花括号使用K&R风格(左花括号不换行)

### 5.4 注解使用
- 使用Lombok简化代码，但实体类优先使用`@Setter`/`@Getter`而非`@Data`
- Service实现类使用`@Service`注解
- DAO接口使用`@MapperScan`注解
- 业务逻辑类使用`@Component`或`@Service`注解
- 日志使用`@Slf4j`注解

## 6. 注释规范

### 6.1 类注释模板

```java
/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

/**
 * @description: 类的描述信息
 * <AUTHOR>
 * @date 2025-03-19 08:30:14
 * @since JDK 1.8
 */
```

### 6.2 方法注释模板

```java
/**
 * @description: 方法的描述信息
 * @param param1 参数1的描述
 * @param param2 参数2的描述
 * @return 返回值的描述
 * <AUTHOR>
 * @date 2025-03-19 08:30:14
 * @since JDK 1.8
 */
```

### 6.3 Dubbo接口注释模板

```java
/**
 * @api {DUBBO} com.howbuy.cs.client.facade.DemoFacade.execute(request) execute()
 * @apiVersion 1.0.0
 * @apiGroup DemoFacadeService
 * @apiName demo接口
 * @apiParam (请求参数) {String} textParam 文本参数
 * @apiParam (请求参数) {String} [requestId] 请求ID
 * @apiParamExample 请求参数示例
 * requestId=B7W&textParam=iNQX9QzA
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccessExample 响应结果示例
 * {"code":"97gSdRTO","data":{"demoVo":{"testLabel":"6Uqs"}},"description":"g7"}
 */
```

### 6.4 注释原则
- 类、方法必须有注释
- 关键业务逻辑必须有注释
- 复杂算法或特殊处理必须有详细注释
- 如果类或方法已有注释，不修改原有注释中的@author和@date值
- 注释应简明扼要，避免啰嗦或无意义的注释

## 7. 层次结构和职责

### 7.1 各层职责

- **Job层**: 定时任务触发层，负责接收调度消息，简单处理后调用Service层
- **Service层**: 业务服务层，协调调用Buss层和Dao层，处理业务流程
- **Buss层**: 业务逻辑层，实现具体业务逻辑和算法
- **Dao层**: 数据访问层，定义数据库操作接口
- **Model层**: 数据模型层，定义与数据库表对应的实体类
- **Mapper层**: MyBatis映射文件，实现SQL语句和Dao接口的映射

### 7.2 层次调用规则

1. Job -> Service: 定时任务调用服务层
2. Service -> Buss/Dao: 服务层调用业务逻辑层和数据访问层
3. Buss -> Dao: 业务逻辑层调用数据访问层
4. 严格禁止跨层调用，如Job直接调用Dao

## 8. 异常处理规范

### 8.1 异常分类和使用
- 优先使用自定义异常类型
- 业务异常应继承自`BusinessException`
- 系统异常应被捕获并转换为友好的错误信息

### 8.2 异常处理原则
- 不允许吞没异常(catch后不处理)
- 异常应进行适当日志记录
- 在Job层捕获所有异常，防止影响调度系统
- 使用`try-finally`确保资源释放
- 避免过度捕获异常，精确捕获特定异常类型

## 9. 日志规范

### 9.1 日志级别使用
- ERROR: 系统错误，影响功能或业务的异常
- WARN: 警告信息，可能的问题但不影响主要功能
- INFO: 重要业务流程节点，任务开始/结束标记
- DEBUG: 调试信息，详细流程数据(生产环境不开启)

### 9.2 日志内容规范
- 必须记录异常堆栈
- 敏感信息需脱敏处理(如手机号、身份证号等)
- 记录必要的上下文信息以便问题定位
- 使用占位符格式化日志，避免字符串拼接
- 使用AlertLogUtil进行重要异常告警

## 10. 事务管理规范

### 10.1 事务注解使用
- 使用`@Transactional`注解管理事务
- 事务注解应用在Service层或Buss层方法上
- 查询方法无需事务
- 更新方法使用`@Transactional(rollbackFor = Exception.class)`

### 10.2 事务原则
- 保持事务粒度合适，避免过大事务
- 避免在事务中进行RPC调用
- 避免在事务中进行耗时操作
- 严格遵循数据库隔离级别

## 11. 缓存使用规范

### 11.1 缓存Key命名
- 遵循`CacheKeyPrefix` + 业务标识 + ID的命名方式
- 使用常量定义缓存过期时间
- 合理设置过期时间，避免缓存永不过期

### 11.2 缓存使用原则
- 优先使用统一的缓存服务
- 先更新数据库，再删除缓存
- 使用分布式锁控制并发
- 重要业务数据不依赖缓存，需二次确认

## 12. 并发处理规范

### 12.1 线程池使用
- 使用Spring提供的线程池或自定义线程池
- 设置合理的线程池参数
- 任务应可拒绝或优雅降级
- 避免直接创建Thread

### 12.2 并发控制
- 使用分布式锁控制跨实例并发
- 使用悲观锁或乐观锁控制数据库并发
- 避免使用同步块，优先使用并发工具类
- 注意避免死锁和资源泄露

## 13. 安全规范

### 13.1 数据安全
- 敏感数据加密存储
- 接口参数和返回值脱敏
- 避免SQL注入
- 避免日志中输出敏感信息

### 13.2 代码安全
- 禁止使用BeanUtils.copyProperties进行属性拷贝
- 禁止使用反射修改对象属性
- 避免使用不安全的第三方库
- 定期升级依赖版本，修复安全漏洞

## 14. 性能优化规范

### 14.1 SQL优化
- 合理使用索引
- 避免全表扫描
- 大数据量操作使用分批处理
- 避免循环中执行数据库操作

### 14.2 代码优化
- 使用合理的数据结构
- 避免创建不必要的对象
- 正确关闭资源
- 使用批量操作替代循环单条操作

## 15. 测试规范

### 15.1 单元测试
- 使用JUnit编写单元测试
- 使用Mockito模拟依赖
- 单元测试覆盖率不低于80%
- 每个方法至少有一个测试用例

### 15.2 集成测试
- 使用Spring TestContext框架
- 测试环境使用内存数据库
- 避免测试依赖外部系统
- 使用随机端口避免冲突

## 16. 版本管理规范

### 16.1 版本命名
- 主版本号.次版本号.修订号-标识
- 示例: 20250103-RELEASE

### 16.2 分支管理
- 主分支: master
- 开发分支: develop
- 功能分支: feature/xxx
- 修复分支: bugfix/xxx
- 发布分支: release/xxx

## 17. 任务调度规范

### 17.1 任务实现
- 继承AbstractBatchMessageJob
- 实现doProcessMessage方法
- 任务代码应具备幂等性
- 使用分布式锁避免重复执行

### 17.2 任务调度
- 配置适当的调度周期
- 记录任务执行日志
- 异常不影响下次调度
- 任务超时自动终止 