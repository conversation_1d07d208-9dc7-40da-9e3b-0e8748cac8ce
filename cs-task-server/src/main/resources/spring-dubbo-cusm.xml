<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

	<!--引用dubbo请求日志过滤器-->
	<dubbo:consumer filter="requestLogDubboFilter"/>

	<!-- message-remote -->
	<dubbo:registry id="message-remote" protocol="zookeeper" address="${dubbo.registries.message-remote.address}" check="false" />
	<dubbo:reference id="sendMessageDubboService" registry="message-remote" interface="com.howbuy.cc.message.SendMsgService" timeout="60000" retries="0" check="false" cluster="failover"/>

	<!-- acc-center-server -->
	<dubbo:registry id="acc-center-server" protocol="zookeeper" address="${dubbo.registries.acc-center-server.address}" check="false" />
	<dubbo:reference id="QuerySingleService" registry="acc-center-server" interface="com.howbuy.cc.hbone.service.QuerySingleService" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="HboneService" registry="acc-center-server" interface="com.howbuy.cc.hbone.service.HboneService" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="QueryCustInfoFacade" registry="acc-center-server"  interface="com.howbuy.acccenter.facade.query.querycustinfo.QueryCustInfoFacade"  check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="QueryTxAcctInfoFacade" registry="acc-center-server" interface="com.howbuy.acccenter.facade.query.querytxacctinfofacade.QueryTxAcctInfoFacade" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="QueryWechatAcctBindFacade" registry="acc-center-server" interface="com.howbuy.acccenter.facade.query.querywechatbindinfo.QueryWechatAcctBindFacade" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="QueryCustMobileFacade" registry="acc-center-server" interface="com.howbuy.acccenter.facade.query.sensitive.mobile.QueryCustMobileFacade" check="false" retries="0" timeout="1200000" />

	<!-- crm-nt-server -->
	<dubbo:registry id="crm-nt-server" protocol="zookeeper" address="${dubbo.registries.crm-nt-server.address}" check="false" />
	<dubbo:reference id="CmPushMsgService" registry="crm-nt-server" interface="com.howbuy.crm.nt.pushmsg.service.CmPushMsgService" timeout="60000" retries="0" check="false" cluster="failover"/>
	<dubbo:reference id="queryConsultantExpService" registry="crm-nt-server" interface="com.howbuy.crm.nt.consultant.service.QueryConsultantExpService" timeout="60000" retries="0" check="false" cluster="failover"/>

	<!-- crm-core-server -->
	<dubbo:registry id="crm-core-server" protocol="zookeeper" address="${dubbo.registries.crm-core-server.address}" check="false" />
	<dubbo:reference id="CreateConscustInfoService" registry="crm-core-server" interface="com.howbuy.crm.conscust.service.CreateConscustInfoService" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="QueryRepeatCustService" registry="crm-core-server" interface="com.howbuy.crm.conscust.service.QueryRepeatCustService" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="QueryCmSourceInfoService" registry="crm-core-server" interface="com.howbuy.crm.conscust.service.QueryCmSourceInfoService" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="QueryConscustInfoService" registry="crm-core-server" interface="com.howbuy.crm.conscust.service.QueryConscustInfoService" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="QueryCustconstantInfoService" registry="crm-core-server" interface="com.howbuy.crm.conscust.service.QueryCustconstantInfoService" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="UpdateConscustInfoService" registry="crm-core-server" interface="com.howbuy.crm.conscust.service.UpdateConscustInfoService" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="UpdateCustconstantInfoService" registry="crm-core-server" interface="com.howbuy.crm.conscust.service.UpdateCustconstantInfoService" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="core.HbOrgUserService" registry="crm-core-server" interface="com.howbuy.crm.orguser.service.HbOrgUserService" check="false"  timeout="12000" />
	<dubbo:reference id="reloadFundBasicCacheService" registry="crm-core-server" interface="com.howbuy.crm.jjxx.service.ReloadFundBasicCacheService" check="false"  timeout="36000" group="*" />
	<dubbo:reference id="ConsultantInfoService" registry="crm-core-server" interface="com.howbuy.crm.consultant.service.ConsultantInfoService" check="false" retries="2" timeout="1200000" />
	<dubbo:reference id="CsCommunicateVisitService" registry="crm-core-server" interface="com.howbuy.crm.custvisit.service.CsCommunicateVisitService" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="JjxxInfoService" registry="crm-core-server" interface="com.howbuy.crm.jjxx.service.JjxxInfoService" check="false" retries="2" timeout="12000" />


	<!-- howbuy-auth-service -->
	<dubbo:registry id="howbuy-auth-service" protocol="zookeeper" address="${dubbo.registries.howbuy-auth-service.address}" check="false" />
	<dubbo:reference id="DecryptBatchFacade" registry="howbuy-auth-service" interface="com.howbuy.auth.facade.decrypt.DecryptBatchFacade" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="EncryptBatchFacade" registry="howbuy-auth-service" interface="com.howbuy.auth.facade.encrypt.EncryptBatchFacade" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="DecryptSingleFacade" registry="howbuy-auth-service" interface="com.howbuy.auth.facade.decrypt.DecryptSingleFacade" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="EncryptSingleFacade" registry="howbuy-auth-service" interface="com.howbuy.auth.facade.encrypt.EncryptSingleFacade" check="false" retries="0" timeout="1200000" />

	<!-- high-order-trade-remote -->
	<dubbo:registry id="high-order-trade-remote" protocol="zookeeper" address="${dubbo.registries.high-order-trade-remote.address}" check="false" />
	<dubbo:reference id="QueryBalanceTxAcctNoByFundFacade" registry="high-order-trade-remote" interface="com.howbuy.tms.high.orders.facade.search.queryBalanceTxAcctNoByFund.QueryBalanceTxAcctNoByFundFacade" check="false" retries="0" timeout="1200000" />

	<!-- crm-td-server -->
	<dubbo:registry id="crm-td-server" protocol="zookeeper" address="${dubbo.registries.crm-td-server.address}" check="false" />
	<dubbo:reference id="CrmHighDealOrderService" registry="crm-td-server" interface="com.howbuy.crm.dealorder.service.CrmHighDealOrderService" check="false" retries="0" timeout="1200000" />

	<!-- howbuy-member-remote -->
	<dubbo:registry id="howbuy-member-remote" protocol="zookeeper" address="${dubbo.registries.howbuy-member-remote.address}" check="false" />
	<dubbo:reference id="ProductReportUpdatePushService" registry="howbuy-member-remote" interface="com.howbuy.member.service.report.ProductReportUpdatePushService" check="false" retries="0" timeout="1200000" />


</beans>
