create table CM_CONSULTANT_CHANGEORG_REC
(
    RECORD_NO        VARCHAR2(25)  not null
        constraint PK_CONSULTANT_CHANGEORG_REC
            primary key,
    CONSCODE         VARCHAR2(100) not null,
    TEAM_CODE        VARCHAR2(32),
    MANAGEMENT_CODE  VARCHAR2(100),
    MANAGEMENT_LEVEL VARCHAR2(2)   not null,
    START_DATE       VARCHAR2(8)   not null,
    END_DATE         VARCHAR2(8),
    CREATE_DTM       TIMESTAMP(6)  not null,
    UPDATE_DTM       TIMESTAMP(6)  not null
);

comment on table CM_CONSULTANT_CHANGEORG_REC is '投顾管理层变更明细表';

comment on column CM_CONSULTANT_CHANGEORG_REC.RECORD_NO is '记录号';

comment on column CM_CONSULTANT_CHANGEORG_REC.CONSCODE is '理财顾问代码';

comment on column CM_CONSULTANT_CHANGEORG_REC.TEAM_CODE is '所属架构代码';

comment on column CM_CONSULTANT_CHANGEORG_REC.MANAGEMENT_CODE is '所属管理层代码';

comment on column CM_CONSULTANT_CHANGEORG_REC.MANAGEMENT_LEVEL is '管理层层级，3-分总；4-区域副；5-区域总';

comment on column CM_CONSULTANT_CHANGEORG_REC.START_DATE is '开始日期';

comment on column CM_CONSULTANT_CHANGEORG_REC.END_DATE is '结束日期';

comment on column CM_CONSULTANT_CHANGEORG_REC.CREATE_DTM is '创建日期时间';

comment on column CM_CONSULTANT_CHANGEORG_REC.UPDATE_DTM is '更新日期时间';

create index UK_CONSULTANT_CHANGEORG
    on CM_CONSULTANT_CHANGEORG_REC (CONSCODE, MANAGEMENT_LEVEL, START_DATE);

create index INDEX_CONSULTANT_CHANGEORGDT
    on CM_CONSULTANT_CHANGEORG_REC (START_DATE, END_DATE);

create table CM_STOCK_SPLIT_CONFIG
(
    ID                VARCHAR2(100) not null
        constraint PK_STOCK_SPLIT_CONFIG
            primary key,
    CONFIG_TYPE       VARCHAR2(2)   not null,
    FORMER_ORG_CODE   VARCHAR2(100),
    FORMER_CONS_CODE  VARCHAR2(100) not null,
    NEWLY_ORG_CODE    VARCHAR2(100),
    NEWLY_CONS_CODE   VARCHAR2(100) not null,
    CONFIG_LEVEL      VARCHAR2(2)   not null,
    ACTIVE_DT         VARCHAR2(8),
    ACTIVE_FLAG       VARCHAR2(1),
    VALID_FLAG        VARCHAR2(1)   not null,
    CAL_START_DT      VARCHAR2(8)   not null,
    CAL_END_DT        VARCHAR2(8)   not null,
    FORMER_CAL_RATE   NUMBER(18, 4),
    NEWLY_CAL_RATE    NUMBER(18, 4),
    LOCK_DURATION_AMT NUMBER(18, 4),
    AUDIT_STATUS      VARCHAR2(1)   not null,
    LAST_AUDIT_STATUS VARCHAR2(1),
    REMARK            VARCHAR2(1000),
    CREATOR           VARCHAR2(50)  not null,
    CREATE_TIMESTAMP  TIMESTAMP(6)  not null,
    MODIFIER          VARCHAR2(50),
    MODIFY_TIMESTAMP  TIMESTAMP(6),
    AUDITOR           VARCHAR2(50),
    AUDIT_TIMESTAMP   TIMESTAMP(6),
    REC_STAT          VARCHAR2(1)   not null
)
;

comment on table CM_STOCK_SPLIT_CONFIG is '存量分成 配置表'
;

comment on column CM_STOCK_SPLIT_CONFIG.ID is '主键
'
;

comment on column CM_STOCK_SPLIT_CONFIG.CONFIG_TYPE is '分成类型
2：育成、
3：异动-协商、
4：异动-规则、
5：重复划转-协商、
6：重复划转-规则
7: 重复划转-新老划断
8: 接管-单客户
9：接管-人力'
;

comment on column CM_STOCK_SPLIT_CONFIG.FORMER_ORG_CODE is '原管理层'
;

comment on column CM_STOCK_SPLIT_CONFIG.FORMER_CONS_CODE is '原投顾'
;

comment on column CM_STOCK_SPLIT_CONFIG.NEWLY_ORG_CODE is '新管理层'
;

comment on column CM_STOCK_SPLIT_CONFIG.NEWLY_CONS_CODE is '新投顾'
;

comment on column CM_STOCK_SPLIT_CONFIG.CONFIG_LEVEL is '层级
1-理财师、
3-分总、
4-区域执行副总、
5-区域总、
6-销售总监'
;

comment on column CM_STOCK_SPLIT_CONFIG.ACTIVE_DT is '激活时间 yyyyMMdd'
;

comment on column CM_STOCK_SPLIT_CONFIG.ACTIVE_FLAG is '激活状态:是否激活1是0否'
;

comment on column CM_STOCK_SPLIT_CONFIG.VALID_FLAG is '是否生效1是0否'
;

comment on column CM_STOCK_SPLIT_CONFIG.CAL_START_DT is '计算起始日期 yyyyMMdd'
;

comment on column CM_STOCK_SPLIT_CONFIG.CAL_END_DT is '计算结束日期 yyyyMMdd'
;

comment on column CM_STOCK_SPLIT_CONFIG.FORMER_CAL_RATE is '计入比例_原'
;

comment on column CM_STOCK_SPLIT_CONFIG.NEWLY_CAL_RATE is '计入比例_新'
;

comment on column CM_STOCK_SPLIT_CONFIG.LOCK_DURATION_AMT is '锁定存续D'
;

comment on column CM_STOCK_SPLIT_CONFIG.AUDIT_STATUS is '审核状态 0-待审核、2-审核不通过、1-审核通过'
;

comment on column CM_STOCK_SPLIT_CONFIG.LAST_AUDIT_STATUS is '最近一次审核状态'
;

comment on column CM_STOCK_SPLIT_CONFIG.REMARK is '备注'
;

comment on column CM_STOCK_SPLIT_CONFIG.CREATOR is '创建人'
;

comment on column CM_STOCK_SPLIT_CONFIG.CREATE_TIMESTAMP is '创建时间'
;

comment on column CM_STOCK_SPLIT_CONFIG.MODIFIER is '修改人'
;

comment on column CM_STOCK_SPLIT_CONFIG.MODIFY_TIMESTAMP is '修改时间'
;

comment on column CM_STOCK_SPLIT_CONFIG.AUDITOR is '审核人'
;

comment on column CM_STOCK_SPLIT_CONFIG.AUDIT_TIMESTAMP is '审核时间'
;

comment on column CM_STOCK_SPLIT_CONFIG.REC_STAT is '记录有效状态（1-正常  0-删除）'
;

create table CM_STOCK_SPLIT_DETAIL
(
    ID               VARCHAR2(100) not null
        constraint PK_STOCK_SPLIT_DETAIL
            primary key,
    CONFIG_ID        VARCHAR2(100) not null,
    CUST_NO          VARCHAR2(100) not null,
    CREATOR          VARCHAR2(50)  not null,
    CREATE_TIMESTAMP TIMESTAMP(6)  not null,
    MODIFIER         VARCHAR2(50),
    MODIFY_TIMESTAMP TIMESTAMP(6),
    REC_STAT         VARCHAR2(1)   not null,
    UPPER_LIMIT      NUMBER(18, 4)
)
;

comment on table CM_STOCK_SPLIT_DETAIL is '存量分成-客户明细表'
;

comment on column CM_STOCK_SPLIT_DETAIL.ID is '主键'
;

comment on column CM_STOCK_SPLIT_DETAIL.CONFIG_ID is '配置-存量分成的ID主键'
;

comment on column CM_STOCK_SPLIT_DETAIL.CUST_NO is '客户号'
;

comment on column CM_STOCK_SPLIT_DETAIL.CREATOR is '创建人'
;

comment on column CM_STOCK_SPLIT_DETAIL.CREATE_TIMESTAMP is '创建时间'
;

comment on column CM_STOCK_SPLIT_DETAIL.MODIFIER is '修改人'
;

comment on column CM_STOCK_SPLIT_DETAIL.MODIFY_TIMESTAMP is '修改时间'
;

comment on column CM_STOCK_SPLIT_DETAIL.REC_STAT is '记录有效状态（1-正常  0-删除）'
;

comment on column CM_STOCK_SPLIT_DETAIL.UPPER_LIMIT is '分配上限_原'
;

create index IDX_STOCK_SPLIT_DETAIL
    on CM_STOCK_SPLIT_DETAIL (CONFIG_ID)
;

create table CM_CUSTCONSTANT
(
    CUSTNO       VARCHAR2(10) not null,
    CONSCODE     VARCHAR2(100),
    STARTDT      VARCHAR2(8),
    ENDDT        VARCHAR2(8),
    MEMO         VARCHAR2(90),
    RECSTAT      VARCHAR2(1),
    CHECKFLAG    VARCHAR2(1),
    CREATOR      VARCHAR2(100),
    MODIFIER     VARCHAR2(100),
    CHECKER      VARCHAR2(100),
    CREDT        VARCHAR2(8),
    MODDT        VARCHAR2(8),
    BINDDATE     DATE,
    BEFOREHISID  VARCHAR2(32),
    OPERATE_DATE DATE
)
;

comment on table CM_CUSTCONSTANT is '客户投顾关系表'
;

comment on column CM_CUSTCONSTANT.CUSTNO is '客户号'
;

comment on column CM_CUSTCONSTANT.CONSCODE is '投资顾问代码'
;

comment on column CM_CUSTCONSTANT.STARTDT is '开始日期'
;

comment on column CM_CUSTCONSTANT.ENDDT is '结束日期'
;

comment on column CM_CUSTCONSTANT.MEMO is '备注'
;

comment on column CM_CUSTCONSTANT.RECSTAT is '记录状态'
;

comment on column CM_CUSTCONSTANT.CHECKFLAG is '复核标志'
;

comment on column CM_CUSTCONSTANT.CREATOR is '创建人'
;

comment on column CM_CUSTCONSTANT.MODIFIER is '修改人'
;

comment on column CM_CUSTCONSTANT.CHECKER is '复核人'
;

comment on column CM_CUSTCONSTANT.CREDT is '记录创建日期'
;

comment on column CM_CUSTCONSTANT.MODDT is '记录修改日期'
;

comment on column CM_CUSTCONSTANT.BINDDATE is '绑定（分配）时间（带时分秒）'
;

comment on column CM_CUSTCONSTANT.BEFOREHISID is '上一条分配id'
;

comment on column CM_CUSTCONSTANT.OPERATE_DATE is '操作时间'
;

create unique index PK_CM_CUSTCONSTANT
    on CM_CUSTCONSTANT (CUSTNO)
;

create index IND1_CM_CUSTCONSTANT
    on CM_CUSTCONSTANT (CUSTNO, CONSCODE)
;

create index IND2_CM_CUSTCONSTANT
    on CM_CUSTCONSTANT (CONSCODE, STARTDT, CUSTNO)
;

create index IDX_CUSTCONS_CONSCODE
    on CM_CUSTCONSTANT (CONSCODE)
;

create index IDX_CM_CUSTCONSTANT_ENDDT
    on CM_CUSTCONSTANT (ENDDT)
;

create index IDX_CM_CUSTCONSTANT_STARTDT
    on CM_CUSTCONSTANT (STARTDT)
;

