<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context" xmlns:nacos="http://nacos.io/schema/nacos"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://nacos.io/schema/nacos http://nacos.io/schema/nacos.xsd">

    <context:annotation-config/>
    <context:component-scan base-package="com.howbuy.cs"/>
    <import resource="spring-dubbo-prov.xml"/>
    <import resource="spring-dubbo-cusm.xml"/>
    <import resource="performance.xml"/>
    <import resource="spring-mybatis.xml"/>

    <!--开启注解-->
    <nacos:annotation-driven></nacos:annotation-driven>
    <!--指定nacos配置地址-->
    <nacos:global-properties
            server-addr="${nacosServerAddr}"
            namespace="${namespace}"/>
    <!--指定dataId,group-id, 是否是自动刷新-->
    <nacos:property-source data-id="${dataId}" group-id="${groupId}" auto-refreshed="true"/>

    <bean id="transOwnerTask" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <!-- 核心线程数 -->
        <property name="corePoolSize" value="10"/>
        <!-- 最大线程数 -->
        <property name="maxPoolSize" value="20"/>
        <!-- 队列最大长度 -->
        <property name="queueCapacity" value="600"/>
        <!-- 线程池维护线程所允许的空闲时间，默认为60s -->
        <property name="keepAliveSeconds" value="60"/>
    </bean>
</beans>