<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tx="http://www.springframework.org/schema/tx"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://www.springframework.org/schema/tx 
       http://www.springframework.org/schema/tx/spring-tx.xsd ">
	<!-- 自动扫描 -->
	<tx:annotation-driven/>

	<bean id="dataSource" class="com.howbuy.common.db.HowbuyBasicDataSource3"
		destroy-method="close" init-method="init">
		<property name="hpsUrl" value="${PS_URL}"/>
		<property name="url" value="${master.ds.jdbcUrl}" />
		<property name="username" value="${master.ds.user}" />
		<property name="password" value="${master.ds.password}" />
		<property name="initialSize" value="${master.ds.initialSize}" />
		<property name="minIdle" value="${master.ds.minIdle}" />
		<property name="maxIdle" value="${master.ds.maxIdle}" />
		<property name="maxActive" value="${master.ds.maxActive}" />
		<property name="maxWait" value="${master.ds.maxWait}" />
		<property name="timeBetweenEvictionRunsMillis" value="${master.ds.timeBetweenEvictionRunsMillis}" />
		<property name="minEvictableIdleTimeMillis" value="${master.ds.minEvictableIdleTimeMillis}" />
		<property name="validationQuery" value="${master.ds.validationQuery}" />
		<property name="testWhileIdle" value="${master.ds.testWhileIdle}" />
		<property name="testOnBorrow" value="${master.ds.testOnBorrow}" />
		<property name="testOnReturn" value="${master.ds.testOnReturn}" />

	</bean>

	<bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
		<property name="dataSource" ref="dataSource" />
		<property name="configLocation" value="classpath:mybatis-config.xml" />
		<!-- 自动配置别名 -->
		<property name="typeAliasesPackage" value="com.howbuy.cs.*.model" />
		<property name="mapperLocations">
			<list>
				<!-- 扫描每个模块下面的mapper.xml映射文件 -->
				<value>classpath*:com/howbuy/cs/**/mapper/*Mapper.xml</value>
			</list>
		</property>
		<property name="plugins">
			<array>
				<bean class="com.github.pagehelper.PageInterceptor">
					<property name="properties">
						<value>
							helperDialect=oracle
							supportMethodsArguments=true
							params=count=countSql
						</value>
					</property>
				</bean>
			</array>
		</property>

	</bean>

	<!-- DAO接口所在包名，Spring会自动查找其下的类 ,包下的类需要使用@MapperScan注解,否则容器注入会失败 -->
	<bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
		<property name="basePackage" value="com.howbuy.cs.*.dao"/>
		<property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
	</bean>

	<tx:annotation-driven transaction-manager="transactionManager" />

	<!--事务管理器 -->
	<bean id="transactionManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="dataSource" />
	</bean>

</beans>