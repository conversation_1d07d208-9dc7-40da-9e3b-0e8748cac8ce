<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
			http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

	<!-- 提供方应用信息，用于计算依赖关系 -->
	<dubbo:application name="${dubbo.application.name}" logger="slf4j"/>


	<dubbo:registry id="cs-task-registry" protocol="${dubbo.registry.protocol}" address="${dubbo.registries.cs-task-server.address}"
					file="${dubbo.registry.file}"/>

	<dubbo:protocol name="dubbo" port="${dubbo.protocol.port}" threads="${dubbo.protocol.threads}"
					threadpool="${dubbo.protocol.threadpool}" dispatcher="${dubbo.protocol.dispatcher}"
					queues="${dubbo.protocol.queues}"/>

	<dubbo:provider timeout="${dubbo.provider.timeout}" filter="uuidFilter,accessFilter"/>

	<!-- 创建预约记录接口 -->
	<dubbo:service interface="com.howbuy.cs.bookcust.service.BookingCustInfoService"
				   ref="bookingCustInfoService" registry="cs-task-registry" retries="0" timeout="60000" />

	<!-- 客户投顾划转接口 -->
	<dubbo:service interface="com.howbuy.cs.task.service.AdvisorAutoTransTaskService"
				   ref="advisorAutoTransTaskService" group="csTaskGroup" registry="cs-task-registry" retries="0" timeout="60000" />
				   
	<!-- 非公募客户投顾分配接口:新 -->
	<dubbo:service interface="com.howbuy.cs.task.service.AdvisorNPDistributeTaskNewService"
				   ref="advisorNPDistributeTaskNewService" group="csTaskGroup" registry="cs-task-registry" retries="0" timeout="60000" />			   

	<!-- 投顾任务表重置接口 -->
	<dubbo:service interface="com.howbuy.cs.task.service.AdvisorResetTaskService"
				   ref="advisorResetTaskService" group="csTaskGroup" registry="cs-task-registry" retries="0" timeout="60000" />

	<!-- 同步坐席基础数据接口 -->
	<dubbo:service interface="com.howbuy.cs.task.service.AgentDataCollectTaskService"
				   ref="agentDataCollectTaskService" group="csTaskGroup" registry="cs-task-registry" retries="0" timeout="60000" />

	<!-- 坐席任务分配接口 -->
	<dubbo:service interface="com.howbuy.cs.task.service.AgentDistributeTaskService"
				   ref="agentDistributeTaskService" group="csTaskGroup" registry="cs-task-registry" retries="0" timeout="60000" />

	<!-- 坐席任务回收接口 -->
	<dubbo:service interface="com.howbuy.cs.task.service.AgentRecycleTaskService"
				   ref="agentRecycleTaskService" group="csTaskGroup" registry="cs-task-registry" retries="0" timeout="60000" />

	<!-- 坐席任务规则重置接口 -->
	<dubbo:service interface="com.howbuy.cs.task.service.AgentResetTaskService"
				   ref="agentResetTaskService" group="csTaskGroup" registry="cs-task-registry" retries="0" timeout="60000" />

	<!-- 账户中心数据匹配接口 -->
	<dubbo:service interface="com.howbuy.cs.task.service.CustAccountCenterTaskService"
				   ref="custAccountCenterTaskService" group="csTaskGroup" registry="cs-task-registry" retries="0" timeout="60000" />

	<!-- cms来源异常客户接口 -->
	<dubbo:service interface="com.howbuy.cs.task.service.CustCmsTaskService"
				   ref="custCmsTaskService" group="csTaskGroup" registry="cs-task-registry" retries="0" timeout="60000" />
				   
	<!-- 处理T+14预约再处理客户 -->
	<dubbo:service interface="com.howbuy.cs.task.service.TransBookCustTaskService"
				   ref="transBookCustTaskService" group="csTaskGroup" registry="cs-task-registry" retries="0" timeout="60000" />
				   
	<!-- 处理呼损任务数据 -->
	<dubbo:service interface="com.howbuy.cs.task.service.HandleLeaveMsgCustTaskService"
				   ref="handleLeaveMsgCustTaskService" group="csTaskGroup" registry="cs-task-registry" retries="0" timeout="60000" />

</beans>