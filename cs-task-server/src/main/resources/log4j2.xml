<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO" packages="com">
    <!-- 变量配置 -->
    <Properties>
        <Property name="log_path">/data/logs/cs-task-server</Property>
        <property name="mainLogName">cs-task-server-main</property>
    </Properties>
    
	<!--%enc语法 只有log4j 2.x才支持-->
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <CustomPatternLayout pattern='{"time":"%d{yyyy-MM-dd HH:mm:ss.SSS}","custNo":"%X{custNo}","tid":"%X{uuid}","status":"%X{status}","labelName":"%markerSimpleName","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%msg%n'>
	   			<replaces>
	      			<replace regex='(\\"idNo\\":\\"|\\"idno\\":\\")(\w{3})\w{5,8}(\w{4})' replacement="$1$2********$3" />
	      			<replace regex='(\\"mobile\\":\\"|\\"mobileNo\\":\\"|\\"telno\\":\\"|\\"telNo\\":\\")(\d{3})\d{4}(\d{4})' replacement="$1$2****$3" />
	      			<replace regex='(\\"bankAcct\\":\\"|\\"bankNo\\":\\"|\\"bankacct\\":\\")(\d{6})\d{1,20}(\d{4})' replacement="$1$2********$3" />
	      			<replace regex='(\\"email\\":\\")(.{3})(.*?)@([\w.]+)' replacement="$1$2****@$4" />
	      			<replace regex='(\\"addr\\":\\")(.*?)(\\")' replacement="$1*******$3" />
	   			</replaces>
			</CustomPatternLayout>
        </Console>
        
        <RollingFile name="errorLog" fileName="${log_path}/error.log" filePattern="${log_path}/%d{yyyyMMdd}/error-%d{yyyy-MM-dd}.log">
        	<Filters>  
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>  
            </Filters>
            <CustomPatternLayout pattern='{"time":"%d{yyyy-MM-dd HH:mm:ss.SSS}","custNo":"%X{custNo}","tid":"%X{uuid}","status":"%X{status}","labelName":"%markerSimpleName","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%msg%n'>
	   			<replaces>
	      			<replace regex='(\\"idNo\\":\\"|\\"idno\\":\\")(\w{3})\w{5,8}(\w{4})' replacement="$1$2********$3" />
	      			<replace regex='(\\"mobile\\":\\"|\\"mobileNo\\":\\"|\\"telno\\":\\"|\\"telNo\\":\\")(\d{3})\d{4}(\d{4})' replacement="$1$2****$3" />
	      			<replace regex='(\\"bankAcct\\":\\"|\\"bankNo\\":\\"|\\"bankacct\\":\\")(\d{6})\d{1,20}(\d{4})' replacement="$1$2********$3" />
	      			<replace regex='(\\"email\\":\\")(.{3})(.*?)@([\w.]+)' replacement="$1$2****@$4" />
	      			<replace regex='(\\"addr\\":\\")(.*?)(\\")' replacement="$1*******$3" />
	   			</replaces>
			</CustomPatternLayout>
            <Policies>  
		        <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
                <SizeBasedTriggeringPolicy size="1024 MB" />
		    </Policies>
        </RollingFile>
        
        <RollingFile name="DefaultLog" fileName="${log_path}/cs-task-server.log" filePattern="${log_path}/%d{yyyyMMdd}/cs-task-server-%d{yyyy-MM-dd}.log">
        	<Filters>
                <ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>  
            </Filters>
            <CustomPatternLayout pattern='{"time":"%d{yyyy-MM-dd HH:mm:ss.SSS}","custNo":"%X{custNo}","tid":"%X{uuid}","status":"%X{status}","labelName":"%markerSimpleName","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%msg%n'>
	   			<replaces>
	      			<replace regex='(\\"idNo\\":\\"|\\"idno\\":\\")(\w{3})\w{5,8}(\w{4})' replacement="$1$2********$3" />
	      			<replace regex='(\\"mobile\\":\\"|\\"mobileNo\\":\\"|\\"telno\\":\\"|\\"telNo\\":\\")(\d{3})\d{4}(\d{4})' replacement="$1$2****$3" />
	      			<replace regex='(\\"bankAcct\\":\\"|\\"bankNo\\":\\"|\\"bankacct\\":\\")(\d{6})\d{1,20}(\d{4})' replacement="$1$2********$3" />
	      			<replace regex='(\\"email\\":\\")(.{3})(.*?)@([\w.]+)' replacement="$1$2****@$4" />
	      			<replace regex='(\\"addr\\":\\")(.*?)(\\")' replacement="$1*******$3" />
	   			</replaces>
			</CustomPatternLayout>
            <Policies>  
		        <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
                <SizeBasedTriggeringPolicy size="1024 MB" />
		    </Policies>
            <DefaultRolloverStrategy max="100" />
        </RollingFile>
        
        <RollingFile name="AccessLog" fileName="${log_path}/access.log" filePattern="${log_path}/%d{yyyyMMdd}/access-%d{yyyy-MM-dd}.log">
            <PatternLayout pattern='{"time":"%d{yyyy-MM-dd HH:mm:ss.SSS}","custNo":"%X{custNo}","tid":"%X{uuid}","status":"%X{status}","labelName":"%markerSimpleName","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%msg%n'/>
            <Policies>  
		        <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
                <SizeBasedTriggeringPolicy size="1024 MB" />
		    </Policies>
        </RollingFile>
        <RollingFile name="AlertJsonLogger" filename="${log_path}/alterJson.log"
                     filepattern="${log_path}/%d{yyyyMMdd}/alterJson.log">
            <PatternLayout pattern="%msg%n" />
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true" />
            </Policies>
            <DefaultRolloverStrategy max="30" />
        </RollingFile>

        <RollingFile name="MainLogger"
                     fileName="${log_path}/${mainLogName}.log"
                     filePattern="${log_path}/%d{yyyyMMdd}/${mainLogName}.log">
            <PatternLayout pattern="%msg%n" />
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
        </RollingFile>
    </Appenders>

    <Loggers>
    	<logger name="howbuy-dubbo-access-log" level="info" additivity="false">
            <appender-ref ref="AccessLog" />  
        </logger>
        <AsyncLogger name="alertJsonLog" level="info" additivity="false">
            <appender-ref ref="AlertJsonLogger"/>
        </AsyncLogger>
        <logger name="mainlog" level="info" additivity="false">
            <appender-ref ref="MainLogger"/>
        </logger>
        <Root level="info">
            <AppenderRef ref="DefaultLog"/>
            <AppenderRef ref="errorLog"/>
            <AppenderRef ref="Console"/>
        </Root>
    </Loggers>
</Configuration>