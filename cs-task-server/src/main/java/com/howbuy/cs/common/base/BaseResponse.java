package com.howbuy.cs.common.base;

import java.io.Serializable;

/**
 * @apiDefine BaseResponse 
 * @apiGroup booking
 * @apiSuccess {String} returnCode 返回码(0000，执行成功)
 * @apiSuccess {String} description 说明描述
 * @apiSuccess {String} duration 调用时间
 */
public class BaseResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	private String returnCode;
	private String description;
	private Long duration;

	@Override
	public String toString() {
		return " returnCode = " + returnCode + " description = " + description;
	}

	public String getReturnCode() {
		return returnCode;
	}

	public void setReturnCode(String returnCode) {
		this.returnCode = returnCode;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Long getDuration() {
		return duration;
	}

	public void setDuration(Long duration) {
		this.duration = duration;
	}

	/**
	 * 通过返回的code获取枚举类型
	 * 
	 * @return BaseConstantEnum 枚举
	 * @deprecated 可能没啥使用场景。
	 */
	public BaseConstantEnum getResult() {
		return BaseConstantEnum.getEnum(this.returnCode);
	}

	/**
	 * 通过BaseConstantEnum 设置 description 和 returncode
	 * 
	 * @param BaseConstantEnum
	 */
	public void putResult(BaseConstantEnum e) {
		this.description = e.getDescription();
		this.returnCode = e.getCode();
	}

	/**
	 * 通过BaseConstantEnum、message 设置 description 和 returncode
	 * 
	 * @param e
	 *            枚举值
	 * @param message
	 *            返回的信息
	 */
	public void putResult(BaseConstantEnum e, String message) {
		this.description = e.getDescription() + ":" + message;
		this.returnCode = e.getCode();
	}

	/**
	 * 通过BaseConstantEnum 设置 description 和 returncode
	 * 
	 * @param BaseConstantEnum
	 */
	public void putBaseResult(BaseConstantEnum e) {
		this.description = e.getDescription();
		this.returnCode = e.getCode();
	}

	/**
	 * 通过BaseConstantEnum、message 设置 description 和 returncode
	 * 
	 * @param e
	 *            枚举值
	 * @param message
	 *            返回的信息
	 */
	public void putBaseResult(BaseConstantEnum e, String message) {
		this.description = e.getDescription() + ":" + message;
		this.returnCode = e.getCode();
	}

	/**
	 * 业务成功时，设置返回状态。
	 */
	public void success() {
		this.putBaseResult(BaseConstantEnum.SUCCESS);
	}

	/**
	 * 没有查询到数据时，设置返回状态。
	 */
	public void noDataError() {
		this.putBaseResult(BaseConstantEnum.NULL_ERROR);
	}

	/**
	 * 系统错误时，设置返回状态。
	 */
	public void sysError() {
		this.putBaseResult(BaseConstantEnum.SYS_ERROR);
	}

	/**
	 * 请求参数校验失败时，设置返回状态及附加错误消息。
	 * 
	 * @param appendMsg
	 */
	public void invalidReqParams(String appendMsg) {
		this.putBaseResult(BaseConstantEnum.PARAM_ERROR, appendMsg);
	}

	/**
	 * 判断返回状态是否代表业务成功。
	 */
	public boolean isSuccessful() {
		return BaseConstantEnum.SUCCESS.getCode().equals(returnCode);
	}

	/**
	 * 设置返回代码和描述。
	 */
	public void putReturnCodeAndDesc(String returnCode, String description) {
		this.returnCode = returnCode;
		this.description = description;
	}

}
