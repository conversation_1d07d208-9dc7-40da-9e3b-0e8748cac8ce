/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.common.Constant;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/6/19 14:06
 * @since JDK 1.8
 */
public class MessageBusinessIdConstant {


    /**
     * 好买基金APP 有报告更新，关联基金数=1
     */
    public static final String FUND_APP_REPORT_UPDATE_ONE_FUND = "60553";

    /**
     * 好买基金APP 有报告更新，关联基金数>1
     */
    public static final String FUND_APP_REPORT_UPDATE_MORE_THAN_ONE_FUND = "60554";

    /**
     * 臻财公众号 报告更新
     */
    public static final String ZC_OFFICIAL_ACCOUNT_REPORT_UPDATE = "60324";

    /**
     * 短信 有报告更新，关联基金数=1 分销= 好买
     */
    public static final String SMS_REPORT_UPDATE_ONE_FUND_DISTRIBUTION_FUND = "60555";

    /**
     * 短信 有报告更新，关联基金数>1 分销= 好买
     */
    public static final String SMS_REPORT_UPDATE_MORE_THAN_ONE_FUND_DISTRIBUTION_FUND = "60561";

    /**
     * 短信 有报告更新，关联基金数=1 分销= 好臻
     */
    public static final String SMS_REPORT_UPDATE_ONE_FUND_DISTRIBUTION_HZ = "60556";

    /**
     * 短信 有报告更新，关联基金数>1 分销= 好臻
     */
    public static final String SMS_REPORT_UPDATE_MORE_THAN_ONE_FUND_DISTRIBUTION_HZ = "60557";

    /**
     * 短信 有报告更新，关联基金数=1 分销= 好买香港
     */
    public static final String SMS_REPORT_UPDATE_ONE_FUND_DISTRIBUTION_FUND_HK = "60558";

    /**
     * 短信 有报告更新，关联基金数>1 分销= 好买香港
     */
    public static final String SMS_REPORT_UPDATE_MORE_THAN_ONE_FUND_DISTRIBUTION_FUND_HK = "60559";


}