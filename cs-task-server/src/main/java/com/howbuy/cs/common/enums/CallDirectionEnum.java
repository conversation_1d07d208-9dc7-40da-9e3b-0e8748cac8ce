/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.common.enums;

/**
 * @description: 调用方向枚举
 * <AUTHOR>
 * @date 2025-04-02 20:40:47
 * @since JDK 1.8
 */
public enum CallDirectionEnum {
     
    /**
     * 入站调用
     */
    IN("in"),
     
    /**
     * 出站调用
     */
    OUT("out");
     
    private final String direction;
     
    CallDirectionEnum(String direction) {
        this.direction = direction;
    }
     
    public String getDirection() {
        return direction;
    }
}
