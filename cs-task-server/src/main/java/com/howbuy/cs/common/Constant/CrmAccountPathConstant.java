/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.common.Constant;

/**
 * @description: CRM账户服务接口路径常量类，用于存放CRM账户服务接口路径常量
 * <AUTHOR>
 * @date 2023/12/14 13:39
 * @since JDK 1.8
 */
public class CrmAccountPathConstant {

    /**
     * 存量分成配置类型: 10-接管系统
     */
    public static final String STOCK_SPLIT_CONFIG_TYPE_TAKEOVER = "10";

    /**
     * 原架构代码默认值
     */
    public static final String DEFAULT_FORMER_ORG_CODE = "1";

    /**
     * 存量分成配置结束日期
     */
    public static final String STOCK_SPLIT_END_DATE = "********";

    /**
     * 有效标志
     */
    public static final String VALID_FLAG = "1";

    /**
     * 审核状态-已审核
     */
    public static final String AUDIT_STATUS_APPROVED = "1";

    /**
     * 记录状态-有效
     */
    public static final String REC_STAT_VALID = "1";
    
    /**
     * 记录状态-无效
     */
    public static final String REC_STAT_INVALID = "0";

    /**
     * 系统操作人
     */
    public static final String SYSTEM_OPERATOR = "system";

    /**
     * 日期格式
     */
    public static final String DATE_FORMAT_PATTERN = "yyyyMMdd";

    /**
     * 根据香港客户号查询香港客户信息接口路径
     */
    public static final String QUERY_HK_CUST_INFO_BY_HK_TX_ACCT_NO = "/hkcustinfo/queryhkcustinfo";

    /**
     * 根据投顾客户号查询香港客户信息接口路径
     */
    public static final String QUERY_HK_CUST_INFO_BY_CUST_NO = "/hkcustinfo/queryHkCustInfoByCustNo";

    /**
     * 分页查询香港账户中心香港客户信息列表
     */
    public static final String QUERY_HK_CUST_INFO_BY_PAGE = "/hkcustinfo/queryhkcustinfopage";

    /**
     * 关联投顾客户号和香港客户号
     */
    public static final String RELATE_CONSCUST_AND_HKTXACCTNO = "/hkcustinfo/bindhktxacct";

    /**
     * 关联投顾客户号和香港客户号
     */
    public static final String DISRELATE_CONSCUST_AND_HKTXACCTNO = "/hkcustinfo/unbindhktxacct";

    /**
     * 关联投顾客户号和一账通号
     */
    public static final String DISRELATE_CONSCUST_AND_HBNOE = "/hbonecustinfo/unbindhbone";
    /**
     * 关联投顾客户号和香港客户号
     */
    public static final String MERGE_CONSCUST_LIST_AND_HKTXACCTNO = "/hkcustinfo/mergehktxacct";

    /**
     * 关联投顾客户号和香港客户号
     */
    public static final String CREATE_HK_TX_ACCT_NO = "/hkcustinfo/createhktxacctinfo";


    /**
     * 根据投顾客户号查询一账通账户详细信息
     */
    public static final String QUERY_HBONE_CUST_DETAIL_INFO_BY_CUSTNO = "/hbonecustinfo/queryhbonecustdetailinfobycustno";



    /**
     * 根据香港交易账号查询香港客户风险测评问卷流水
     */
    public static final String QUERY_HK_KYC_ANSWER_LIST_BY_HKTXACCTNO = "/hkcustinfo/queryHkKycAnswerListByHkTxAcctNo";


    /**
     * 根据一账通号 查询账户中心客户信息
     */
    public static final String QUERY_HBONE_CUST_DETAILINFO_BY_HBONENO = "/hbonecustinfo/queryhbonecustdetailinfobyhboneno";


    /**
     * 分页查询香港客户异常信息列表
     */
    public static final String QUERY_HK_CUST_ABNORMAL_INFO_BY_PAGE = "/hkabnormal/queryhkabmoamlpage";

    /**
     * 分页查询一账通客户异常信息列表
     */
    public static final String QUERY_HBONE_CUST_ABNORMAL_INFO_BY_PAGE = "/hboneabnormal/queryhboneabmoamlpage";

    /**
     * 处理香港客户异常信息
     */
    public static final String DEAL_HK_CUST_ABNORMAL_INFO = "/hkabnormal/dealabnormal";

    /**
     * 处一账通客户异常信息
     */
    public static final String DEAL_HBONE_CUST_ABNORMAL_INFO = "/hboneabnormal/dealabnormal";


    /**
     *查询 香港客户异常信息 明细列表信息
     */
    public static final String QUERY_HK_CUST_ABNORMAL_DETAIL_INFO_ = "/hkabnormal/queryhkabmoamldetaillist";

    /**
     * 查询 一账通客户异常信息 明细列表信息
     */
    public static final String QUERY_HBONE_CUST_ABNORMAL_DETAIL_INFO_ = "/hboneabnormal/queryhboneabmoamldetaillist";

    /**
     * 批量处理香港客户异常信息
     */
    public static final String BATCH_DEAL_HK_CUST_ABNORMAL_INFO = "/hkabnormal/batchdealabnormal";
    /**
     * 批量处理一账通客户异常信息
     */
    public static final String BATCH_DEAL_HBONE_CUST_ABNORMAL_INFO = "/hboneabnormal/batchdealabnormal";


    /**
     * 关联异常主表一账通并更新信息
     */
    public static final String ASSOCIATE_HBONE_CUST_ABNORMAL_INFO = "/hboneabnormal/associateabnormalhbone";


    /**
     * 创建投顾客户信息
     */
    public static final String CREATE_CONS_CUST = "/custinfo/createCustInfo";
    
    /**
     * 参数类型-配置存量分成
     */
    public static final String PARAM_TYPE_STOCK_SPLIT = "01";
    
    /**
     * 变更类型-新增
     */
    public static final String CHANGE_TYPE_ADD = "0";
    
    /**
     * 审核状态-待审核
     */
    public static final String AUDIT_STATUS_PENDING = "0";
    
    /**
     * 激活标志-未激活
     */
    public static final String ACTIVE_FLAG_INACTIVE = "0";

    /**
     * 虚拟投顾标志-是
     */
    public static final String CONSULTANT_VIRTUAL_YES = "1";

    /**
     * 管理层级-分总
     */
    public static final String MANAGEMENT_LEVEL_BRANCH_MANAGER = "3";

}