package com.howbuy.cs.common.exception;

import com.howbuy.cs.common.base.BaseConstantEnum;
import com.howbuy.cs.common.base.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * <AUTHOR>
 */
@Slf4j
public class ExceptionCatchAdvice implements MethodInterceptor {

	@Override
	public Object invoke(MethodInvocation invocation) throws Throwable {
		Class<?> returnType = invocation.getMethod().getReturnType();
		if (BaseResponse.class.isAssignableFrom(returnType)) {
			long start = System.currentTimeMillis();
			BaseResponse response = null;
			try {
				response = (BaseResponse) invocation.proceed();
			} catch (Exception e) {
				response = (BaseResponse) returnType.newInstance();
				response.setDescription(BaseConstantEnum.UNKNOWN_ERROR.getDescription());
				response.setReturnCode(BaseConstantEnum.SYS_ERROR.getCode());
				log.error(e.getMessage(), e);
			} finally {
				if (response != null) {
					long end = System.currentTimeMillis();
					response.setDuration(end - start);
				}
			}
			return response;
		} else {
			return invocation.proceed();
		}
	}
}
