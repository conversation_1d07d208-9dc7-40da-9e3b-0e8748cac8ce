/**
 * Project Name:crm-conscard-client
 * File Name:ConscardConstantEnum.java
 * Package Name:com.howbuy.crm.conscard.base
 * Date:2017年5月26日下午5:59:27
 * Copyright (c) 2017, <EMAIL> All Rights Reserved.
 *
 */

package com.howbuy.cs.common.base;

/**
 * ClassName:ConscardConstantEnum <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2017年5月26日 下午5:59:27 <br/>
 * 
 * <AUTHOR>
 * @version
 * @see
 */
public enum ConscardBussCodeEnum {

	// 执行成功
	CONSCARD_SUCCESS("CB_0000", "操作成功"),
	// 参数错误
	CONSCARD__ERROR("CB_0002", "参数错误"),
	// 执行失败
	CONSCARD_FAIL("CB_0001", "操作失败"),
	// 执行失败
	CONSCARD_UNKNOWN_ERROR("CB_9999", "未知异常");

	private String code; // 编码

	private String description; // 描述

	private ConscardBussCodeEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		for (ConscardBussCodeEnum b : ConscardBussCodeEnum.values()) {
			if (b.getCode().equals(code)) {
				return b.description;
			}
		}
		return null;
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return BaseConstantEnum
	 */
	public static BaseConstantEnum getEnum(String code) {
		if (code != null || !"".equals(code)) {
			for (BaseConstantEnum b : BaseConstantEnum.values()) {
				if (b.getCode().equals(code)) {
					return b;
				}
			}
		}
		return null;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

}
