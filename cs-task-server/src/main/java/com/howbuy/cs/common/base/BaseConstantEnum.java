package com.howbuy.cs.common.base;

/**
 * 常用返回参数枚举
 * <AUTHOR>
 * 
 */
public enum BaseConstantEnum {
	//执行成功
	SUCCESS("0000","操作成功"),
	//参数错误
	PARAM_ERROR("0002","参数错误"),
	//执行失败
	FAIL("0001","操作失败"),
	//系统错误
	SYS_ERROR("0003","系统错误"),

	//没有查询到匹配数据
	NULL_ERROR("0004","没有查询到匹配数据"),
	
	CALL_DUBBO_ERROR("9997","调用其他DUBBO服务时出现错误。"),
	
	CALL_HSB_ERROR("9998","调用其他HSB服务时出现错误。"),
	
	UNKNOWN_ERROR("9999","未定义的错误");
	
	
	private String code;	//编码
	
	private String description;  //描述
	
	private BaseConstantEnum(String code,String description){
		this.code=code;
		this.description=description;
	}
	
	/**
	 * 通过code获得
	 * @param code	系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code){
		for(BaseConstantEnum b:BaseConstantEnum.values()){
			if(b.getCode().equals(code)){
				return b.description;
			}
		}
		return null;
	}
	
	/**
	 * 通过code直接返回 整个枚举类型
	 * @param code 系统返回参数编码
	 * @return BaseConstantEnum
	 */
	public static BaseConstantEnum getEnum(String code){
		if(code != null || !"".equals(code)){
			for(BaseConstantEnum b:BaseConstantEnum.values()){
				if(b.getCode().equals(code)){
					return b;
				}
			}
		}
		return null;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}
}
