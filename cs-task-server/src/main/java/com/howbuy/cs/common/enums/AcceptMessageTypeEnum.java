package com.howbuy.cs.common.enums;

/**
 * @description:(crm接受消息类型： 1-营销喜报、2-机构crm审批发送企微提示、3-企微机器人消息、4-邮箱、5-短信、6-带附件的邮箱)
 * @return
 * @author: haoran.zhang
 * @date: 2023/10/7 10:51
 * @since JDK 1.8
 */
public enum AcceptMessageTypeEnum {

    /**
     * 营销喜报
     */
    MARKETING_REPORT("1", "营销喜报"),
    JG_CRM_CHECK("2", "机构crm审批发送企微提示"),

    ROBOT("3", "企微机器人消息"),


    /**
     * 邮箱发送
     */
    EMAIL("4", "邮箱"),



    /**
     * 短信发送
     */
    SMS("5", "短信"),

    /**
     * 带附件的邮箱
     */
    EMAIL_WITH_ATTACHMENT("6", "带附件的邮箱"),

    /**
     * 好买基金APP 内的消息
     */
    HB_FUND_APP("7", "好买基金APP内的消息"),

    /**
     * 自动渠道-通过HBONENO
     *
     * 消息中心的渠道：邮箱、短信、企微等等
     * 具体发哪个渠道，这个是由 产品方 配置的。
     * 举个例子：指定的一个消息模版businessId，产品方 可以既选择发送邮箱、同时又发送短信。
     *
     * 所以，这个枚举，只负责调消息中心发送接口。具体发送渠道由配置决定，不直接指定。
     */
    AUTO_CHANNEL_BY_HBONENO("8", "自动渠道-通过HBONENO"),
    ;

    /**
     * 编码
     */
    private String code;


    /**
     * 描述
     */
    private String description;

    AcceptMessageTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 通过code获得
     *
     * @param code
     *            系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code) {
        AcceptMessageTypeEnum statusEnum=getEnum(code);
        return statusEnum==null?null :statusEnum.getDescription();
    }


    /**
     * 通过code直接返回 整个枚举类型
     *
     * @param code
     *            系统返回参数编码
     * @return BaseConstantEnum
     */
    public static AcceptMessageTypeEnum getEnum(String code) {
        for(AcceptMessageTypeEnum statusEnum : AcceptMessageTypeEnum.values()){
            if(statusEnum.getCode().equals(code)){
                return statusEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
