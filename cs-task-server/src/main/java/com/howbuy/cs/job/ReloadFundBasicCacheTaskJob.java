/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.job;

import com.google.common.base.Throwables;
import com.howbuy.crm.jjxx.service.ReloadFundBasicCacheService;
import com.howbuy.cs.task.service.SyncBpFundBasicInfoServiceImpl;
import com.howbuy.cs.task.util.AlertLogUtil;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: CRM基金信息缓存同步(增量)定时任务（45 0/10 * * * ?）
 * <AUTHOR>
 * @date 2023/10/9 17:23
 * @since JDK 1.8
 */
@Slf4j
@Component
public class ReloadFundBasicCacheTaskJob extends AbstractBatchMessageJob{

    @Value("${sync.TOPIC_RELOAD_FUND_BASIC_CACHE}")
    private String queue;

    @Autowired
    private ReloadFundBasicCacheService reloadFundBasicCacheService;

    @Autowired
    private SyncBpFundBasicInfoServiceImpl syncBpFundBasicInfoService;

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("ReloadFundBasicCacheTaskJob process start");
        try {
            reloadFundBasicCacheService.reload(getContent(message));
        } catch (Exception e){
            AlertLogUtil.alert("ReloadFundBasicCacheTaskJob", Throwables.getStackTraceAsString(e));
            log.error(e.getMessage(), e);
        }
        log.info("reload end");
        try {
            syncBpFundBasicInfoService.syncFundInfoFromJJXX1();
        } catch (Exception e){
            AlertLogUtil.alert("ReloadFundBasicCacheTaskJob", Throwables.getStackTraceAsString(e));
            log.error(e.getMessage(), e);
        }
        log.info("ReloadFundBasicCacheTaskJob process end");
    }

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }
}