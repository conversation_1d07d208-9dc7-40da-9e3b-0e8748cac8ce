/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.job;

import com.google.common.base.Throwables;
import com.howbuy.cs.task.service.AgentResetTaskService;
import com.howbuy.cs.task.util.AlertLogUtil;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: CRM客服-重置客服任务数据定时任务
 * <AUTHOR>
 * @date 2023/10/9 18:36
 * @since JDK 1.8
 */
@Slf4j
@Component
public class AgentResetTaskJob extends AbstractBatchMessageJob{

    /**
     * 调度消息队列
     */
    @Value("${sync.TOPIC_AGENT_RESET}")
    private String queue;

    @Autowired
    private AgentResetTaskService agentResetTaskService;
    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("AgentResetTaskJob process start");
        try {
            agentResetTaskService.agentTaskReset(getContent(message));
        } catch (Exception e){
            AlertLogUtil.alert("AgentResetTaskJob", Throwables.getStackTraceAsString(e));
            log.error(e.getMessage(), e);
        }
        log.info("AgentResetTaskJob process end");
    }

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }
}