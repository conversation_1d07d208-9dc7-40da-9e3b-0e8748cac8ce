/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.howbuy.cs.base.Constants;
import com.howbuy.cs.task.buss.ImportFundVolFileTaskBuss;
import com.howbuy.cs.task.model.FundVolFileVo;
import com.howbuy.cs.task.util.AlertLogUtil;
import com.howbuy.dfile.HFileService;
import com.howbuy.dfile.internal.dto.FileInfo;
import com.howbuy.message.SimpleMessage;
import crm.howbuy.base.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/22 14:17
 * @since JDK 1.8
 */
@Slf4j
@Service("importFundVolFileTaskJob")
public class ImportFundVolFileTaskJob  extends AbstractBatchMessageJob {

    @Value("${sync.TOPIC_IMPORT_FUND_VOL_FILE}")
    private String queue;

    @Autowired
    private ImportFundVolFileTaskBuss importFundVolFileTaskBuss;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("ImportFundVolFileTaskJob process start");
        try {

            String tradeDt = getTradeDt(message);
            List<FundVolFileVo> fileVoList = getAllFileInfo(tradeDt);
            if (CollectionUtils.isEmpty(fileVoList)) {
                log.info("fileVoList is empty,tradeDt:{}", tradeDt);
                return;
            }
            importFundVolFileTaskBuss.importFundVolFile(tradeDt, fileVoList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            AlertLogUtil.alert("ImportFundVolFileTaskJob", Throwables.getStackTraceAsString(e));
        }
        log.info("ImportFundVolFileTaskJob process end");
    }


    /**
     * @description: 获取消息中的日期参数
     * @param message
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2024/11/25 11:10
     * @since JDK 1.8
     */
    private String getTradeDt(SimpleMessage message) {
        String content = getContent(message);
        if (StringUtils.isEmpty(content)) {
            return DateUtil.getDateYYYYMMDD();
        }

        JSONObject jsonObject = JSONObject.parseObject(content);
        String tradeDt = jsonObject.getString("tradeDt");
        if (StringUtils.isEmpty(tradeDt)) {
            return DateUtil.getDateYYYYMMDD();
        }
        return tradeDt;
    }


    /**
     * @description: 获取所有文件信息
     * @param tradeDt 交易日期
     * @return java.util.List<com.howbuy.cs.task.model.FundVolFileVo>
     * @author: hongdong.xie
     * @date: 2024/11/26 19:18
     * @since JDK 1.8
     */
    private List<FundVolFileVo> getAllFileInfo(String tradeDt) throws Exception {
        List<FileInfo> fileInfoList =  HFileService.getInstance().listFile(Constants.FUND_VOL_CHECK_STORECONFIG, tradeDt);
        List<FundVolFileVo> fileVoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(fileInfoList)) {
            log.info("fileInfoList is empty,tradeDt:{}", tradeDt);
            return fileVoList;
        }
        fileInfoList.forEach(fileInfo -> {
            log.info("fileInfo:{}", JSON.toJSONString(fileInfo));
            // 第一层只取文件夹
            if (Boolean.FALSE.equals(fileInfo.getDirectory())) {
                return;
            }

            String taCode = fileInfo.getDisplayName();
            try {
                List<FileInfo> fileInfoList1 = HFileService.getInstance().listFile(Constants.FUND_VOL_CHECK_STORECONFIG, tradeDt + "/" + taCode);
                if (CollectionUtils.isEmpty(fileInfoList1)) {
                    log.info("fileInfoList1 is empty,tacode:{}", taCode);
                    return;
                }
                fileInfoList1.forEach(fileInfo1 -> {
                    FundVolFileVo fileVo = getFundVolFileVo(fileInfo1, taCode);
                    if (fileVo != null) {
                        fileVoList.add(fileVo);
                    }
                });
            } catch (Exception e) {
                log.error("文件夹处理失败：{}", taCode);
                log.error("", e);
            }

        });
        log.info("========fileVoList:{}", JSON.toJSONString(fileVoList));
        return fileVoList;
    }

    /**
     * @description: 获取基金文件信息
     * @param fileInfo1 文件信息
     * @param taCode TA代码
     * @return com.howbuy.cs.task.model.FundVolFileVo
     * @author: hongdong.xie
     * @date: 2024/11/26 19:02
     * @since JDK 1.8
     */
    private FundVolFileVo getFundVolFileVo(FileInfo fileInfo1, String taCode) {
        log.info("getFundVolFileVo,fileInfo1:{}", JSON.toJSONString(fileInfo1));
        String fileName = fileInfo1.getDisplayName();
        try {
            // 第二层只取文件
            if (Boolean.TRUE.equals(fileInfo1.getDirectory())) {
                return null;
            }
            // 文件结尾非.txt的不处理
            if (!fileName.endsWith(".txt")) {
                return null;
            }
            String fundCode = getFundCode(fileName);
            FundVolFileVo fileVo = new FundVolFileVo();
            fileVo.setFundCode(fundCode);
            fileVo.setFileName(fileName);
            fileVo.setTaCode(taCode);
            return fileVo;
        } catch (Exception e) {
            log.error("文件处理失败，fileName：{}", fileName);
            log.error("", e);
        }

        return null;
    }

    /**
     * @description: 获取基金代码
     * @param fileName 文件名
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2024/11/26 19:06
     * @since JDK 1.8
     */
    public String getFundCode(String fileName) {
        String regex = ".*_(\\w+)\\.txt";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
        java.util.regex.Matcher matcher = pattern.matcher(fileName);
        if (matcher.matches()) {
            return matcher.group(1);
        }
        return null;
    }
}