/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.job;

import com.google.common.base.Throwables;
import com.howbuy.cs.task.service.AgentDataCollectTaskService;
import com.howbuy.cs.task.util.AlertLogUtil;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: CRM客服-收集客服任务数据
 * <AUTHOR>
 * @date 2023/10/18 15:10
 * @since JDK 1.8
 */
@Slf4j
@Component
public class AgentDataCollectTaskJob extends AbstractBatchMessageJob {

    @Value("${sync.TOPIC_CS_TASK_AGENT_DATA_COLLECT}")
    private String queue;

    @Autowired
    private AgentDataCollectTaskService agentDataCollectTaskService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("AgentDataCollectTaskJob process start");
        try {
            agentDataCollectTaskService.collectBasicData(getContent(message));
        }catch (Exception e){
            AlertLogUtil.alert("AgentDataCollectTaskJob", Throwables.getStackTraceAsString(e));
            log.error(e.getMessage(), e);
        }
        log.info("AgentDataCollectTaskJob process end");
    }
}