package com.howbuy.cs.job;

import com.google.common.base.Throwables;
import com.howbuy.cs.task.service.AgentDistributeTaskService;
import com.howbuy.cs.task.util.AlertLogUtil;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: CRM客服-座席分配任务
 * @date 2023/7/18 10:10
 * @since JDK 1.8
 */
@Slf4j
@Component
public class AgentDistributeTaskJob extends AbstractBatchMessageJob {
    /**
     * 调度消息队列
     */
    @Value("${sync.TOPIC_CS_TASK_AGENT_DISTRIBUTE}")
    private String queue;

    @Autowired
    private AgentDistributeTaskService agentDistributeTaskService;

    /**
     * @return java.lang.String 调度消息队列名称
     * @description: 获取调度消息队列名
     * @author: hongdong.xie
     * @date: 2023/3/8 17:09
     * @since JDK 1.8
     */
    @Override
    protected String getQuartMessageChannel() {
        return this.queue;
    }

    /**
     * @param message 消息
     * @description:具体执行的回调方法
     * @author: hongdong.xie
     * @date: 2023/3/8 17:08
     * @since JDK 1.8
     */
    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("AgentDistributeTaskJob process start");
        try {
            agentDistributeTaskService.agentTaskDistribute(null);
        }catch (Exception e){
            AlertLogUtil.alert("AgentDistributeTaskJob", Throwables.getStackTraceAsString(e));
            log.error("", e);
        }
        log.info("AgentDistributeTaskJob process end");
    }
}
