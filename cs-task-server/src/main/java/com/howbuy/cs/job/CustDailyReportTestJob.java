/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.job;

import com.google.common.base.Throwables;
import com.howbuy.cs.task.service.CustDailyReportTestService;
import com.howbuy.cs.task.util.AlertLogUtil;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: 客户每日报告发送任务
 *
 *
 * !!!! TEST：用于产品测试、验收
 *
 * <AUTHOR>
 * @date 2025-06-16 13:25:20
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CustDailyReportTestJob extends AbstractBatchMessageJob {

    /**
     * 调度消息队列
     */
    @Value("${sync.TOPIC_CUST_DAILY_REPORT_TEST}")
    private String queue;

    @Autowired
    private CustDailyReportTestService custDailyReportTestService;

    /**
     * @description: 获取调度消息队列名
     * @return java.lang.String 调度消息队列名称
     * @author: hongdong.xie
     * @date: 2025-06-16 13:25:20
     * @since JDK 1.8
     */
    @Override
    protected String getQuartMessageChannel() {
        return this.queue;
    }

    /**
     * @description: 具体执行的回调方法
     * @param message 消息
     *
     *   需求文档地址：http://dms.intelnal.howbuy.com/pages/viewpage.action?pageId=94507089
     *
     * @author: hongdong.xie
     * @date: 2025-06-16 13:25:20
     * @since JDK 1.8
     */
    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("CustDailyReportTestJob process start");
        try {
            // 客户每日报告发送任务
            // !!!! TEST：用于产品测试、验收
            // 正式调度：CustDailyReportService
            // ！！！！ 给客户推送报告那一步，仅日志汇总发送情况，并不真的发送
            // ！！！！ 给入参中指定的 hboneNoList 进行推送（hboneNoList 为产品人员指定的验收账号，用于测试）
            custDailyReportTestService.sendDailyReport(getContent(message));
        } catch (Exception e) {
            AlertLogUtil.alert("CustDailyReportTestJob", Throwables.getStackTraceAsString(e));
            log.error("执行CustDailyReportTestJob出现异常！", e);
        }
        log.info("CustDailyReportTestJob process end");
    }
} 