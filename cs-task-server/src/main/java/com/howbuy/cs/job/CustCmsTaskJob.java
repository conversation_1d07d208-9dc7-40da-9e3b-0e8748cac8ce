/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.job;

import com.google.common.base.Throwables;
import com.howbuy.cs.task.service.CustCmsTaskService;
import com.howbuy.cs.task.util.AlertLogUtil;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: CRM客服-来源配置更新通知定时任务
 * <AUTHOR>
 * @date 2023/10/9 18:34
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CustCmsTaskJob extends AbstractBatchMessageJob{

    /**
     * 调度消息队列
     */
    @Value("${sync.TOPIC_CUST_CMS_TASK}")
    private String queue;

    @Autowired
    private CustCmsTaskService custCmsTaskService;

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("CustCmsTaskJob process start");
        try {
            custCmsTaskService.sourceAbnormalCustHandle(getContent(message));
        } catch (Exception e){
            AlertLogUtil.alert("CustCmsTaskJob", Throwables.getStackTraceAsString(e));
            log.error(e.getMessage(), e);
        }
        log.info("CustCmsTaskJob process end");
    }

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }
}