package com.howbuy.cs.job;

import com.howbuy.cs.task.service.StockSplitAutoConfigService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * @description: 存量分成自动配置定时任务
 * @author: hongdong.xie
 * @date: 2024/1/17
 */
@Slf4j
@Component
public class StockSplitAutoConfigJob extends AbstractBatchMessageJob {

    @Value("${sync.TOPIC_STOCK_SPLIT_AUTO_CONFIG}")
    private String queue;

    @Autowired
    private StockSplitAutoConfigService stockSplitAutoConfigService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("存量分成自动配置定时任务开始执行...");
        try {
            stockSplitAutoConfigService.generateStockSplitConfig();
        } catch (Exception e) {
            log.error("存量分成自动配置定时任务执行异常", e);
        }
        log.info("存量分成自动配置定时任务执行完成");
    }
//    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

//    @Override
//    public void afterPropertiesSet() throws Exception {
//        scheduler.scheduleAtFixedRate(
//            () -> stockSplitAutoConfigService.generateStockSplitConfig(),
//            2,
//            30,
//            TimeUnit.SECONDS
//        );
//    }
}