/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.job;

import com.google.common.base.Throwables;
import com.howbuy.cs.task.buss.GenerateConsultantChangeorgRecTaskBuss;
import com.howbuy.cs.task.util.AlertLogUtil;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * @description:  生成投顾变更记录任务
 * <AUTHOR>
 * @date 2024/11/25 17:13
 * @since JDK 1.8
 */
@Slf4j
@Service
public class GenerateConsultantChangeorgRecTaskJob extends AbstractBatchMessageJob {

    @Autowired
    private GenerateConsultantChangeorgRecTaskBuss generateConsultantChangeorgRecTaskBuss;

    @Value("${sync.GENERATE_CONSULTANT_CHANGEORG_REC}")
    private String queue;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("GenerateConsultantChangeorgRecTaskJob process start");
        try {
            generateConsultantChangeorgRecTaskBuss.generate();
        } catch (Exception e) {
            AlertLogUtil.alert("GenerateConsultantChangeorgRecTaskJob", Throwables.getStackTraceAsString(e));
            log.error(e.getMessage(), e);
        }
        log.info("GenerateConsultantChangeorgRecTaskJob process end");
    }
}