/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.job;

import com.google.common.base.Throwables;
import com.howbuy.cs.task.service.DelBookingCustGarbageTaskService;
import com.howbuy.cs.task.util.AlertLogUtil;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: 清除cms预约垃圾数据
 * @date 2023/11/15 11:03
 * @since JDK 1.8
 */
@Slf4j
@Component
public class DelBookingCustGarbageTaskJob extends AbstractBatchMessageJob {

    /**
     * 调度消息队列
     */
    @Value("${sync.TOPIC_DEL_BOOKING_CUST_GARBAGE_TASK}")
    private String queue;

    @Autowired
    private DelBookingCustGarbageTaskService delBookingCustGarbageTaskService;

    /**
     * @description: 改造存储过程[PRO_TASK_DELGARBAGECMSCUST]，该存储过程原属于howbuy-etl-server项目
     * @param message 消息
     * @since JDK 1.8
     */
    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("DelBookingCustGarbageTaskJob process start");
        try {
            delBookingCustGarbageTaskService.delBookingCustGarbage(getContent(message));
        } catch (Exception e) {
            AlertLogUtil.alert("DelBookingCustGarbageTaskJob", Throwables.getStackTraceAsString(e));
            log.error(e.getMessage(), e);
        }
        log.info("DelBookingCustGarbageTaskJob process end");
    }

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }
}