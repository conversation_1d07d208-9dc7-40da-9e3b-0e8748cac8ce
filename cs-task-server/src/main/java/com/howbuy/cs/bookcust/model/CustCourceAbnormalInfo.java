package com.howbuy.cs.bookcust.model;

import java.io.Serializable;

public class CustCourceAbnormalInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	private String consCustNo;// 投顾客户号
	private String custName;// 客户名称
	private String bookingType; // 预约类型(1:私募预约 ;2:合作预约)
	private String activityType; // 活动类型
	private String wirelessChannel; // 无线渠道
	private String qualifiedStatus; // 合规状态
	private String newSourceNo; // 新来源编码
	private String optFlag; // 操作标识

	public String getConsCustNo() {
		return consCustNo;
	}

	public void setConsCustNo(String consCustNo) {
		this.consCustNo = consCustNo;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getBookingType() {
		return bookingType;
	}

	public void setBookingType(String bookingType) {
		this.bookingType = bookingType;
	}

	public String getActivityType() {
		return activityType;
	}

	public void setActivityType(String activityType) {
		this.activityType = activityType;
	}

	public String getWirelessChannel() {
		return wirelessChannel;
	}

	public void setWirelessChannel(String wirelessChannel) {
		this.wirelessChannel = wirelessChannel;
	}

	public String getQualifiedStatus() {
		return qualifiedStatus;
	}

	public void setQualifiedStatus(String qualifiedStatus) {
		this.qualifiedStatus = qualifiedStatus;
	}

	public String getNewSourceNo() {
		return newSourceNo;
	}

	public void setNewSourceNo(String newSourceNo) {
		this.newSourceNo = newSourceNo;
	}

	public String getOptFlag() {
		return optFlag;
	}

	public void setOptFlag(String optFlag) {
		this.optFlag = optFlag;
	}
	
}