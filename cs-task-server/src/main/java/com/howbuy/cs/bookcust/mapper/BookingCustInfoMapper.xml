<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.cs.bookcust.dao.BookingCustInfoDao">
	<resultMap id="BaseResultMap" type="com.howbuy.cs.bookcust.model.BookingCustInfo">
	        <result column="ID" property="id" jdbcType="INTEGER"/>
	        <result column="BOOKINGDT" property="bookingDt" jdbcType="VARCHAR"/>
	        <result column="CUSTNAME" property="custName" jdbcType="INTEGER"/>
	        <result column="BOOKINGTYPE" property="bookingType" jdbcType="VARCHAR"/>
	        <result column="ACTIVITYTYPE" property="activityType" jdbcType="VARCHAR"/>
	        <result column="CHANNELCODE" property="channelCode" jdbcType="VARCHAR"/>
	        <result column="BOOKINGCONTENT" property="bookingContent" jdbcType="VARCHAR"/>
	        <result column="SOURCEADDR" property="sourceAddr" jdbcType="VARCHAR"/>
	        <result column="HANDLESTAT" property="handleStat" jdbcType="VARCHAR"/>
	        <result column="CONSCUSTNO" property="conscustNo" jdbcType="VARCHAR"/>
	        <result column="RECSTAT" property="recStat" jdbcType="VARCHAR"/>
	        <result column="CREATOR" property="creator" jdbcType="VARCHAR"/>
	        <result column="MODIFIER" property="modifier" jdbcType="VARCHAR"/>
	        <result column="CREDT" property="credt" jdbcType="VARCHAR"/>
	        <result column="MODDT" property="moddt" jdbcType="VARCHAR"/>
	        <result column="SYNC_DATE" property="syncDate" jdbcType="DATE"/>
	        <result column="VALIDATEFLAG" property="validateFlag" jdbcType="INTEGER"/>
	        <result column="FRAUDFLAG" property="fraudFlag" jdbcType="INTEGER"/>
	        <result column="FRAUDINFO" property="fraudInfo" jdbcType="VARCHAR"/>
	        <result column="READFLAG" property="readFlag" jdbcType="INTEGER"/>
	        <result column="BOOKINGSERIALNO" property="bookingSerialNo" jdbcType="VARCHAR"/>
	        <result column="BOOKINGDETAILDT" property="bookingDetailDt" jdbcType="VARCHAR"/>
	        <result column="WIRELESS_CHANNEL" property="wirelessChannel" jdbcType="VARCHAR"/>
	        <result column="QUALIFIED_STATUS" property="qualifiedStatus" jdbcType="VARCHAR"/>
	        <result column="STARTHOUR" property="startHour" jdbcType="VARCHAR"/>
	        <result column="ENDHOUR" property="endHour" jdbcType="VARCHAR"/>
    </resultMap>
    
    <insert id="insertCmBookingCust" parameterType="com.howbuy.cs.bookcust.model.BookingCustInfo">
        INSERT INTO CM_BOOKINGCUST (
	     	<trim suffix="" suffixOverrides=",">
		     	ID,
		     	<if test="bookingDt != null"> BOOKINGDT, </if>
		     	<if test="custName != null"> CUSTNAME, </if>
		     	<if test="bookingType != null"> BOOKINGTYPE, </if>
		     	<if test="activityType != null"> ACTIVITYTYPE, </if>
		     	<if test="channelCode != null"> CHANNELCODE, </if>
		     	<if test="bookingContent != null"> BOOKINGCONTENT, </if>
		     	<if test="sourceAddr != null"> SOURCEADDR, </if>
				<if test="sourceSys != null"> SOURCE_SYS, </if>
		     	HANDLESTAT,
		     	<if test="conscustNo != null"> CONSCUSTNO, </if>
		     	<if test="hboneNo != null"> HBONE_NO, </if>
		     	RECSTAT,
		     	CREATOR,
		     	<if test="modifier != null"> MODIFIER, </if>
		     	CREDT,
		     	<if test="moddt != null"> MODDT, </if>
		     	SYNC_DATE,
		     	<if test="validateFlag != null"> VALIDATEFLAG, </if>
		     	<if test="fraudFlag != null"> FRAUDFLAG, </if>
		     	<if test="fraudInfo != null"> FRAUDINFO, </if>
		     	READFLAG,
		     	<if test="bookingSerialNo != null"> BOOKINGSERIALNO, </if>
		     	<if test="bookingDetailDt != null"> BOOKINGDETAILDT, </if>
		     	<if test="wirelessChannel != null"> WIRELESS_CHANNEL, </if>
		     	<if test="qualifiedStatus != null"> QUALIFIED_STATUS, </if>
		     	<if test="startHour != null"> STARTHOUR, </if>
		     	<if test="endHour != null"> ENDHOUR, </if>
		     	<if test="mobileMask != null"> MOBILE_MASK, </if>
		     	<if test="mobileDigest != null"> MOBILE_DIGEST, </if>
		     	<if test="mobileCipher != null"> MOBILE_CIPHER, </if>
		     	<if test="emailMask != null"> EMAIL_MASK, </if>
		     	<if test="emailDigest != null"> EMAIL_DIGEST, </if>
		     	<if test="emailCipher != null"> EMAIL_CIPHER, </if>
				<if test="disCode != null and disCode != ''">DISCODE,</if>
				<if test="mobileAreaCode != null and mobileAreaCode != ''">MOBILE_AREA_CODE,</if>
	     	</trim>
          	) values (
         	<trim suffix="" suffixOverrides=",">
	         	SEQ_CM_BOOKING_CUST.NEXTVAL,
		     	<if test="bookingDt != null"> #{bookingDt, jdbcType = VARCHAR}, </if>
		     	<if test="custName != null"> #{custName, jdbcType = VARCHAR}, </if>
		     	<if test="bookingType != null"> #{bookingType, jdbcType = VARCHAR}, </if>
		     	<if test="activityType != null"> #{activityType, jdbcType = VARCHAR}, </if>
		     	<if test="channelCode != null"> #{channelCode, jdbcType = VARCHAR}, </if>
		     	<if test="bookingContent != null"> #{bookingContent, jdbcType = VARCHAR}, </if>
		     	<if test="sourceAddr != null"> #{sourceAddr, jdbcType = VARCHAR}, </if>
				<if test="sourceSys != null"> #{sourceSys, jdbcType = VARCHAR}, </if>
		     	'0',
		     	<if test="conscustNo != null"> #{conscustNo, jdbcType = VARCHAR}, </if>
		     	<if test="hboneNo != null"> #{hboneNo, jdbcType = VARCHAR}, </if>
		     	'0',
		     	'SYS',
		     	<if test="modifier != null"> #{modifier, jdbcType = VARCHAR}, </if>
		     	 to_char(sysdate,'yyyymmdd'),
		     	<if test="moddt != null"> #{moddt, jdbcType = VARCHAR}, </if>
		     	sysdate,
		     	<if test="validateFlag != null"> #{validateFlag, jdbcType = INTEGER}, </if>
		     	<if test="fraudFlag != null"> #{fraudFlag, jdbcType = INTEGER}, </if>
		     	<if test="fraudInfo != null"> #{fraudInfo, jdbcType = VARCHAR}, </if>
		     	0,
		     	<if test="bookingSerialNo != null"> #{bookingSerialNo, jdbcType = VARCHAR}, </if>
		     	<if test="bookingDetailDt != null"> #{bookingDetailDt, jdbcType = VARCHAR}, </if>
		     	<if test="wirelessChannel != null"> #{wirelessChannel, jdbcType = VARCHAR}, </if>
		     	<if test="qualifiedStatus != null"> #{qualifiedStatus, jdbcType = VARCHAR}, </if>
		     	<if test="startHour != null"> #{startHour, jdbcType = VARCHAR}, </if>
		     	<if test="endHour != null"> #{endHour, jdbcType = VARCHAR}, </if>
		     	<if test="mobileMask != null"> #{mobileMask, jdbcType = VARCHAR}, </if>
		     	<if test="mobileDigest != null"> #{mobileDigest, jdbcType = VARCHAR}, </if>
		     	<if test="mobileCipher != null"> #{mobileCipher, jdbcType = VARCHAR}, </if>
		     	<if test="emailMask != null"> #{emailMask, jdbcType = VARCHAR}, </if>
		     	<if test="emailDigest != null"> #{emailDigest, jdbcType = VARCHAR}, </if>
		     	<if test="emailCipher != null"> #{emailCipher, jdbcType = VARCHAR}, </if>
				<if test="disCode != null and disCode != ''"> #{disCode,jdbcType=VARCHAR},</if>
				<if test="mobileAreaCode != null and mobileAreaCode != ''"> #{mobileAreaCode,jdbcType=VARCHAR},</if>
         	</trim>	
         	)      
    </insert>
    
    <select id="queryConscustInfo" parameterType="Map" resultType="int" useCache="false">
        select count(1)
		  from cm_conscust a
		  left join cm_conscust_cipher b 
		  on a.conscustno = b.conscustno
		 where 1 = 1
		   and a.conscuststatus = '0'
		   and a.mobile_digest = #{mobileDigest, jdbcType = VARCHAR}
    </select>
    
    <select id="queryListCustconstantInfo" parameterType="Map" resultType="com.howbuy.cs.bookcust.model.CustconstantInfo" useCache="false">
        select a.conscustno, c.consname, c.mobile
		  from cm_conscust a
		  join cm_custconstant b
		    on b.custno = a.conscustno
		  join cm_consultant c
		    on c.conscode = b.conscode
		   and c.isvirtual != 1
		 left join cm_conscust_cipher d 
		  on a.conscustno = d.conscustno
		 where 1 = 1
		   and a.conscuststatus = '0'
		   and a.hbone_no is not null
		   and a.mobile_digest = #{mobileDigest, jdbcType = VARCHAR}
		   and c.mobile is not null
    </select>
    
    <select id="queryListCustCourceAbnormal" parameterType="Map" resultType="com.howbuy.cs.bookcust.model.CustCourceAbnormalInfo" useCache="false">
        select t1.conscustno as consCustNo,
		       t1.custname as custName,
		       t3.bookingtype as bookingType,
		       t3.activitytype as activityType,
		       t3.wireless_channel as wirelessChannel,
		       t3.qualified_status as qualifiedStatus,
		       t4.newsourceno as newSourceNo
		  from cm_conscust t1
		  left join cs_callout_waitdistribute t2
		    on t2.conscustno = t1.conscustno
		  left join cm_bookingcust t3
		    on t3.id = t2.taskid
		  left join cm_bookingtypesource t4
		    on t4.bookingtype = t3.bookingtype
		   and t4.activitytype = t3.activitytype
		   and t4.wireless_channel = t3.wireless_channel
		   and t4.qualified_status = t3.qualified_status
		   and t4.recstat = '0'
		 where t1.newsourceno = 'PO1807A02'   
		   and t1.conscustno in (select t2.conscustno from cm_custsource_abnormal t2)
		   and t1.conscuststatus = '0'
		   <if test="optFlag != null">
			   <if test="optFlag=='repairSource'">
			   	   and t4.newsourceno is not null
			   </if>
			   <if test="optFlag=='sendEmail'">
			   	   and t4.newsourceno is null
				   and rownum <![CDATA[<=]]> 30
			   </if>
		   </if>
    </select>

	<delete id="delBookingCustGarbage">
		delete from CM_BOOKINGCUST t where t.CUSTNAME like '%mobile=%'
	</delete>
</mapper>