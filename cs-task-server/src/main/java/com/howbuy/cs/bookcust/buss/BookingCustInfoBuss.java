package com.howbuy.cs.bookcust.buss;


import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.howbuy.cs.bookcust.dao.BookingCustInfoDao;
import com.howbuy.cs.bookcust.model.BookingCustInfo;
import com.howbuy.cs.bookcust.model.CustCourceAbnormalInfo;
import com.howbuy.cs.bookcust.model.CustconstantInfo;

@Component
@Service("bookingCustInfoBuss")
public class BookingCustInfoBuss {

    @Autowired
    private BookingCustInfoDao bookingCustInfoDao;

    /**
     * 功能描述：同步预约数据接口
     */
    @Transactional(rollbackFor = { Exception.class })
    public void insertBookingCustInfo(BookingCustInfo cmBookingCust) {
    	bookingCustInfoDao.insertCmBookingCust(cmBookingCust);
    }
    
    /**
     * 功能描述：根据客户手机号查询客户信息（判断客户是否存在多个）
     */
    public int queryConscustInfo(String custMobileDigest) {
    	return bookingCustInfoDao.queryConscustInfo(custMobileDigest);
    }
    
    /**
     * 功能描述：根据客户手机号密文查询投顾信息
     */
    public List<CustconstantInfo> queryListCustconstantInfo(String mobileDigest) {
    	return bookingCustInfoDao.queryListCustconstantInfo(mobileDigest);
    }
    
    /**
     * 功能描述：查询CMS异常来源客户方法
     */
    public List<CustCourceAbnormalInfo> queryListCustCourceAbnormal(Map<String, String> param) {
    	return bookingCustInfoDao.queryListCustCourceAbnormal(param);
    }

}
