package com.howbuy.cs.bookcust.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.cc.message.SendMsgResult;
import com.howbuy.cc.message.SendMsgService;
import com.howbuy.cs.base.Constants;
import com.howbuy.cs.base.model.BaseConstantEnum;
import com.howbuy.cs.bookcust.buss.BookingCustInfoBuss;
import com.howbuy.cs.bookcust.dto.BookingCustInfoDomain;
import com.howbuy.cs.bookcust.enums.BookingCustSourceSysEnum;
import com.howbuy.cs.bookcust.model.BookingCustInfo;
import com.howbuy.cs.bookcust.model.CustconstantInfo;
import com.howbuy.cs.bookcust.request.BookingCustInfoRequest;
import com.howbuy.cs.bookcust.response.BookingCustInfoResponse;
import com.howbuy.cs.bookcust.enums.CmsDisCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service("bookingCustInfoService")
public class BookingCustInfoServiceImpl implements BookingCustInfoService {
    private static final Logger log = LoggerFactory.getLogger(BookingCustInfoServiceImpl.class);

    @Autowired
    private BookingCustInfoBuss bookingCustInfoBuss;

    @Autowired
    private SendMsgService sendMessageDubboService;

    @Autowired
    private EncryptSingleFacade encryptSingleFacade;

    private final static String OLD_TIME = "9:00";

    private final static String NEW_TIME = "09:00";

    @Override
    public BookingCustInfoResponse insertBookingCustInfo(BookingCustInfoRequest request) {
        log.info("insertBookingCustInfo1 request is: {} ", JSONObject.toJSONString(request));

        BookingCustInfoResponse response = new BookingCustInfoResponse();
        response.processedFail();
        try {
            // 获取传入对象
            BookingCustInfoDomain bookingCustInfoDomain = request.getBookingCustInfoDomain();
            log.info("bookingCustInfoDomain,{}", JSONObject.toJSONString(bookingCustInfoDomain));
            // 打印传入数据
            log.info("insertBookingCustInfo request is: {} ", JSONObject.toJSONString(bookingCustInfoDomain));

            if (this.validate(response, bookingCustInfoDomain)) {
                BookingCustInfo bookingCust = new BookingCustInfo();
                if (OLD_TIME.equals(bookingCustInfoDomain.getStartHour())) {
                    bookingCustInfoDomain.setStartHour(NEW_TIME);
                }
                bookingCust.setSourceSys(BookingCustSourceSysEnum.CMS.getCode());
                BeanUtils.copyProperties(bookingCustInfoDomain, bookingCust);

                // 执行创建预约记录操作
                if (insertBookingCustInfo(bookingCust)) {
                    response.success();
                    // 香港分销渠道推送消息
                    if (CmsDisCodeEnum.HK_CHANNEL.getCode().equals(bookingCust.getDisCode())) {
                        sendHkMsg(bookingCust);
                    } else {
                        sendMsg(bookingCust);
                    }
                } else {
                    response.putBaseResult(BaseConstantEnum.SYS_ERROR, "新增异常！");
                }
            }

        } catch (Exception e) {
            log.error(e.getMessage());
        }

        return response;
    }

    /**
     * 香港渠道推送消息
     *
     * @param bookingCust 预约客户信息
     */
    private void sendHkMsg(BookingCustInfo bookingCust) {
        try {
            if (null == bookingCust || !CmsDisCodeEnum.HK_CHANNEL.getCode().equals(bookingCust.getDisCode())) {
                log.info("BookingCustInfoServiceImpl sendHkMsg params is not meet!");
                return;
            }

            String custName = bookingCust.getCustName();
            String mobile = bookingCust.getMobile();
            String mobileAreaCode = bookingCust.getMobileAreaCode();
            String email = bookingCust.getEmail();
            Map<String, String> sendMap = new HashMap<>();
            sendMap.put("custName", custName);
            log.info("BookingCustInfoServiceImpl sendHkMsg params=[{},{},{},{}]", custName, mobile, mobileAreaCode, email);
            if (StringUtils.isNotBlank(mobile) && Constants.DEFAULT_MOBILE_AREA_CODE.equals(mobileAreaCode)) {
                // 手机号存在 且 手机区号是+86 时根据手机号推送消息
                SendMsgResult sendMsgResult = sendMessageDubboService.sendTemplateMsgByPhone(Constants.HK_MSG_TEMPLATE_ID_60514, JSON.toJSONString(sendMap), mobile, null, null);
                log.info("BookingCustInfoServiceImpl sendHkMsg sendTemplateMsgByPhone result={}", null != sendMsgResult ? JSON.toJSONString(sendMsgResult) : null);
                return;
            }

            if (StringUtils.isNotBlank(email)) {
                // 邮箱存在时根据邮箱推送消息
                SendMsgResult sendMsgResult = sendMessageDubboService.sendTemplateMsgByEmail(Constants.HK_MSG_TEMPLATE_ID_60514, JSON.toJSONString(sendMap), email, null, null, null);
                log.info("BookingCustInfoServiceImpl sendHkMsg sendTemplateMsgByEmail result={}", null != sendMsgResult ? JSON.toJSONString(sendMsgResult) : null);
                return;
            }
            log.info("BookingCustInfoServiceImpl sendHkMsg end!");
        } catch (Exception e) {
            log.error("BookingCustInfoServiceImpl sendHkMsg error={}", e.getMessage(), e);
        }
    }

    /**
     * 给客户和投顾发送短信
     *
     * @param bookingCust
     */
    public void sendMsg(BookingCustInfo bookingCust) {
        // 对手机号码进行校验，并进行短信发送
        Pattern p = Pattern.compile("^[1][1-9][0-9]{9}$");
        Matcher m = p.matcher(bookingCust.getMobile());
        if (!m.matches()) {
            return;
        }

        // 获取客户姓名、手机号和预约内容
        String custName = bookingCust.getCustName();
        String custMobile = bookingCust.getMobile();
        String bookingContent = bookingCust.getBookingContent();

        // 客户手机号信息校验
        if (StringUtils.isNotBlank(custMobile)) {
            String custMobileDigest = DigestUtil.digest(custMobile.trim());
            int count = bookingCustInfoBuss.queryConscustInfo(custMobileDigest);
            Map<String, String> sendMap = new HashMap<String, String>();
            SendMsgResult sendCustMsgResult = null;
            if (count == 0) {
                // 给客户发送短信
                sendMap.put("custMobile", custMobile);
                // 首次预约客户消息模板：【好买财富】尊敬的客户，您的信息已成功提交，即刻开启好买尊贵VIP会员体验！客服将和您进行确认，来电显示为021-2061XXXX。关注微信公众号“好买臻财VIP”或者下载“掌上基金APP”，尽享更多资讯与特权！
                sendCustMsgResult = sendMessageDubboService.sendTemplateMsgByPhone("60062", JSON.toJSONString(sendMap), custMobile, null, null);
            } else if (count == 1) {// 非首次预约客户消息模板
                // 获取客户所属投顾信息
                List<CustconstantInfo> listInfoDomain = bookingCustInfoBuss.queryListCustconstantInfo(custMobileDigest);
                if (listInfoDomain != null && listInfoDomain.size() == 1) {
                    String consCustNo = listInfoDomain.get(0).getConsCustNo();
                    String consName = listInfoDomain.get(0).getConsName();
                    String consMobile = listInfoDomain.get(0).getMobile();

                    // 给客户发送短信
                    sendMap.clear();
                    sendMap.put("custName", custName);
                    sendMap.put("consName", consName);
                    sendMap.put("custMobile", custMobile);
                    // 非首次预约客户消息模板：【好买财富】尊敬的XXX先生/女士，您好，感谢您在好买查询私募相关信息，您的专属投资顾问XXX将及时与您联系，为您提供专属服务，祝您投资愉快！
                    sendCustMsgResult = sendMessageDubboService.sendTemplateMsgByPhone("60063", JSON.toJSONString(sendMap), custMobile, null, null);

                    // 给客户所属投顾发送短信
                    if (StringUtils.isNotBlank(consMobile)) {
                        sendMap.clear();
                        sendMap.put("consName", consName);
                        sendMap.put("custName", custName);
                        sendMap.put("consCustNo", consCustNo);
                        sendMap.put("bookingContent", bookingContent);
                        sendMap.put("consMobile", consMobile);
                        // 3． 非首次预约投顾消息模板：【好买财富】尊敬的XXX投顾您好，您的客户XXX（投顾号XXX）已提交了产品预约，预约详情：XXX（产品名称），请您及时联系客户，谢谢！
                        SendMsgResult sendConsMsgResult = sendMessageDubboService.sendTemplateMsgByPhone("60064", JSON.toJSONString(sendMap), consMobile, null, null);
                        log.info("sendConsMsgResult: {}", sendConsMsgResult.getMsg());
                    }

                }
            }

            if (sendCustMsgResult != null) {
                log.info("sendCustMsgResult: {} ", sendCustMsgResult.getMsg());
            }

        }

    }

    /**
     * 预约数据进数据库方法
     */
    public boolean insertBookingCustInfo(BookingCustInfo bookingCust) {
        boolean flag = true;
        try {
            String mobile = bookingCust.getMobile();
            String email = bookingCust.getEmail();
            if (StringUtils.isNotBlank(mobile)) {
                bookingCust.setMobileDigest(DigestUtil.digest(mobile.trim()));
                bookingCust.setMobileMask(MaskUtil.maskMobile(mobile.trim()));
                bookingCust.setMobileCipher(encryptSingleFacade.encrypt(mobile.trim()).getCodecText());
            }

            if (StringUtils.isNotBlank(email)) {
                bookingCust.setEmailDigest(DigestUtil.digest(email.trim().toLowerCase()));
                bookingCust.setEmailMask(MaskUtil.maskEmail(email.trim().toLowerCase()));
                bookingCust.setEmailCipher(encryptSingleFacade.encrypt(email.trim().toLowerCase()).getCodecText());
            }

            bookingCustInfoBuss.insertBookingCustInfo(bookingCust);
        } catch (Exception e) {
            log.error(e.getMessage());
            flag = false;
        }

        return flag;
    }

    /**
     * 验证参数
     */
    public boolean validate(BookingCustInfoResponse response,
                            BookingCustInfoDomain bookingCustInfoDomain) {
        // 校验传入数据==null
        if (bookingCustInfoDomain == null) {
            response.invalidReqParams("BookingCustInfoDomain对象为空！");
            return false;
        }
        // 校验传入数据==属性
        if (StringUtils.isBlank(bookingCustInfoDomain.getCustName())) {
            response.invalidReqParams("传入参数（客户名称）为空");
            return false;
        }

        if (StringUtils.isBlank(bookingCustInfoDomain.getMobile())) {
            response.invalidReqParams("传入参数（手机号码）为空");
            return false;
        }
        if (StringUtils.isBlank(bookingCustInfoDomain.getBookingType())) {
            response.invalidReqParams("传入参数（预约类型）为空");
            return false;
        }

        if (StringUtils.isBlank(bookingCustInfoDomain.getActivityType())) {
            response.invalidReqParams("传入参数（活动类型）为空");
            return false;
        }

        if (StringUtils.isBlank(bookingCustInfoDomain.getBookingContent())) {
            response.invalidReqParams("传入参数（预约内容）为空");
            return false;
        }

        if (StringUtils.isBlank(bookingCustInfoDomain.getBookingSerialNo())) {
            response.invalidReqParams("传入参数（流水序列号）为空");
            return false;
        }

        return true;
    }

}