package com.howbuy.cs.bookcust.model;

import java.io.Serializable;

public class BookingCustInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	private String id;				// 主键id
	private String bookingDt;		// 预约时间
	/** 一账通号 */
	private String hboneNo;
	private String custName;		// 姓名
	private String mobile;			// 手机
	private String email;			// 邮箱
	private String bookingType;		// 预约类型(1:私募预约 ;2:合作预约)
	private String activityType;	// 活动类型
	private String channelCode;		// 渠道(合作预约需求)
	private String bookingContent;	// 预约内容
	private String sourceAddr;		// 来源地址
	private String sourceSys;		// 来源系统：1-CMS;2-ACC
	private String handleStat;		// 处理状态（0：未处理 ，1：已处理）
	private String conscustNo;		// 投顾客户号
	private String recStat;			// 记录状态( 0：有效 ，1 ：删除 ；2：临时删除)
	private String creator;			// 创建人
	private String modifier;		// 修改人
	private String credt;			// 记录创建日期
	private String moddt;			// 记录修改日期
	private String syncDate;		// 数据同步日期
	private String validateFlag;	// 验证标识
	private String fraudFlag;		// 特殊标识
	private String fraudInfo;		// 特殊信息
	private String readFlag;		// 读取标识
	private String bookingSerialNo;	// 预约流水号
	private String bookingDetailDt;	// 预约详细时间
	private String wirelessChannel;	// 无线渠道（1:掌基、2:臻财、3:M站）
	private String qualifiedStatus;	// 合规状态（N：否 ，Y：是）
	private String startHour;		// 开始时间段（预约）
	private String endHour;			// 截止时间段（预约）
	private String mobileMask;      // 手机掩码
	private String mobileDigest;    // 手机摘要
	private String mobileCipher;    // 手机密文
	private String emailMask;       // 邮箱掩码
	private String emailDigest;     // 邮箱摘要
	private String emailCipher;     // 邮箱密文
	private String disCode; // 分销渠道

	private String mobileAreaCode; // 手机区号

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getBookingDt() {
		return bookingDt;
	}

	public void setBookingDt(String bookingDt) {
		this.bookingDt = bookingDt;
	}

	public String getHboneNo() {
		return hboneNo;
	}

	public void setHboneNo(String hboneNo) {
		this.hboneNo = hboneNo;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getBookingType() {
		return bookingType;
	}

	public void setBookingType(String bookingType) {
		this.bookingType = bookingType;
	}

	public String getActivityType() {
		return activityType;
	}

	public void setActivityType(String activityType) {
		this.activityType = activityType;
	}

	public String getChannelCode() {
		return channelCode;
	}

	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}

	public String getBookingContent() {
		return bookingContent;
	}

	public void setBookingContent(String bookingContent) {
		this.bookingContent = bookingContent;
	}

	public String getSourceAddr() {
		return sourceAddr;
	}

	public void setSourceAddr(String sourceAddr) {
		this.sourceAddr = sourceAddr;
	}

	public String getSourceSys() {
		return sourceSys;
	}

	public void setSourceSys(String sourceSys) {
		this.sourceSys = sourceSys;
	}

	public String getHandleStat() {
		return handleStat;
	}

	public void setHandleStat(String handleStat) {
		this.handleStat = handleStat;
	}

	public String getConscustNo() {
		return conscustNo;
	}

	public void setConscustNo(String conscustNo) {
		this.conscustNo = conscustNo;
	}

	public String getRecStat() {
		return recStat;
	}

	public void setRecStat(String recStat) {
		this.recStat = recStat;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public String getCredt() {
		return credt;
	}

	public void setCredt(String credt) {
		this.credt = credt;
	}

	public String getModdt() {
		return moddt;
	}

	public void setModdt(String moddt) {
		this.moddt = moddt;
	}

	public String getSyncDate() {
		return syncDate;
	}

	public void setSyncDate(String syncDate) {
		this.syncDate = syncDate;
	}

	public String getValidateFlag() {
		return validateFlag;
	}

	public void setValidateFlag(String validateFlag) {
		this.validateFlag = validateFlag;
	}

	public String getFraudFlag() {
		return fraudFlag;
	}

	public void setFraudFlag(String fraudFlag) {
		this.fraudFlag = fraudFlag;
	}

	public String getFraudInfo() {
		return fraudInfo;
	}

	public void setFraudInfo(String fraudInfo) {
		this.fraudInfo = fraudInfo;
	}

	public String getReadFlag() {
		return readFlag;
	}

	public void setReadFlag(String readFlag) {
		this.readFlag = readFlag;
	}

	public String getBookingSerialNo() {
		return bookingSerialNo;
	}

	public void setBookingSerialNo(String bookingSerialNo) {
		this.bookingSerialNo = bookingSerialNo;
	}

	public String getBookingDetailDt() {
		return bookingDetailDt;
	}

	public void setBookingDetailDt(String bookingDetailDt) {
		this.bookingDetailDt = bookingDetailDt;
	}

	public String getWirelessChannel() {
		return wirelessChannel;
	}

	public void setWirelessChannel(String wirelessChannel) {
		this.wirelessChannel = wirelessChannel;
	}

	public String getQualifiedStatus() {
		return qualifiedStatus;
	}

	public void setQualifiedStatus(String qualifiedStatus) {
		this.qualifiedStatus = qualifiedStatus;
	}

	public String getStartHour() {
		return startHour;
	}

	public void setStartHour(String startHour) {
		this.startHour = startHour;
	}

	public String getEndHour() {
		return endHour;
	}

	public void setEndHour(String endHour) {
		this.endHour = endHour;
	}

	public String getMobileMask() {
		return mobileMask;
	}

	public void setMobileMask(String mobileMask) {
		this.mobileMask = mobileMask;
	}

	public String getMobileDigest() {
		return mobileDigest;
	}

	public void setMobileDigest(String mobileDigest) {
		this.mobileDigest = mobileDigest;
	}

	public String getMobileCipher() {
		return mobileCipher;
	}

	public void setMobileCipher(String mobileCipher) {
		this.mobileCipher = mobileCipher;
	}

	public String getEmailMask() {
		return emailMask;
	}

	public void setEmailMask(String emailMask) {
		this.emailMask = emailMask;
	}

	public String getEmailDigest() {
		return emailDigest;
	}

	public void setEmailDigest(String emailDigest) {
		this.emailDigest = emailDigest;
	}

	public String getEmailCipher() {
		return emailCipher;
	}

	public void setEmailCipher(String emailCipher) {
		this.emailCipher = emailCipher;
	}

	public String getDisCode() {
		return disCode;
	}

	public void setDisCode(String disCode) {
		this.disCode = disCode;
	}

	public String getMobileAreaCode() {
		return mobileAreaCode;
	}

	public void setMobileAreaCode(String mobileAreaCode) {
		this.mobileAreaCode = mobileAreaCode;
	}
}
