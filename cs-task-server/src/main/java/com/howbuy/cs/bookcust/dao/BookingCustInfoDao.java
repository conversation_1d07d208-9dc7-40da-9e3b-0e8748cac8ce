package com.howbuy.cs.bookcust.dao;

import com.howbuy.cs.bookcust.model.BookingCustInfo;
import com.howbuy.cs.bookcust.model.CustCourceAbnormalInfo;
import com.howbuy.cs.bookcust.model.CustconstantInfo;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;
import java.util.Map;

@MapperScan
public interface BookingCustInfoDao {

    /**
     * 保存预约数据方法
     */
    int insertCmBookingCust(BookingCustInfo bookingCustInfo);
    
    /**
     * 查询客户信息
     */
    int queryConscustInfo(String mobileDigest);
    
    /**
     * 查询投顾信息
     */
    List<CustconstantInfo> queryListCustconstantInfo(String mobileDigest);
    
    /**
     * 查询异常CMS来源客户方法
     */
    List<CustCourceAbnormalInfo> queryListCustCourceAbnormal(Map<String, String> param);

    /**
     * @description: 删除预约客户中垃圾数据
     * @return int 删除条数
     * @author: jin.wang03
     * @date: 2023/11/15 13:13
     * @since JDK 1.8
     */
    int delBookingCustGarbage();

}
