/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.outservice.ordercenter;

import com.alibaba.fastjson.JSON;
import com.howbuy.cs.outservice.accenter.QueryAccCenterOuterService;
import com.howbuy.tms.high.orders.facade.search.queryBalanceTxAcctNoByFund.QueryBalanceTxAcctNoByFundFacade;
import com.howbuy.tms.high.orders.facade.search.queryBalanceTxAcctNoByFund.QueryBalanceTxAcctNoByFundRequest;
import com.howbuy.tms.high.orders.facade.search.queryBalanceTxAcctNoByFund.QueryBalanceTxAcctNoByFundResponse;
import crm.howbuy.base.enums.DisCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 查询客户持仓信息 OuterService
 * @date 2025/6/18 15:27
 * @since JDK 1.8
 */

@Slf4j
@Service
public class QueryCustBalanceOuterService {

    /**
     * 请求高端中台 参数
     */
    public static final String OUTLET_CODE_A20150120 = "A20150120";
    public static final String OPER_IP = "127.0.0.1";
    public static final String TX_CHANNEL = "5";


    @Autowired
    private QueryBalanceTxAcctNoByFundFacade queryBalanceTxAcctNoByFundFacade;

    @Resource
    private QueryAccCenterOuterService queryAccCenterOuterService;


    /**
     * @param fundCode 基金代码
     * @return java.util.List<java.lang.String> 一账通号列表
     * @description: 查询基金代码对应的客户持仓 一账通号列表
     * @author: jin.wang03
     * @date: 2025/6/18 16:13
     * @since JDK 1.8
     */
    public List<String> getBalanceHboneListByFundCode(String fundCode) {
        QueryBalanceTxAcctNoByFundRequest request = new QueryBalanceTxAcctNoByFundRequest();
        request.setFundCode(fundCode);
        request.setDisCode(DisCodeEnum.HOWBUY.getCode());
        request.setOutletCode(OUTLET_CODE_A20150120);
        request.setOperIp(OPER_IP);
        request.setTxChannel(TX_CHANNEL);
        log.info("QueryBalanceTxAcctNoByFundFacade.request 查询客户持仓请求参数：{}", JSON.toJSONString(request));
        QueryBalanceTxAcctNoByFundResponse response = queryBalanceTxAcctNoByFundFacade.execute(request);
        log.info("QueryBalanceTxAcctNoByFundFacade.response 查询客户持仓响应结果参数：{}", JSON.toJSONString(response));
        if (Objects.isNull(response) ||!response.isSuccess()) {
            log.error("查询客户持仓信息失败! request={}, response={}", JSON.toJSONString(request), JSON.toJSONString(response));
            return null;
        }

        //NOTICE-说明:
        // 服务端逻辑：
        // 对于直销产品，返回一账通列表：response.getHbOneNoList()
        // 对于代销产品，返回公募交易账号列表： response.getTxAcctNoList() 不能同时为空
        // 结论： hboneNoList 和 txAcctNoList 不会同时有值，  且 对于代销，需要根据公募交易账号查询一账通号
        List<String> hbOneNoList = response.getHbOneNoList();
        List<String> list = new ArrayList<>(CollectionUtils.isEmpty(hbOneNoList) ? new ArrayList<>() : hbOneNoList);

        // 代销客户号列表
        List<String> txAcctNoList = response.getTxAcctNoList();
        Map<String, String> txAcctHboneMap = queryAccCenterOuterService.getHboneNoMapByTxAcctNos(txAcctNoList);
        List<String> txHboneList = new ArrayList<>(txAcctHboneMap.values());
        list.addAll(CollectionUtils.isEmpty(txHboneList) ? new ArrayList<>() : txHboneList);
        log.info("查询基金代码：{}, 客户持仓 一账通号列表：{}", fundCode, hbOneNoList);
        return list;
    }


}