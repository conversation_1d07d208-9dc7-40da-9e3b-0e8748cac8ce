/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.outservice.crm.message.domain;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/5/24 17:44
 * @since JDK 1.8
 */

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;

@Data
public class CrmAcceptMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息类型
     */
    private String type;

    /**
     * 所需发送参数
     *         "encryEmail": "",
     *         "encryPhone": "",
     *         "emailTitle": ""
     */
    private JSONObject params;

    /**
     * 文件数据
     */
    private String templateParams;

    /**
     * 业务标识
     */
    private String uniqueId;

    /**
     * 模版文件
     */
    private String templateId;

}