/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.outservice.crm.message;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.cs.base.Constants;
import com.howbuy.cs.common.enums.AcceptMessageTypeEnum;
import com.howbuy.cs.outservice.crm.message.domain.CrmAcceptMessageDTO;
import com.howbuy.message.MessageService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @description: 消息发送
 * @date 2024/5/13 13:33
 * @since JDK 1.8
 */
@Slf4j
@Service
public class SendMessageOuterService {


    @Value("${TOPIC.CRM_ACCEPT_MESSAGE}")
    private String crmAcceptMessageTopic;

    /**
     * 用于控制 是否真实地推送消息
     * 1-推送
     * 0-不推送（用于上线时，测试使用）
     */
    @Value("${send.message.flag}")
    private String sendMessageFlag;


    /**
     * @param hboneNo
     * @param templateId
     * @param templateParams
     * @return void
     * @description:(请在此添加描述)
     * @author: jin.wang03
     * @date: 2025/6/19 19:20
     * @since JDK 1.8
     */
    public void sendHbApp(String hboneNo, String templateId, JSONObject templateParams) {
        SimpleMessage message = new SimpleMessage();
        CrmAcceptMessageDTO messageDTO = new CrmAcceptMessageDTO();

        messageDTO.setType(AcceptMessageTypeEnum.HB_FUND_APP.getCode());
        messageDTO.setTemplateId(templateId);
        messageDTO.setTemplateParams(templateParams.toJSONString());

        JSONObject params = new JSONObject();
        params.put("hboneNo", hboneNo);
        messageDTO.setParams(params);
        message.setContent(JSON.toJSONString(messageDTO));

        send(message);
    }

    /**
     * @param mobileCipher
     * @param templateId
     * @param templateParams
     * @return void
     * @description: 发送手机短信
     * @author: jin.wang03
     * @date: 2025/6/19 19:18
     * @since JDK 1.8
     */
    public void sendMobileCipher(String mobileCipher, String templateId, JSONObject templateParams) {
        SimpleMessage message = new SimpleMessage();
        CrmAcceptMessageDTO messageDTO = new CrmAcceptMessageDTO();

        messageDTO.setType(AcceptMessageTypeEnum.SMS.getCode());
        messageDTO.setTemplateId(templateId);
        messageDTO.setTemplateParams(templateParams.toJSONString());

        JSONObject params = new JSONObject();
        params.put("encryPhone", mobileCipher);
        messageDTO.setParams(params);
        message.setContent(JSON.toJSONString(messageDTO));

        send(message);
    }


    /**
     * @description: 根据消息中心配置的渠道策略，自动选择渠道 推送消息
     * @param hboneNo
     * @param templateId
     * @param templateParams
     * @return void
     * @author: jin.wang03
     * @date: 2025/6/23 11:22
     * @since JDK 1.8
     */
    public void sendAutoChannel(String hboneNo, String templateId, JSONObject templateParams) {
        SimpleMessage message = new SimpleMessage();
        CrmAcceptMessageDTO messageDTO = new CrmAcceptMessageDTO();

        messageDTO.setType(AcceptMessageTypeEnum.AUTO_CHANNEL_BY_HBONENO.getCode());
        messageDTO.setTemplateId(templateId);
        messageDTO.setTemplateParams(templateParams.toJSONString());

        JSONObject params = new JSONObject();
        params.put("hboneNo", hboneNo);
        messageDTO.setParams(params);

        message.setContent(JSON.toJSONString(messageDTO));

        send(message);
    }


    /**
     * @param message
     * @return void
     * @description: 将待发送的消息 发送给crm-wechat消息服务
     * @author: jin.wang03
     * @date: 2025/6/19 19:18
     * @since JDK 1.8
     */
    private void send(SimpleMessage message) {
        log.info("发送给crm-wechat消息:{}", message);
        if (!Constants.YES.equals(sendMessageFlag)) {
            // 不发送
            return;
        }
        MessageService.getInstance().send(crmAcceptMessageTopic, message);
    }


}