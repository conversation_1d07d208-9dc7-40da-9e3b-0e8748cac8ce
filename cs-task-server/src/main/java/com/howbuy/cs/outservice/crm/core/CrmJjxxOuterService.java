/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.outservice.crm.core;

import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/6/19 20:00
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CrmJjxxOuterService {

    @Autowired
    private JjxxInfoService jjxxInfoService;

    /**
     * @param fundCode
     * @return com.howbuy.crm.jjxx.dto.JjxxInfo
     * @description:(请在此添加描述)
     * @author: jin.wang03
     * @date: 2025/6/20 16:00
     * @since JDK 1.8
     */
    public JjxxInfo getJjxxByFundCode(String fundCode) {
        return jjxxInfoService.getJjxxByJjdm(fundCode);
    }

    /**
     * @param fundCode
     * @return crm.howbuy.base.enums.DisChannelCodeEnum
     * @description: (请在此添加描述)
     * @author: jin.wang03
     * @date: 2025/6/23 14:47
     * @since JDK 1.8
     */
    public DisChannelCodeEnum getDisCodeEnumByJjdm(String fundCode) {
        return jjxxInfoService.getDisCodeEnumByJjdm(fundCode);
    }

}