/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.outservice.td;

import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.dealorder.service.CrmHighDealOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/6/19 11:14
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CrmHighDealOrderOuterService {


    @Autowired
    private CrmHighDealOrderService crmHighDealOrderService;

    /**
     * @description: 获取客户首个交易上报日
     * @param hboneNo 一账通号
     * @param fundCode 基金代码
     * @return Date 首个交易上报日
     * @author: hongdong.xie
     * @date: 2025-06-16 13:25:20
     * @since JDK 1.8
     */
    public Date getFirstTradeDate(String hboneNo, String fundCode) {
        try {
            // 调用CrmHighDealOrderService#getEarliestSubmitDtByHboneAndFundCode
            ReturnMessageDto<String> dtResponse = crmHighDealOrderService.getEarliestSubmitDtByHboneAndFundCode(hboneNo, fundCode);

            if (Objects.isNull(dtResponse) || !dtResponse.isSuccess()
                    || StringUtils.isBlank(dtResponse.getReturnObject())) {
                return null;
            }

            String firstTradeDate = dtResponse.getReturnObject();

            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            return sdf.parse(firstTradeDate);
        } catch (Exception e) {
            log.error("获取客户{}基金{}的首个交易日期失败", hboneNo, fundCode, e);
            return null;
        }
    }

}