/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.outservice.auth;

import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.auth.facade.response.CodecSingleResponse;
import com.howbuy.common.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/5/27 14:10
 * @since JDK 1.8
 */
@Service
public class EncryptOuterService {

    @Autowired
    private EncryptSingleFacade encryptSingleFacade;

    /**
     * @param oriStr
     * @return java.lang.String
     * @description:加密
     * <AUTHOR>
     * @date 2024/5/27 14:13
     * @since JDK 1.8
     */
    public String encrypt(String oriStr) {
        CodecSingleResponse response = encryptSingleFacade.encrypt(oriStr);
        if (response.success()) {
            return response.getCodecText();
        }
        throw new BusinessException("", "加密异常");
    }

}
