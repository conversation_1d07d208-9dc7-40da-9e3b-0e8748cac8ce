/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.outservice.cms.report;

import com.howbuy.common.page.Page;
import com.howbuy.common.page.Pagination;
import com.howbuy.member.dto.report.ProductReportUpdatePushDTO;
import com.howbuy.member.service.report.ProductReportUpdatePushService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/6/19 11:11
 * @since JDK 1.8
 */

@Slf4j
@Service
public class CmsReportOutService {


    @Autowired
    private ProductReportUpdatePushService productReportUpdatePushService;

    /**
     * @return List<ProductReportUpdatePushDTO> 报告列表
     * @description: 获取每日更新的报告列表
     * @author: hongdong.xie
     * @date: 2025-06-16 13:25:20
     * @since JDK 1.8
     */
    public List<ProductReportUpdatePushDTO> getUpdatedReports(String startTimeStr, String endTimeStr, List<String> reportIds) {
        log.info("开始获取每日更新的报告列表");
        log.info("查询报告更新时间范围: {} 到 {}", startTimeStr, endTimeStr);

        // 分页查询所有报告，每页500条
        List<ProductReportUpdatePushDTO> allReportList = new ArrayList<>();
        int currentPage = 1;
        int perPage = 500;

        while (true) {
            // 创建分页查询请求
            Page page = new Page(currentPage, perPage);

            log.info("查询第{}页，每页{}条", currentPage, perPage);

            // 调用非交易接口获取区间内更新的产品报告列表
            Pagination<ProductReportUpdatePushDTO> pagination =
                    productReportUpdatePushService.getProductReportUpdatePushDTO(startTimeStr, endTimeStr, reportIds, page);

            if (pagination == null || CollectionUtils.isEmpty(pagination.getResultList())) {
                log.info("第{}页没有数据，查询结束", currentPage);
                break;
            }

            List<ProductReportUpdatePushDTO> currentPageList = pagination.getResultList();
            allReportList.addAll(currentPageList);

            log.info("第{}页获取到{}条报告", currentPage, currentPageList.size());

            // 判断是否还有下一页
            Page resultPage = pagination.getPage();
            if (resultPage != null && currentPageList.size() < perPage) {
                // 当前页数据量小于每页大小，说明已经是最后一页
                log.info("已查询到最后一页，总共{}页", currentPage);
                break;
            }

            currentPage++;

            // 防止无限循环，最多查询100页
            if (currentPage > 100) {
                log.warn("查询页数超过100页限制，停止查询");
                break;
            }
        }

        log.info("获取到更新的报告总数量: {}", allReportList.size());
        return allReportList;
    }

}