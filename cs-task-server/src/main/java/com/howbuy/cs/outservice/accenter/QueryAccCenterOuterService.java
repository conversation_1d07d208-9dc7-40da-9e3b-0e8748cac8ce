/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.outservice.accenter;

import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.facade.common.AccBaseResponse;
import com.howbuy.acccenter.facade.query.querytxacctinfofacade.QueryTxAcctInfoFacade;
import com.howbuy.acccenter.facade.query.querytxacctinfofacade.QueryTxAcctInfoRequest;
import com.howbuy.acccenter.facade.query.querytxacctinfofacade.QueryTxAcctInfoResponse;
import com.howbuy.acccenter.facade.query.querywechatbindinfo.QueryWechatAcctBindFacade;
import com.howbuy.acccenter.facade.query.querywechatbindinfo.QueryWechatAcctBindRequest;
import com.howbuy.acccenter.facade.query.querywechatbindinfo.QueryWechatAcctBindResponse;
import com.howbuy.acccenter.facade.query.querywechatbindinfo.WechatAcctBindInfo;
import com.howbuy.acccenter.facade.query.sensitive.mobile.QueryCustMobileFacade;
import com.howbuy.acccenter.facade.query.sensitive.mobile.QueryCustMobileRequest;
import com.howbuy.acccenter.facade.query.sensitive.mobile.QueryCustMobileResponse;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.cs.common.Constant.RMIConstant;
import crm.howbuy.base.enums.DisCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/6/18 15:29
 * @since JDK 1.8
 */
@Slf4j
@Service
public class QueryAccCenterOuterService {


    @Autowired
    private QueryTxAcctInfoFacade queryTxAcctInfoFacade;

    @Autowired
    private QueryWechatAcctBindFacade queryWechatAcctBindFacade;

    @Autowired
    private QueryCustMobileFacade queryCustMobileFacade;

    /**
     * 微信公众号绑定状态 2-已绑定
     */
    public static final String APP_ID_STATUS_BOUND = "2";


    /**
     * @param txAcctNoList
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @description: 通过 公募交易号查询HBONE号
     * @author: jin.wang03
     * @date: 2025/6/18 17:18
     * @since JDK 1.8
     */
    public Map<String, String> getHboneNoMapByTxAcctNos(List<String> txAcctNoList) {
        if (CollectionUtils.isEmpty(txAcctNoList)) {
            return new HashMap<>();
        }

        Map<String, String> map = new HashMap<>();
        for (String txAcctNo : txAcctNoList) {
            String hboneNo = getHboneNoByTxAcctNo(txAcctNo);
            if (StringUtil.isEmpty(hboneNo)) {
                continue;
            }
            map.put(txAcctNo, hboneNo);
        }

        return map;
    }


    /**
     * @param txAcctNo
     * @return java.lang.String
     * @description: 通过 公募交易号查询HBONE号
     * @author: jin.wang03
     * @date: 2025/6/18 17:19
     * @since JDK 1.8
     */
    public String getHboneNoByTxAcctNo(String txAcctNo) {
        if (StringUtil.isEmpty(txAcctNo)) {
            return null;
        }

        QueryTxAcctInfoResponse response;
        try {
            QueryTxAcctInfoRequest request = new QueryTxAcctInfoRequest();
            request.setTxAcctNo(txAcctNo);
            request.setDisCode(DisCodeEnum.HOWBUY.getCode());
            log.info("QueryTxAcctInfoFacade.request 查询客户一账通请求参数：{}", JSON.toJSONString(request));
            response = queryTxAcctInfoFacade.execute(request);
            log.info("QueryTxAcctInfoFacade.response 查询客户一账通响应结果参数：{}", JSON.toJSONString(response));
            if (Objects.isNull(response)
                    || !RMIConstant.RMISUCCNEW.equals(response.getReturnCode())) {
                return null;
            }
        } catch (Exception e) {
            log.error("查询客户一账通信息失败! error={}", e.getMessage(), e);
            return null;
        }

        return response.getHboneNo();
    }


    /**
     * @description: 通过HBONE号查询微信公众号的绑定信息
     * @param hboneNo 一账通号
     * @param appId 公众号appId
     * @return java.util.List<com.howbuy.acccenter.facade.query.querywechatbindinfo.WechatAcctBindInfo>
     * @author: jin.wang03
     * @date: 2025/6/23 10:57
     * @since JDK 1.8
     */
    public List<WechatAcctBindInfo> getWechatAcctBindInfo(String hboneNo, String appId) {
        QueryWechatAcctBindRequest acctBindRequest = new QueryWechatAcctBindRequest();
        acctBindRequest.setHboneNo(hboneNo);
        acctBindRequest.setAppId(appId);
        QueryWechatAcctBindResponse response = queryWechatAcctBindFacade.execute(acctBindRequest);
        log.info("QueryWechatAcctBindFacade.response 查询微信绑定信息响应结果参数：{}", JSON.toJSONString(response));
        if (Objects.isNull(response)) {
            log.error("查询微信绑定信息失败! request={}, response={}", JSON.toJSONString(acctBindRequest), JSON.toJSONString(response));
            return null;
        }
        return response.getWechatList();
    }

    /**
     * @description: 判断客户是否关注公众号
     * @param hboneNo 一账通号
     * @param appId 公众号appId
     * @return boolean
     * @author: jin.wang03
     * @date: 2025/6/23 10:49
     * @since JDK 1.8
     */
    public boolean isFollowOfficialAccount(String hboneNo, String appId) {
        List<WechatAcctBindInfo> wechatAcctBindInfo = getWechatAcctBindInfo(hboneNo, appId);
        return CollectionUtils.isNotEmpty(wechatAcctBindInfo) && APP_ID_STATUS_BOUND.equals(wechatAcctBindInfo.get(0).getBindStatus());
    }


    /**
     * @description:(请在此添加描述)
     * @param hboneNo
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2025/6/24 16:24
     * @since JDK 1.8
     */
    public String getMobile(String hboneNo) {
        QueryCustMobileRequest request = new QueryCustMobileRequest();
        request.setHboneNo(hboneNo);
        QueryCustMobileResponse response = queryCustMobileFacade.execute(request);
        log.info("QueryCustMobileFacade.response 查询手机号响应结果参数：{}", JSON.toJSONString(response));
        if (!isAcctSuccess(response)) {
            log.error("查询手机号失败! request={}, response={}", JSON.toJSONString(request), JSON.toJSONString(response));
            return null;
        }
        if (Objects.isNull(response.getCustMobile()) || StringUtils.isEmpty(response.getCustMobile().getMobile())) {
            return null;
        }
        return response.getCustMobile().getMobile();
    }


    /**
     * @description: 判断查询结果是否成功
     * @param resp
     * @return boolean
     * @author: jin.wang03
     * @date: 2025/6/24 16:24
     * @since JDK 1.8
     */
    public static boolean isAcctSuccess(AccBaseResponse resp) {
        return resp != null && RMIConstant.RMISUCCNEW.equals(resp.getReturnCode());
    }

}