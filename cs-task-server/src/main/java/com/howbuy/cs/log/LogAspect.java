/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.log;

import com.alibaba.fastjson.JSON;
import com.howbuy.cs.common.base.BaseConstantEnum;
import com.howbuy.cs.task.util.LoggerUtils;
import com.howbuy.cs.task.util.MainLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description: 日志打印
 * @date 2023/7/5 10:40
 * @since JDK 1.8
 */
@Slf4j
@Aspect
@Component
public class LogAspect {

    private static final Logger mainLogger = LogManager.getLogger("mainlog");

    @Pointcut("execution(* com.howbuy.crm..*.service..*ServiceImpl.*(..))")
    public void entryPoint() {
    }

    /**
     * @param pjp 参数
     * @return java.lang.Object
     * @description: 切点实现
     * @author: hongdong.xie
     * @date: 2023/7/5 10:48
     * @since JDK 1.8
     */
    @Around("entryPoint()")
    public Object doAround(ProceedingJoinPoint pjp) throws Throwable {
        LoggerUtils.setReqId(getReqId());
        LoggerUtils.setRanNo(LoggerUtils.createRanNo());
        long start = System.currentTimeMillis();
        // 获取方法参数
        Object[] args = pjp.getArgs();
        Signature signature = pjp.getSignature();
        String signatureStr = signature.toShortString();
        Object response = null;
        // 默认请求状态码 0000
        String returnCode = BaseConstantEnum.SUCCESS.getCode();
        try {
            MethodSignature methodSignature = (MethodSignature) signature;
            Method method = methodSignature.getMethod();
            printRequestLog(method, args);
            response = pjp.proceed();
        } catch (Throwable e) {
            // 异常状态码 9999
            returnCode = BaseConstantEnum.UNKNOWN_ERROR.getCode();
            log.error("BusinessAspect|exception:" + e.getMessage(), e);
        } finally {
            long end = System.currentTimeMillis();
            // 打印返回日志
            printResponseLog(response, start);
            // 记录main.log
            printMainLog(returnCode, signatureStr, end - start);
            LoggerUtils.clearConfig();
        }
        return response;
    }

    /**
     * @param
     * @return java.lang.String
     * @throws
     * @description: 获取reqId
     * @since JDK 1.8
     */
    private String getReqId() {
        // dubbo接口调用
        String reqId = (RpcContext.getContext() ==null) ? null : RpcContext.getContext().getAttachment(LoggerUtils.REQ_ID);
        if (StringUtils.isEmpty(reqId)) {
            // http接口调用
            reqId = LoggerUtils.getUuid();
            // 如果都没有则生成一个
            if (StringUtils.isEmpty(reqId)) {
                reqId = UUID.randomUUID().toString().replace("-", "");
            }
        }
        return reqId;
    }

    /**
     * @description: 打印main.log
     * @param
     * @return java.lang.String
     * @throws
     * @since JDK 1.8
     */
    private void printMainLog(String returnCode, String signatureStr, long time) {
        if (mainLogger.isInfoEnabled()) {
            try {
                String reqId = LoggerUtils.getReqId();
                String ranNo = LoggerUtils.getRanNo();
                MainLogUtils.dubboCallIn(reqId, ranNo, signatureStr, returnCode, time);
            } catch (Exception e) {
                log.error("LogAspect printMainLog error.|methodName:{}", signatureStr);
                log.error("", e);
            }
        }
    }

    /**
     * @param response 返回数据
     * @param start    开始时间
     * @return void
     * @description: 打印返回日志
     * @author: hongdong.xie
     * @date: 2023/12/11 15:51
     * @since JDK 1.8
     */
    private void printResponseLog(Object response, long start) {
        try {
            if (log.isInfoEnabled()) {
                log.info("BusinessAspect|response:{}", response == null ? null : JSON.toJSONString(response));
            }
            long time = System.currentTimeMillis() - start;
            log.info("cost:{}", time);
        } catch (Exception e) {
            log.error("printResponseLog error.", e);
        }
    }

    /**
     * @param method 方法
     * @param args   请求参数
     * @return void
     * @description: 处理请求日志
     * @author: hongdong.xie
     * @date: 2023/12/11 15:48
     * @since JDK 1.8
     */
    private void printRequestLog(Method method, Object[] args) {
        try {
            if (log.isInfoEnabled()) {
                String methodName = method.getName();
                log.info("BusinessAspect|classSimpleName:{},methodName:{},args:{}", method.getDeclaringClass().getSimpleName(), methodName, args == null ? null : JSON.toJSONString(args));
            }
        } catch (Exception e) {
            log.error("printRequestLog error.", e);
        }
    }
}