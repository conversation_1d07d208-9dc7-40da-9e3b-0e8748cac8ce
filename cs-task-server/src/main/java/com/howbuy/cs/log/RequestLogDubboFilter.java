package com.howbuy.cs.log;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.howbuy.cs.common.base.BaseConstantEnum;
import com.howbuy.cs.task.util.LoggerUtils;
import com.howbuy.cs.task.util.MainLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.*;

import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @Description: 请求日志过滤器
 * @reason:
 * @Date: 2020/4/22 11:06
 */
@Slf4j
public class RequestLogDubboFilter implements Filter {
    private static final int custSize = 10000;

    private static final Map<Class<?>, Function<Object, String>> RETURN_CODE_EXTRACTORS = new HashMap<>();
    static {
        RETURN_CODE_EXTRACTORS.put(com.howbuy.common.facade.BaseResponse.class, obj -> ((com.howbuy.common.facade.BaseResponse) obj).getReturnCode());
        RETURN_CODE_EXTRACTORS.put(com.howbuy.cc.center.base.BaseResponse.class, obj -> ((com.howbuy.cc.center.base.BaseResponse) obj).getReturnCode());
    }

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        List<String> msgs = new ArrayList<>();
        String methodName = "";
        String interfaceName = "";
        String invokerDetail = "";
        try {
            // 设置reqId
            setAttachmentParam();
            // 获取dubbo调用信息
            methodName = invocation.getMethodName();
            interfaceName = this.getInterfaceMessage(invoker, invocation);
            invokerDetail = JSON.toJSONString(invocation.getInvoker());
            if (StringUtils.isNotBlank(invokerDetail) && invokerDetail.length() > 3500) {
                invokerDetail = invokerDetail.substring(0, 3500);
            }

            msgs.add(this.getHostMessage());
            msgs.add(interfaceName);

            // 调用参数
            Object[] args = invocation.getArguments();
            if (args != null && args.length > 0) {
                msgs.add("request:" + JSON.toJSONString(args));
            } else {
                msgs.add("null");
            }
        } catch (Throwable exception) {
            log.error("Exception in RequestLogDubboFilter of service(" + invoker + " -> " + invocation + ")",
                    exception);
        }

        long startTime = System.currentTimeMillis();
        Result result = null;
        long cost;
        try {
            result = invoker.invoke(invocation);
            // 计算接口调用时间
            cost = System.currentTimeMillis() - startTime;
            String returnCode = getReturnCode(result.getValue());
            MainLogUtils.dubboCallOut(LoggerUtils.getReqId(), LoggerUtils.getRanNo(), MainLogUtils.getInterfaceName(interfaceName, methodName), returnCode, cost);
        } catch (Throwable e){
            // 计算接口调用时间
            cost = System.currentTimeMillis() - startTime;
            MainLogUtils.dubboCallOut(LoggerUtils.getReqId(), LoggerUtils.getRanNo(), MainLogUtils.getInterfaceName(interfaceName, methodName), BaseConstantEnum.UNKNOWN_ERROR.getCode(), cost);
        }

        msgs.add("response: " + (null == result ? "null" : JSON.toJSONString(result.getValue())));
        msgs.add(String.valueOf(cost));
        log.info(cust(msgs));

        return result;
    }

    /**
     * 获取返回码
     * @param obj
     * @return
     */
    private String getReturnCode(Object obj) {
        if (obj == null) {
            return BaseConstantEnum.SUCCESS.getCode();
        }

        try {
            for (Map.Entry<Class<?>, Function<Object, String>> entry : RETURN_CODE_EXTRACTORS.entrySet()) {
                if (entry.getKey().isAssignableFrom(obj.getClass())) {
                    return entry.getValue().apply(obj);
                }
            }
        } catch (Exception e) {
            log.error("Error extracting return code", e);
        }

        return BaseConstantEnum.SUCCESS.getCode();
    }

    /**
     * @description: 设置附件参数
     * @param
     * @return void
     * @author: hongdong.xie
     * @date: 2024/9/27 14:39
     * @since JDK 1.8
     */
    private void setAttachmentParam() {
        // 设置请求reqId
        String reqId = LoggerUtils.getCfgValue(LoggerUtils.CTX_UUID);
        if (StringUtils.isEmpty(reqId)) {
            reqId = UUID.randomUUID().toString().replace("-", "");
        }
        // 往服务端传递reqId
        RpcContext.getContext().setAttachment(LoggerUtils.REQ_ID, reqId);
    }

    private static String cust(List<String> source) {
        if (source == null || source.isEmpty()) {
            return null;
        }

        String sentence = Joiner.on("|").join(source);

        if (sentence.length() < custSize) {
            return sentence;
        } else {
            return String.format("%s......%s", sentence.substring(0, custSize/2), sentence.substring(sentence.length() - custSize/2));
        }
    }

    /**
     * 生成访问方法的日志信息：192.168.119.214:37418 | 192.168.221.20:20117|center-asset/com.howbuy.cc.center.asset.income
     * .schedule.UnfoldUserAssetSchedule execute(java.lang.String)
     *
     * @param invoker
     * @param invocation
     * @return
     */
    private String getInterfaceMessage(Invoker<?> invoker, Invocation invocation) {
        String version = invoker.getUrl().getParameter("version");
        String serviceName = invoker.getInterface().getName();
        String group = invoker.getUrl().getParameter("group");
        StringBuilder returnMsg = new StringBuilder();

        if (group != null && !group.isEmpty()) {
            returnMsg.append(group).append("/");
        }

        returnMsg.append(serviceName);
        if (version != null && !version.isEmpty()) {
            returnMsg.append(":").append(version);
        }

        returnMsg.append(" ");
        returnMsg.append(invocation.getMethodName());
        returnMsg.append("(");
        Class[] types = invocation.getParameterTypes();
        //设置参数类型
        if (types != null && types.length > 0) {
            boolean first = true;
            int paramLen = types.length;

            for (int i = 0; i < paramLen; ++i) {
                Class type = types[i];
                if (first) {
                    first = false;
                } else {
                    returnMsg.append(",");
                }
                returnMsg.append(type.getName());
            }
        }

        returnMsg.append(") ");
        return returnMsg.toString();
    }

    private String getHostMessage() {
        StringBuilder returnMsg = new StringBuilder();
        RpcContext rpcContext = RpcContext.getContext();
        returnMsg.append(rpcContext.getRemoteHost()).append(":").append(rpcContext.getRemotePort()).append(" | ")
                .append(rpcContext.getLocalHost()).append(":").append(rpcContext.getLocalPort());
        return returnMsg.toString();
    }
}
