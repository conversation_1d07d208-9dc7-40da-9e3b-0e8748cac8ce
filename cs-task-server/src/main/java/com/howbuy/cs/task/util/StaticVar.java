package com.howbuy.cs.task.util;

import com.howbuy.cs.task.model.ConsultTypeVO;

import java.util.HashMap;
import java.util.Map;

public class StaticVar {

    public final static String DEFAULT_ASSIGN_RULE = "10000,10010,10100,10110,11000,10001,10011,10101,10111,11001";
    public final static String DEFAULT_VISIT_RULE = "1000_03,1001_03,1010_03,1011_03,1100_03,1000_13,1001_13,1010_13,1011_13,1100_13";

    /**
     * 投顾自动分配的时候，当前总数和初始化额度的比率
     */
    public final static double CURR_INIT_TARGETNUM_RATE = 1.3;
    /**
     * 拓鹏普通
     */
    public final static String TP_COMMON = "TP_COMMON";
    /**
     * 创世华信通知
     */
    public final static String CSHX = "CSHX";
    /**
     * 字符分割符号
     */
    public final static String SPLIT_SIGN = "^";
    /**
     * 任务运行中
     */
    public final static int Task_Start = 1;
    /**
     * 任务休息中
     */
    public final static int Task_Stop = 0;
    /**
     * 发送成功
     */
    public final static int SUCCESS = 1;
    /**
     * 发送失败
     */
    public final static int ERROR = 0;

    public final static String CALLEE_DEFAULT = "3400";

    public final static String KEYNUM_DEFAULT = "10";  //表示无按键

    public final static String HOWBUY_CALLEENO = "3400";

    public final static String TENCENT_CALLEENO = "3310";

    public final static String HIGH_KEYNUM = "1";

    public final static int NEW = 1;

    public static Map<String, String> sourceMap = new HashMap<String, String>();

    public static Map<String, ConsultTypeVO> consultType_SourceMap = new HashMap<>();

    public static Map<String, ConsultTypeVO> consultType_KeyMap = new HashMap<>();

    public static Map<String, ConsultTypeVO> consultNO_Map = new HashMap<>();

    public final static String EC_CS_ORGCODE = "200000003";

    public final static String Exist_Advisor = "已有投顾";

    //Leads分配
    public static final String TRANSF_REASON_LEADSFP ="6";
    //其他（客服）
    public static final String TRANSF_REASON_OTHERKF ="9";
    //同行
    public static final String TRANSF_REASON_TH = "11";

    /**
     * 是否香港 1-是
     */
    public static final String SFXG_YES = "1";


}
