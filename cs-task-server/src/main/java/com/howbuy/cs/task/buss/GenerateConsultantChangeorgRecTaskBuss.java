/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.buss;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.consultant.dto.CmConsultantExpDomain;
import com.howbuy.crm.nt.consultant.request.QueryConsultantExpRequest;
import com.howbuy.crm.nt.consultant.response.QueryConsultantExpReponse;
import com.howbuy.crm.nt.consultant.service.QueryConsultantExpService;
import com.howbuy.cs.base.Constants;
import com.howbuy.cs.cacheService.lock.LockService;
import com.howbuy.cs.task.model.CmConsultant;
import com.howbuy.cs.task.model.CmConsultantChangeorgRec;
import com.howbuy.cs.task.model.CmConsultantExp;
import com.howbuy.cs.task.service.CmConsultantChangeorgRecServiceImpl;
import com.howbuy.cs.task.util.DateTimeUtil;
import com.howbuy.cs.task.util.LoggerUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @description: 生成投顾变更记录任务业务类
 * <AUTHOR>
 * @date 2024/11/25 17:14
 * @since JDK 1.8
 */
@Slf4j
@Service("generateConsultantChangeorgRecTaskBuss")
public class GenerateConsultantChangeorgRecTaskBuss extends AbstractTaskBuss {

    @Autowired
    private CmConsultantChangeorgRecServiceImpl cmConsultantChangeorgRecService;

    @Autowired
    private QueryConsultantExpService queryConsultantExpService;

    @Autowired
    private LockService lockService;

    /**
     * @description: 生成投顾变更记录
     * @return void
     * @author: hongdong.xie
     * @date: 2024/11/25 19:19
     * @since JDK 1.8
     */
    public void generate() {
        log.info("生成投顾变更记录任务");
        int pageNo = 1;
        int pageSize = 500;
        while (true) {
            log.info("查询第{}页的投顾code", pageNo);
            // 查询所有的投顾code
            List<String> consCodeList = cmConsultantChangeorgRecService.getAllConsCode(pageNo, pageSize);
            if(CollectionUtils.isEmpty(consCodeList)) {
                log.info("查询投顾code列表为空，结束任务");
                break;
            }

            String uuid = LoggerUtils.getUuid();
            List<Boolean> futureList = consCodeList.stream().map(consCode -> CompletableFuture.supplyAsync(() -> {
                LoggerUtils.setChildUUID(uuid);
                try {
                    // 根据投顾code处理变更记录
                    processConCode(consCode);
                }catch (Exception e){
                    log.error("consCode：" + consCode, e);
                    return Boolean.FALSE;
                }
                return Boolean.TRUE;
            }, THREAD_POOL)).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());

            boolean allResult = futureList.stream().allMatch(result -> result);

            if (!allResult) {
                log.error("processConCode process error");
            }

            log.info("processConCode result :{}", allResult);

            pageNo++;
        }
    }

    /**
     * @description: 处理投顾code
     * @param consCode
     * @return void
     * @author: hongdong.xie
     * @date: 2024/11/25 18:48
     * @since JDK 1.8
     */
    private void processConCode(String consCode) {
        log.info("查询投顾code:{}的变更记录", consCode);
        // 根据投顾code查询每个管理层级的最新管理层
        QueryConsultantExpRequest request=new QueryConsultantExpRequest();
        request.setConscode(consCode);
        QueryConsultantExpReponse reponse = queryConsultantExpService.queryConsultantExpLead(request);
        if(Objects.isNull(reponse) || Objects.isNull(reponse.getCmConsultantExpDomain())) {
            log.info("查询投顾code:{}的当前管理层记录为空", consCode);
            return;
        }
        CmConsultantExpDomain domain = reponse.getCmConsultantExpDomain();
        log.info("查询投顾code:{}的当前管理层记录:{}", consCode, JSON.toJSONString(domain));
        Map<String, CmConsultantChangeorgRec> recMap = cmConsultantChangeorgRecService.getNewstByConsCodeAndLevel(consCode);
        log.info("查询投顾code:{}的变更记录recMap:{}", consCode, recMap);
        List<CmConsultantChangeorgRec> addList = new ArrayList<>();
        List<CmConsultantChangeorgRec> updateList = new ArrayList<>();

        // 分总
        List<String> outletleaderList = domain.getOutletleaderList();
        extracted(consCode, recMap, outletleaderList, addList, updateList, Constants.MANAGEMENT_LEVEL_3);
        // 区域执行副总
        List<String> areafleaderList = domain.getAreafleaderList();
        extracted(consCode, recMap, areafleaderList, addList, updateList, Constants.MANAGEMENT_LEVEL_4);
        // 区域总
        List<String> arealeaderList = domain.getArealeaderList();
        extracted(consCode, recMap, arealeaderList, addList, updateList, Constants.MANAGEMENT_LEVEL_5);
        // 批量插入或更新
        cmConsultantChangeorgRecService.insertOrUpdate(addList, updateList);
    }


    /**
     * @description: 处理更新和新增逻辑
     * @param consCode 投顾code
     * @param recMap 投顾code查询每个管理层级的最新的变更记录
     * @param outletleaderList 分总
     * @param addList 添加列表
     * @param updateList 更新列表
     * @param managementLevel 管理层级
     * @return void
     * @author: hongdong.xie
     * @date: 2024/11/25 18:40
     * @since JDK 1.8
     */
    private void extracted(String consCode, Map<String, CmConsultantChangeorgRec> recMap, List<String> outletleaderList,
                           List<CmConsultantChangeorgRec> addList, List<CmConsultantChangeorgRec> updateList,String managementLevel) {
        CmConsultantChangeorgRec rec3 = recMap.get(managementLevel);

        // 兼容CM_CONSULTANT_EXP表中对应的管理层数据为待审核，接口返回空的情况，这种情况不做处理
        // 如果新管理者为空（outletleaderList）且原管理者的CM_CONSULTANT_EXP表的记录审核状态不为2-审核通过，则不做处理
        if(CollectionUtils.isEmpty(outletleaderList) && Objects.nonNull(rec3) && Objects.nonNull(rec3.getManagementCode())) {
            CmConsultantExp cmConsultantExp = cmConsultantChangeorgRecService.getCmConsultantExpByUserId(rec3.getManagementCode());
            // 审核状态不为2-审核通过，则不做处理
            if (Objects.nonNull(cmConsultantExp) && !Constants.CHECK_FLAG_2.equals(cmConsultantExp.getCheckflag())) {
                log.info("投顾code:{}的管理层级:{}的审核状态不为2-审核通过，不做处理", consCode, managementLevel);
                return;
            }
        }

        String currYMD = DateTimeUtil.getCurrYMD();
        String prevYMD = DateTimeUtil.fmtDate(DateTimeUtil.addDaysFromNow(-1), DateTimeUtil.YYYYMMDD);
        String managementConsCode = getManagementConsCode(outletleaderList);
        // outletleaderList为null且rec3也为null（无对应新管理层 && 无历史变更记录，新增一条空的）
        if(StringUtils.isEmpty(managementConsCode) && Objects.isNull(rec3)) {
            createChangeorgRec(consCode, managementConsCode, managementLevel, currYMD, addList);
            return;
        }
        // outletleaderList不为null且rec3为null（有对应新管理层 && 无历史变更记录，新增一条）
        if(StringUtils.isNotEmpty(managementConsCode) && Objects.isNull(rec3)) {
            createChangeorgRec(consCode, managementConsCode, managementLevel, currYMD, addList);
            return;
        }
        // outletleaderList为null且rec3不为null但是rec3的managementCode为null,则不做处理（都为空则说明没有变化）
        if(CollectionUtils.isEmpty(outletleaderList) && Objects.nonNull(rec3) && Objects.isNull(rec3.getManagementCode())) {
            return;
        }
        // outletleaderList为null且rec3不为null（无对应新管理层 && 有历史变更记录，更新原来的，新增一条空的）
        if(StringUtils.isEmpty(managementConsCode) && Objects.nonNull(rec3)) {
            rec3.setEndDate(prevYMD);
            rec3.setUpdateDtm(new Date());
            updateList.add(rec3);
            createChangeorgRec(consCode, managementConsCode, managementLevel, currYMD, addList);
            return;
        }
        // outletleaderList不为null且rec3不为null（心管理层和老管理层不相同，新增一条新的）
        if(StringUtils.isNotEmpty(managementConsCode) && Objects.nonNull(rec3)) {
            if(!managementConsCode.equals(rec3.getManagementCode())) {
                rec3.setEndDate(prevYMD);
                rec3.setUpdateDtm(new Date());
                updateList.add(rec3);
                createChangeorgRec(consCode, managementConsCode, managementLevel, currYMD, addList);
            }
        }
    }

    /**
     * @description: 获取管理者code
     * @param outletleaderList
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2024/11/25 19:14
     * @since JDK 1.8
     */
    private String getManagementConsCode(List<String> outletleaderList) {
        String managementConsCode = null;
        if (!CollectionUtils.isEmpty(outletleaderList)) {
            managementConsCode = outletleaderList.get(0);
        }
        return managementConsCode;
    }

    /**
     * @description: 创建变更记录
     * @param consCode 投顾code
     * @param managementConsCode	 分总code
     * @param managementLevel 管理层级
     * @param currYMD 当前日期
     * @param addList 添加列表
     * @return void
     * @author: hongdong.xie
     * @date: 2024/11/25 18:21
     * @since JDK 1.8
     */
    private void createChangeorgRec(String consCode, String managementConsCode, String managementLevel, String currYMD, List<CmConsultantChangeorgRec> addList) {
        String managementOutletCode= null;
        if (!StringUtils.isEmpty(managementConsCode)) {
            CmConsultant leaderInfo = cmConsultantChangeorgRecService.getConsultantByConsCode(managementConsCode);
            if (Objects.nonNull(leaderInfo)) {
                managementOutletCode = leaderInfo.getOutletcode();
            }
        }

        CmConsultantChangeorgRec rec = new CmConsultantChangeorgRec();
        rec.setRecordNo(currYMD + lockService.getSequence());
        rec.setConscode(consCode);
        rec.setTeamCode(managementOutletCode);
        rec.setManagementCode(managementConsCode);
        rec.setManagementLevel(managementLevel);
        rec.setStartDate(currYMD);
        rec.setEndDate(null);
        Date now = new Date();
        rec.setCreateDtm(now);
        rec.setUpdateDtm(now);
        addList.add(rec);
    }
}