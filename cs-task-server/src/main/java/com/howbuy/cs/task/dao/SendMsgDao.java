package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.CsSendMsgLog;
import com.howbuy.cs.task.model.SendMsgInfo;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;
import java.util.Map;

@MapperScan
public interface SendMsgDao {

	/**
	 * 获取需要发送短信和邮件的信息
	 */
	public List<SendMsgInfo> getNeedSendInfo();

	/**
	 * 获取咨询内容
	 */
	public String getCustContent(String conscustNo);

	/**
	 * 更新日志表中的发送短信标识符
	 */
	public Integer updateSendSmsFlag(final Map<String, Object> param);
	
	/**
	 * 更新日志表中的发送邮件标识符
	 */
	public Integer updateSendEmailFlag(final Map<String, Object> param);

	/**
	 * @description: 将cs_autodistribute_log推送标识符更新为已处理
	 * @param id
	 * @return java.lang.Integer
	 * @author: jin.wang03
	 * @date: 2025/6/24 13:53
	 * @since JDK 1.8
	 */
	Integer updateDealPushMsgFlag(@Param("id") long id);

	/**
	 * 向自动发送邮件短信日志表中插入信息
	 */
	public Integer addSendLog(CsSendMsgLog csSendMsgLog);
}
