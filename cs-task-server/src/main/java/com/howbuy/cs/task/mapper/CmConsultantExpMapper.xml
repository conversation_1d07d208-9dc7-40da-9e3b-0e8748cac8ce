<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.cs.task.dao.CmConsultantExpMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.cs.task.model.CmConsultantExp">
    <!--@mbg.generated-->
    <!--@Table CM_CONSULTANT_EXP-->
    <id column="USERID" jdbcType="VARCHAR" property="userid" />
    <result column="USERNO" jdbcType="VARCHAR" property="userno" />
    <result column="PROVCODE" jdbcType="VARCHAR" property="provcode" />
    <result column="CITYCODE" jdbcType="VARCHAR" property="citycode" />
    <result column="GENDER" jdbcType="VARCHAR" property="gender" />
    <result column="BIRTHDAY" jdbcType="VARCHAR" property="birthday" />
    <result column="EDULEVEL" jdbcType="VARCHAR" property="edulevel" />
    <result column="WORKTYPE" jdbcType="VARCHAR" property="worktype" />
    <result column="WORKSTATE" jdbcType="VARCHAR" property="workstate" />
    <result column="USERLEVEL" jdbcType="VARCHAR" property="userlevel" />
    <result column="CURMONTHLEVEL" jdbcType="VARCHAR" property="curmonthlevel" />
    <result column="STARTDT" jdbcType="VARCHAR" property="startdt" />
    <result column="STARTLEVEL" jdbcType="VARCHAR" property="startlevel" />
    <result column="SALARY" jdbcType="DECIMAL" property="salary" />
    <result column="PROBATIONENDDT" jdbcType="VARCHAR" property="probationenddt" />
    <result column="REGULARDT" jdbcType="VARCHAR" property="regulardt" />
    <result column="REGULARLEVEL" jdbcType="VARCHAR" property="regularlevel" />
    <result column="REGULARSALARY" jdbcType="DECIMAL" property="regularsalary" />
    <result column="QUITDT" jdbcType="VARCHAR" property="quitdt" />
    <result column="QUITLEVEL" jdbcType="VARCHAR" property="quitlevel" />
    <result column="QUITSALARY" jdbcType="DECIMAL" property="quitsalary" />
    <result column="QUITREASON" jdbcType="VARCHAR" property="quitreason" />
    <result column="SERVINGAGE" jdbcType="DECIMAL" property="servingage" />
    <result column="CHECKFLAG" jdbcType="VARCHAR" property="checkflag" />
    <result column="CHECKOR" jdbcType="VARCHAR" property="checkor" />
    <result column="JJCARDNO" jdbcType="VARCHAR" property="jjcardno" />
    <result column="ATTACHTYPE" jdbcType="VARCHAR" property="attachtype" />
    <result column="BACKGROUND" jdbcType="VARCHAR" property="background" />
    <result column="SOURCE" jdbcType="VARCHAR" property="source" />
    <result column="BEFOREPOSITIONTYPE" jdbcType="VARCHAR" property="beforepositiontype" />
    <result column="BEFOREPOSITIONAGE" jdbcType="VARCHAR" property="beforepositionage" />
    <result column="RECRUIT" jdbcType="VARCHAR" property="recruit" />
    <result column="RECOMMEND" jdbcType="VARCHAR" property="recommend" />
    <result column="RECOMMENDTYPE" jdbcType="VARCHAR" property="recommendtype" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATDT" jdbcType="TIMESTAMP" property="creatdt" />
    <result column="MODOR" jdbcType="VARCHAR" property="modor" />
    <result column="MODDT" jdbcType="TIMESTAMP" property="moddt" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="TEAMCODE" jdbcType="VARCHAR" property="teamcode" />
    <result column="OUTLETCODE" jdbcType="VARCHAR" property="outletcode" />
    <result column="CONSNAME" jdbcType="VARCHAR" property="consname" />
    <result column="RECOMMENDUSERNO" jdbcType="VARCHAR" property="recommenduserno" />
    <result column="SUBPOSITIONS" jdbcType="VARCHAR" property="subpositions" />
    <result column="NEXTTESTDATE" jdbcType="VARCHAR" property="nexttestdate" />
    <result column="PROBATIONRESULT3M" jdbcType="VARCHAR" property="probationresult3m" />
    <result column="PROBATIONRESULT6M" jdbcType="VARCHAR" property="probationresult6m" />
    <result column="CURMONTHSALARY" jdbcType="DECIMAL" property="curmonthsalary" />
    <result column="QUITINFO" jdbcType="VARCHAR" property="quitinfo" />
    <result column="PROMOTEDATE" jdbcType="VARCHAR" property="promotedate" />
    <result column="BXCOMMISSIONWAY" jdbcType="VARCHAR" property="bxcommissionway" />
    <result column="BEISENID" jdbcType="VARCHAR" property="beisenid" />
    <result column="PROBATION_RESULT_12M" jdbcType="VARCHAR" property="probationResult12m" />
    <result column="CENTER_ORG" jdbcType="VARCHAR" property="centerOrg" />
    <result column="ADJUST_SERVING_MONTH" jdbcType="DECIMAL" property="adjustServingMonth" />
    <result column="ADJUST_MANAGE_SERVING_MONTH" jdbcType="DECIMAL" property="adjustManageServingMonth" />
    <result column="DT3M" jdbcType="VARCHAR" property="dt3m" />
    <result column="DT3M_FLAG" jdbcType="VARCHAR" property="dt3mFlag" />
    <result column="DT12M" jdbcType="VARCHAR" property="dt12m" />
    <result column="DT12M_FLAG" jdbcType="VARCHAR" property="dt12mFlag" />
    <result column="REGULARDT_FLAG" jdbcType="VARCHAR" property="regulardtFlag" />
    <result column="NEXTTESTDATE_FLAG" jdbcType="VARCHAR" property="nexttestdateFlag" />
    <result column="NEXT_TEST_PERIOD" jdbcType="VARCHAR" property="nextTestPeriod" />
    <result column="PROBATION_SALARY_3M" jdbcType="DECIMAL" property="probationSalary3m" />
    <result column="SALARY_12M" jdbcType="DECIMAL" property="salary12m" />
    <result column="JOIN_RANK" jdbcType="VARCHAR" property="joinRank" />
    <result column="PROBATION_RANK_3M" jdbcType="VARCHAR" property="probationRank3m" />
    <result column="REGULAR_RANK" jdbcType="VARCHAR" property="regularRank" />
    <result column="RANK_12M" jdbcType="VARCHAR" property="rank12m" />
    <result column="JOIN_SSB" jdbcType="VARCHAR" property="joinSsb" />
    <result column="PROBATION_SSB_3M" jdbcType="VARCHAR" property="probationSsb3m" />
    <result column="REGULAR_SSB" jdbcType="VARCHAR" property="regularSsb" />
    <result column="SSB_12M" jdbcType="VARCHAR" property="ssb12m" />
    <result column="PROBATIONLEVEL_3M" jdbcType="VARCHAR" property="probationlevel3m" />
    <result column="TESTLEVEL_12M" jdbcType="VARCHAR" property="testlevel12m" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    USERID, USERNO, PROVCODE, CITYCODE, GENDER, BIRTHDAY, EDULEVEL, WORKTYPE, WORKSTATE, 
    USERLEVEL, CURMONTHLEVEL, STARTDT, STARTLEVEL, SALARY, PROBATIONENDDT, REGULARDT, 
    REGULARLEVEL, REGULARSALARY, QUITDT, QUITLEVEL, QUITSALARY, QUITREASON, SERVINGAGE, 
    CHECKFLAG, CHECKOR, JJCARDNO, ATTACHTYPE, BACKGROUND, "SOURCE", BEFOREPOSITIONTYPE, 
    BEFOREPOSITIONAGE, RECRUIT, RECOMMEND, RECOMMENDTYPE, REMARK, CREATOR, CREATDT, MODOR, 
    MODDT, EMAIL, TEAMCODE, OUTLETCODE, CONSNAME, RECOMMENDUSERNO, SUBPOSITIONS, NEXTTESTDATE, 
    PROBATIONRESULT3M, PROBATIONRESULT6M, CURMONTHSALARY, QUITINFO, PROMOTEDATE, BXCOMMISSIONWAY, 
    BEISENID, PROBATION_RESULT_12M, CENTER_ORG, ADJUST_SERVING_MONTH, ADJUST_MANAGE_SERVING_MONTH, 
    DT3M, DT3M_FLAG, DT12M, DT12M_FLAG, REGULARDT_FLAG, NEXTTESTDATE_FLAG, NEXT_TEST_PERIOD, 
    PROBATION_SALARY_3M, SALARY_12M, JOIN_RANK, PROBATION_RANK_3M, REGULAR_RANK, RANK_12M, 
    JOIN_SSB, PROBATION_SSB_3M, REGULAR_SSB, SSB_12M, PROBATIONLEVEL_3M, TESTLEVEL_12M
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_CONSULTANT_EXP
    where USERID = #{userid,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CM_CONSULTANT_EXP
    where USERID = #{userid,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.cs.task.model.CmConsultantExp">
    <!--@mbg.generated-->
    insert into CM_CONSULTANT_EXP (USERID, USERNO, PROVCODE, 
      CITYCODE, GENDER, BIRTHDAY, 
      EDULEVEL, WORKTYPE, WORKSTATE, 
      USERLEVEL, CURMONTHLEVEL, STARTDT, 
      STARTLEVEL, SALARY, PROBATIONENDDT, 
      REGULARDT, REGULARLEVEL, REGULARSALARY, 
      QUITDT, QUITLEVEL, QUITSALARY, 
      QUITREASON, SERVINGAGE, CHECKFLAG, 
      CHECKOR, JJCARDNO, ATTACHTYPE, 
      BACKGROUND, "SOURCE", BEFOREPOSITIONTYPE, 
      BEFOREPOSITIONAGE, RECRUIT, RECOMMEND, 
      RECOMMENDTYPE, REMARK, CREATOR, 
      CREATDT, MODOR, MODDT, 
      EMAIL, TEAMCODE, OUTLETCODE, 
      CONSNAME, RECOMMENDUSERNO, SUBPOSITIONS, 
      NEXTTESTDATE, PROBATIONRESULT3M, PROBATIONRESULT6M, 
      CURMONTHSALARY, QUITINFO, PROMOTEDATE, 
      BXCOMMISSIONWAY, BEISENID, PROBATION_RESULT_12M, 
      CENTER_ORG, ADJUST_SERVING_MONTH, ADJUST_MANAGE_SERVING_MONTH, 
      DT3M, DT3M_FLAG, DT12M, 
      DT12M_FLAG, REGULARDT_FLAG, NEXTTESTDATE_FLAG, 
      NEXT_TEST_PERIOD, PROBATION_SALARY_3M, SALARY_12M, 
      JOIN_RANK, PROBATION_RANK_3M, REGULAR_RANK, 
      RANK_12M, JOIN_SSB, PROBATION_SSB_3M, 
      REGULAR_SSB, SSB_12M, PROBATIONLEVEL_3M, 
      TESTLEVEL_12M)
    values (#{userid,jdbcType=VARCHAR}, #{userno,jdbcType=VARCHAR}, #{provcode,jdbcType=VARCHAR}, 
      #{citycode,jdbcType=VARCHAR}, #{gender,jdbcType=VARCHAR}, #{birthday,jdbcType=VARCHAR}, 
      #{edulevel,jdbcType=VARCHAR}, #{worktype,jdbcType=VARCHAR}, #{workstate,jdbcType=VARCHAR}, 
      #{userlevel,jdbcType=VARCHAR}, #{curmonthlevel,jdbcType=VARCHAR}, #{startdt,jdbcType=VARCHAR}, 
      #{startlevel,jdbcType=VARCHAR}, #{salary,jdbcType=DECIMAL}, #{probationenddt,jdbcType=VARCHAR}, 
      #{regulardt,jdbcType=VARCHAR}, #{regularlevel,jdbcType=VARCHAR}, #{regularsalary,jdbcType=DECIMAL}, 
      #{quitdt,jdbcType=VARCHAR}, #{quitlevel,jdbcType=VARCHAR}, #{quitsalary,jdbcType=DECIMAL}, 
      #{quitreason,jdbcType=VARCHAR}, #{servingage,jdbcType=DECIMAL}, #{checkflag,jdbcType=VARCHAR}, 
      #{checkor,jdbcType=VARCHAR}, #{jjcardno,jdbcType=VARCHAR}, #{attachtype,jdbcType=VARCHAR}, 
      #{background,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, #{beforepositiontype,jdbcType=VARCHAR}, 
      #{beforepositionage,jdbcType=VARCHAR}, #{recruit,jdbcType=VARCHAR}, #{recommend,jdbcType=VARCHAR}, 
      #{recommendtype,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{creatdt,jdbcType=TIMESTAMP}, #{modor,jdbcType=VARCHAR}, #{moddt,jdbcType=TIMESTAMP}, 
      #{email,jdbcType=VARCHAR}, #{teamcode,jdbcType=VARCHAR}, #{outletcode,jdbcType=VARCHAR}, 
      #{consname,jdbcType=VARCHAR}, #{recommenduserno,jdbcType=VARCHAR}, #{subpositions,jdbcType=VARCHAR}, 
      #{nexttestdate,jdbcType=VARCHAR}, #{probationresult3m,jdbcType=VARCHAR}, #{probationresult6m,jdbcType=VARCHAR}, 
      #{curmonthsalary,jdbcType=DECIMAL}, #{quitinfo,jdbcType=VARCHAR}, #{promotedate,jdbcType=VARCHAR}, 
      #{bxcommissionway,jdbcType=VARCHAR}, #{beisenid,jdbcType=VARCHAR}, #{probationResult12m,jdbcType=VARCHAR}, 
      #{centerOrg,jdbcType=VARCHAR}, #{adjustServingMonth,jdbcType=DECIMAL}, #{adjustManageServingMonth,jdbcType=DECIMAL}, 
      #{dt3m,jdbcType=VARCHAR}, #{dt3mFlag,jdbcType=VARCHAR}, #{dt12m,jdbcType=VARCHAR}, 
      #{dt12mFlag,jdbcType=VARCHAR}, #{regulardtFlag,jdbcType=VARCHAR}, #{nexttestdateFlag,jdbcType=VARCHAR}, 
      #{nextTestPeriod,jdbcType=VARCHAR}, #{probationSalary3m,jdbcType=DECIMAL}, #{salary12m,jdbcType=DECIMAL}, 
      #{joinRank,jdbcType=VARCHAR}, #{probationRank3m,jdbcType=VARCHAR}, #{regularRank,jdbcType=VARCHAR}, 
      #{rank12m,jdbcType=VARCHAR}, #{joinSsb,jdbcType=VARCHAR}, #{probationSsb3m,jdbcType=VARCHAR}, 
      #{regularSsb,jdbcType=VARCHAR}, #{ssb12m,jdbcType=VARCHAR}, #{probationlevel3m,jdbcType=VARCHAR}, 
      #{testlevel12m,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.cs.task.model.CmConsultantExp">
    <!--@mbg.generated-->
    insert into CM_CONSULTANT_EXP
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userid != null">
        USERID,
      </if>
      <if test="userno != null">
        USERNO,
      </if>
      <if test="provcode != null">
        PROVCODE,
      </if>
      <if test="citycode != null">
        CITYCODE,
      </if>
      <if test="gender != null">
        GENDER,
      </if>
      <if test="birthday != null">
        BIRTHDAY,
      </if>
      <if test="edulevel != null">
        EDULEVEL,
      </if>
      <if test="worktype != null">
        WORKTYPE,
      </if>
      <if test="workstate != null">
        WORKSTATE,
      </if>
      <if test="userlevel != null">
        USERLEVEL,
      </if>
      <if test="curmonthlevel != null">
        CURMONTHLEVEL,
      </if>
      <if test="startdt != null">
        STARTDT,
      </if>
      <if test="startlevel != null">
        STARTLEVEL,
      </if>
      <if test="salary != null">
        SALARY,
      </if>
      <if test="probationenddt != null">
        PROBATIONENDDT,
      </if>
      <if test="regulardt != null">
        REGULARDT,
      </if>
      <if test="regularlevel != null">
        REGULARLEVEL,
      </if>
      <if test="regularsalary != null">
        REGULARSALARY,
      </if>
      <if test="quitdt != null">
        QUITDT,
      </if>
      <if test="quitlevel != null">
        QUITLEVEL,
      </if>
      <if test="quitsalary != null">
        QUITSALARY,
      </if>
      <if test="quitreason != null">
        QUITREASON,
      </if>
      <if test="servingage != null">
        SERVINGAGE,
      </if>
      <if test="checkflag != null">
        CHECKFLAG,
      </if>
      <if test="checkor != null">
        CHECKOR,
      </if>
      <if test="jjcardno != null">
        JJCARDNO,
      </if>
      <if test="attachtype != null">
        ATTACHTYPE,
      </if>
      <if test="background != null">
        BACKGROUND,
      </if>
      <if test="source != null">
        "SOURCE",
      </if>
      <if test="beforepositiontype != null">
        BEFOREPOSITIONTYPE,
      </if>
      <if test="beforepositionage != null">
        BEFOREPOSITIONAGE,
      </if>
      <if test="recruit != null">
        RECRUIT,
      </if>
      <if test="recommend != null">
        RECOMMEND,
      </if>
      <if test="recommendtype != null">
        RECOMMENDTYPE,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatdt != null">
        CREATDT,
      </if>
      <if test="modor != null">
        MODOR,
      </if>
      <if test="moddt != null">
        MODDT,
      </if>
      <if test="email != null">
        EMAIL,
      </if>
      <if test="teamcode != null">
        TEAMCODE,
      </if>
      <if test="outletcode != null">
        OUTLETCODE,
      </if>
      <if test="consname != null">
        CONSNAME,
      </if>
      <if test="recommenduserno != null">
        RECOMMENDUSERNO,
      </if>
      <if test="subpositions != null">
        SUBPOSITIONS,
      </if>
      <if test="nexttestdate != null">
        NEXTTESTDATE,
      </if>
      <if test="probationresult3m != null">
        PROBATIONRESULT3M,
      </if>
      <if test="probationresult6m != null">
        PROBATIONRESULT6M,
      </if>
      <if test="curmonthsalary != null">
        CURMONTHSALARY,
      </if>
      <if test="quitinfo != null">
        QUITINFO,
      </if>
      <if test="promotedate != null">
        PROMOTEDATE,
      </if>
      <if test="bxcommissionway != null">
        BXCOMMISSIONWAY,
      </if>
      <if test="beisenid != null">
        BEISENID,
      </if>
      <if test="probationResult12m != null">
        PROBATION_RESULT_12M,
      </if>
      <if test="centerOrg != null">
        CENTER_ORG,
      </if>
      <if test="adjustServingMonth != null">
        ADJUST_SERVING_MONTH,
      </if>
      <if test="adjustManageServingMonth != null">
        ADJUST_MANAGE_SERVING_MONTH,
      </if>
      <if test="dt3m != null">
        DT3M,
      </if>
      <if test="dt3mFlag != null">
        DT3M_FLAG,
      </if>
      <if test="dt12m != null">
        DT12M,
      </if>
      <if test="dt12mFlag != null">
        DT12M_FLAG,
      </if>
      <if test="regulardtFlag != null">
        REGULARDT_FLAG,
      </if>
      <if test="nexttestdateFlag != null">
        NEXTTESTDATE_FLAG,
      </if>
      <if test="nextTestPeriod != null">
        NEXT_TEST_PERIOD,
      </if>
      <if test="probationSalary3m != null">
        PROBATION_SALARY_3M,
      </if>
      <if test="salary12m != null">
        SALARY_12M,
      </if>
      <if test="joinRank != null">
        JOIN_RANK,
      </if>
      <if test="probationRank3m != null">
        PROBATION_RANK_3M,
      </if>
      <if test="regularRank != null">
        REGULAR_RANK,
      </if>
      <if test="rank12m != null">
        RANK_12M,
      </if>
      <if test="joinSsb != null">
        JOIN_SSB,
      </if>
      <if test="probationSsb3m != null">
        PROBATION_SSB_3M,
      </if>
      <if test="regularSsb != null">
        REGULAR_SSB,
      </if>
      <if test="ssb12m != null">
        SSB_12M,
      </if>
      <if test="probationlevel3m != null">
        PROBATIONLEVEL_3M,
      </if>
      <if test="testlevel12m != null">
        TESTLEVEL_12M,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userid != null">
        #{userid,jdbcType=VARCHAR},
      </if>
      <if test="userno != null">
        #{userno,jdbcType=VARCHAR},
      </if>
      <if test="provcode != null">
        #{provcode,jdbcType=VARCHAR},
      </if>
      <if test="citycode != null">
        #{citycode,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=VARCHAR},
      </if>
      <if test="edulevel != null">
        #{edulevel,jdbcType=VARCHAR},
      </if>
      <if test="worktype != null">
        #{worktype,jdbcType=VARCHAR},
      </if>
      <if test="workstate != null">
        #{workstate,jdbcType=VARCHAR},
      </if>
      <if test="userlevel != null">
        #{userlevel,jdbcType=VARCHAR},
      </if>
      <if test="curmonthlevel != null">
        #{curmonthlevel,jdbcType=VARCHAR},
      </if>
      <if test="startdt != null">
        #{startdt,jdbcType=VARCHAR},
      </if>
      <if test="startlevel != null">
        #{startlevel,jdbcType=VARCHAR},
      </if>
      <if test="salary != null">
        #{salary,jdbcType=DECIMAL},
      </if>
      <if test="probationenddt != null">
        #{probationenddt,jdbcType=VARCHAR},
      </if>
      <if test="regulardt != null">
        #{regulardt,jdbcType=VARCHAR},
      </if>
      <if test="regularlevel != null">
        #{regularlevel,jdbcType=VARCHAR},
      </if>
      <if test="regularsalary != null">
        #{regularsalary,jdbcType=DECIMAL},
      </if>
      <if test="quitdt != null">
        #{quitdt,jdbcType=VARCHAR},
      </if>
      <if test="quitlevel != null">
        #{quitlevel,jdbcType=VARCHAR},
      </if>
      <if test="quitsalary != null">
        #{quitsalary,jdbcType=DECIMAL},
      </if>
      <if test="quitreason != null">
        #{quitreason,jdbcType=VARCHAR},
      </if>
      <if test="servingage != null">
        #{servingage,jdbcType=DECIMAL},
      </if>
      <if test="checkflag != null">
        #{checkflag,jdbcType=VARCHAR},
      </if>
      <if test="checkor != null">
        #{checkor,jdbcType=VARCHAR},
      </if>
      <if test="jjcardno != null">
        #{jjcardno,jdbcType=VARCHAR},
      </if>
      <if test="attachtype != null">
        #{attachtype,jdbcType=VARCHAR},
      </if>
      <if test="background != null">
        #{background,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="beforepositiontype != null">
        #{beforepositiontype,jdbcType=VARCHAR},
      </if>
      <if test="beforepositionage != null">
        #{beforepositionage,jdbcType=VARCHAR},
      </if>
      <if test="recruit != null">
        #{recruit,jdbcType=VARCHAR},
      </if>
      <if test="recommend != null">
        #{recommend,jdbcType=VARCHAR},
      </if>
      <if test="recommendtype != null">
        #{recommendtype,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="creatdt != null">
        #{creatdt,jdbcType=TIMESTAMP},
      </if>
      <if test="modor != null">
        #{modor,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        #{moddt,jdbcType=TIMESTAMP},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="teamcode != null">
        #{teamcode,jdbcType=VARCHAR},
      </if>
      <if test="outletcode != null">
        #{outletcode,jdbcType=VARCHAR},
      </if>
      <if test="consname != null">
        #{consname,jdbcType=VARCHAR},
      </if>
      <if test="recommenduserno != null">
        #{recommenduserno,jdbcType=VARCHAR},
      </if>
      <if test="subpositions != null">
        #{subpositions,jdbcType=VARCHAR},
      </if>
      <if test="nexttestdate != null">
        #{nexttestdate,jdbcType=VARCHAR},
      </if>
      <if test="probationresult3m != null">
        #{probationresult3m,jdbcType=VARCHAR},
      </if>
      <if test="probationresult6m != null">
        #{probationresult6m,jdbcType=VARCHAR},
      </if>
      <if test="curmonthsalary != null">
        #{curmonthsalary,jdbcType=DECIMAL},
      </if>
      <if test="quitinfo != null">
        #{quitinfo,jdbcType=VARCHAR},
      </if>
      <if test="promotedate != null">
        #{promotedate,jdbcType=VARCHAR},
      </if>
      <if test="bxcommissionway != null">
        #{bxcommissionway,jdbcType=VARCHAR},
      </if>
      <if test="beisenid != null">
        #{beisenid,jdbcType=VARCHAR},
      </if>
      <if test="probationResult12m != null">
        #{probationResult12m,jdbcType=VARCHAR},
      </if>
      <if test="centerOrg != null">
        #{centerOrg,jdbcType=VARCHAR},
      </if>
      <if test="adjustServingMonth != null">
        #{adjustServingMonth,jdbcType=DECIMAL},
      </if>
      <if test="adjustManageServingMonth != null">
        #{adjustManageServingMonth,jdbcType=DECIMAL},
      </if>
      <if test="dt3m != null">
        #{dt3m,jdbcType=VARCHAR},
      </if>
      <if test="dt3mFlag != null">
        #{dt3mFlag,jdbcType=VARCHAR},
      </if>
      <if test="dt12m != null">
        #{dt12m,jdbcType=VARCHAR},
      </if>
      <if test="dt12mFlag != null">
        #{dt12mFlag,jdbcType=VARCHAR},
      </if>
      <if test="regulardtFlag != null">
        #{regulardtFlag,jdbcType=VARCHAR},
      </if>
      <if test="nexttestdateFlag != null">
        #{nexttestdateFlag,jdbcType=VARCHAR},
      </if>
      <if test="nextTestPeriod != null">
        #{nextTestPeriod,jdbcType=VARCHAR},
      </if>
      <if test="probationSalary3m != null">
        #{probationSalary3m,jdbcType=DECIMAL},
      </if>
      <if test="salary12m != null">
        #{salary12m,jdbcType=DECIMAL},
      </if>
      <if test="joinRank != null">
        #{joinRank,jdbcType=VARCHAR},
      </if>
      <if test="probationRank3m != null">
        #{probationRank3m,jdbcType=VARCHAR},
      </if>
      <if test="regularRank != null">
        #{regularRank,jdbcType=VARCHAR},
      </if>
      <if test="rank12m != null">
        #{rank12m,jdbcType=VARCHAR},
      </if>
      <if test="joinSsb != null">
        #{joinSsb,jdbcType=VARCHAR},
      </if>
      <if test="probationSsb3m != null">
        #{probationSsb3m,jdbcType=VARCHAR},
      </if>
      <if test="regularSsb != null">
        #{regularSsb,jdbcType=VARCHAR},
      </if>
      <if test="ssb12m != null">
        #{ssb12m,jdbcType=VARCHAR},
      </if>
      <if test="probationlevel3m != null">
        #{probationlevel3m,jdbcType=VARCHAR},
      </if>
      <if test="testlevel12m != null">
        #{testlevel12m,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.cs.task.model.CmConsultantExp">
    <!--@mbg.generated-->
    update CM_CONSULTANT_EXP
    <set>
      <if test="userno != null">
        USERNO = #{userno,jdbcType=VARCHAR},
      </if>
      <if test="provcode != null">
        PROVCODE = #{provcode,jdbcType=VARCHAR},
      </if>
      <if test="citycode != null">
        CITYCODE = #{citycode,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        GENDER = #{gender,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        BIRTHDAY = #{birthday,jdbcType=VARCHAR},
      </if>
      <if test="edulevel != null">
        EDULEVEL = #{edulevel,jdbcType=VARCHAR},
      </if>
      <if test="worktype != null">
        WORKTYPE = #{worktype,jdbcType=VARCHAR},
      </if>
      <if test="workstate != null">
        WORKSTATE = #{workstate,jdbcType=VARCHAR},
      </if>
      <if test="userlevel != null">
        USERLEVEL = #{userlevel,jdbcType=VARCHAR},
      </if>
      <if test="curmonthlevel != null">
        CURMONTHLEVEL = #{curmonthlevel,jdbcType=VARCHAR},
      </if>
      <if test="startdt != null">
        STARTDT = #{startdt,jdbcType=VARCHAR},
      </if>
      <if test="startlevel != null">
        STARTLEVEL = #{startlevel,jdbcType=VARCHAR},
      </if>
      <if test="salary != null">
        SALARY = #{salary,jdbcType=DECIMAL},
      </if>
      <if test="probationenddt != null">
        PROBATIONENDDT = #{probationenddt,jdbcType=VARCHAR},
      </if>
      <if test="regulardt != null">
        REGULARDT = #{regulardt,jdbcType=VARCHAR},
      </if>
      <if test="regularlevel != null">
        REGULARLEVEL = #{regularlevel,jdbcType=VARCHAR},
      </if>
      <if test="regularsalary != null">
        REGULARSALARY = #{regularsalary,jdbcType=DECIMAL},
      </if>
      <if test="quitdt != null">
        QUITDT = #{quitdt,jdbcType=VARCHAR},
      </if>
      <if test="quitlevel != null">
        QUITLEVEL = #{quitlevel,jdbcType=VARCHAR},
      </if>
      <if test="quitsalary != null">
        QUITSALARY = #{quitsalary,jdbcType=DECIMAL},
      </if>
      <if test="quitreason != null">
        QUITREASON = #{quitreason,jdbcType=VARCHAR},
      </if>
      <if test="servingage != null">
        SERVINGAGE = #{servingage,jdbcType=DECIMAL},
      </if>
      <if test="checkflag != null">
        CHECKFLAG = #{checkflag,jdbcType=VARCHAR},
      </if>
      <if test="checkor != null">
        CHECKOR = #{checkor,jdbcType=VARCHAR},
      </if>
      <if test="jjcardno != null">
        JJCARDNO = #{jjcardno,jdbcType=VARCHAR},
      </if>
      <if test="attachtype != null">
        ATTACHTYPE = #{attachtype,jdbcType=VARCHAR},
      </if>
      <if test="background != null">
        BACKGROUND = #{background,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        "SOURCE" = #{source,jdbcType=VARCHAR},
      </if>
      <if test="beforepositiontype != null">
        BEFOREPOSITIONTYPE = #{beforepositiontype,jdbcType=VARCHAR},
      </if>
      <if test="beforepositionage != null">
        BEFOREPOSITIONAGE = #{beforepositionage,jdbcType=VARCHAR},
      </if>
      <if test="recruit != null">
        RECRUIT = #{recruit,jdbcType=VARCHAR},
      </if>
      <if test="recommend != null">
        RECOMMEND = #{recommend,jdbcType=VARCHAR},
      </if>
      <if test="recommendtype != null">
        RECOMMENDTYPE = #{recommendtype,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="creatdt != null">
        CREATDT = #{creatdt,jdbcType=TIMESTAMP},
      </if>
      <if test="modor != null">
        MODOR = #{modor,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        MODDT = #{moddt,jdbcType=TIMESTAMP},
      </if>
      <if test="email != null">
        EMAIL = #{email,jdbcType=VARCHAR},
      </if>
      <if test="teamcode != null">
        TEAMCODE = #{teamcode,jdbcType=VARCHAR},
      </if>
      <if test="outletcode != null">
        OUTLETCODE = #{outletcode,jdbcType=VARCHAR},
      </if>
      <if test="consname != null">
        CONSNAME = #{consname,jdbcType=VARCHAR},
      </if>
      <if test="recommenduserno != null">
        RECOMMENDUSERNO = #{recommenduserno,jdbcType=VARCHAR},
      </if>
      <if test="subpositions != null">
        SUBPOSITIONS = #{subpositions,jdbcType=VARCHAR},
      </if>
      <if test="nexttestdate != null">
        NEXTTESTDATE = #{nexttestdate,jdbcType=VARCHAR},
      </if>
      <if test="probationresult3m != null">
        PROBATIONRESULT3M = #{probationresult3m,jdbcType=VARCHAR},
      </if>
      <if test="probationresult6m != null">
        PROBATIONRESULT6M = #{probationresult6m,jdbcType=VARCHAR},
      </if>
      <if test="curmonthsalary != null">
        CURMONTHSALARY = #{curmonthsalary,jdbcType=DECIMAL},
      </if>
      <if test="quitinfo != null">
        QUITINFO = #{quitinfo,jdbcType=VARCHAR},
      </if>
      <if test="promotedate != null">
        PROMOTEDATE = #{promotedate,jdbcType=VARCHAR},
      </if>
      <if test="bxcommissionway != null">
        BXCOMMISSIONWAY = #{bxcommissionway,jdbcType=VARCHAR},
      </if>
      <if test="beisenid != null">
        BEISENID = #{beisenid,jdbcType=VARCHAR},
      </if>
      <if test="probationResult12m != null">
        PROBATION_RESULT_12M = #{probationResult12m,jdbcType=VARCHAR},
      </if>
      <if test="centerOrg != null">
        CENTER_ORG = #{centerOrg,jdbcType=VARCHAR},
      </if>
      <if test="adjustServingMonth != null">
        ADJUST_SERVING_MONTH = #{adjustServingMonth,jdbcType=DECIMAL},
      </if>
      <if test="adjustManageServingMonth != null">
        ADJUST_MANAGE_SERVING_MONTH = #{adjustManageServingMonth,jdbcType=DECIMAL},
      </if>
      <if test="dt3m != null">
        DT3M = #{dt3m,jdbcType=VARCHAR},
      </if>
      <if test="dt3mFlag != null">
        DT3M_FLAG = #{dt3mFlag,jdbcType=VARCHAR},
      </if>
      <if test="dt12m != null">
        DT12M = #{dt12m,jdbcType=VARCHAR},
      </if>
      <if test="dt12mFlag != null">
        DT12M_FLAG = #{dt12mFlag,jdbcType=VARCHAR},
      </if>
      <if test="regulardtFlag != null">
        REGULARDT_FLAG = #{regulardtFlag,jdbcType=VARCHAR},
      </if>
      <if test="nexttestdateFlag != null">
        NEXTTESTDATE_FLAG = #{nexttestdateFlag,jdbcType=VARCHAR},
      </if>
      <if test="nextTestPeriod != null">
        NEXT_TEST_PERIOD = #{nextTestPeriod,jdbcType=VARCHAR},
      </if>
      <if test="probationSalary3m != null">
        PROBATION_SALARY_3M = #{probationSalary3m,jdbcType=DECIMAL},
      </if>
      <if test="salary12m != null">
        SALARY_12M = #{salary12m,jdbcType=DECIMAL},
      </if>
      <if test="joinRank != null">
        JOIN_RANK = #{joinRank,jdbcType=VARCHAR},
      </if>
      <if test="probationRank3m != null">
        PROBATION_RANK_3M = #{probationRank3m,jdbcType=VARCHAR},
      </if>
      <if test="regularRank != null">
        REGULAR_RANK = #{regularRank,jdbcType=VARCHAR},
      </if>
      <if test="rank12m != null">
        RANK_12M = #{rank12m,jdbcType=VARCHAR},
      </if>
      <if test="joinSsb != null">
        JOIN_SSB = #{joinSsb,jdbcType=VARCHAR},
      </if>
      <if test="probationSsb3m != null">
        PROBATION_SSB_3M = #{probationSsb3m,jdbcType=VARCHAR},
      </if>
      <if test="regularSsb != null">
        REGULAR_SSB = #{regularSsb,jdbcType=VARCHAR},
      </if>
      <if test="ssb12m != null">
        SSB_12M = #{ssb12m,jdbcType=VARCHAR},
      </if>
      <if test="probationlevel3m != null">
        PROBATIONLEVEL_3M = #{probationlevel3m,jdbcType=VARCHAR},
      </if>
      <if test="testlevel12m != null">
        TESTLEVEL_12M = #{testlevel12m,jdbcType=VARCHAR},
      </if>
    </set>
    where USERID = #{userid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.cs.task.model.CmConsultantExp">
    <!--@mbg.generated-->
    update CM_CONSULTANT_EXP
    set USERNO = #{userno,jdbcType=VARCHAR},
      PROVCODE = #{provcode,jdbcType=VARCHAR},
      CITYCODE = #{citycode,jdbcType=VARCHAR},
      GENDER = #{gender,jdbcType=VARCHAR},
      BIRTHDAY = #{birthday,jdbcType=VARCHAR},
      EDULEVEL = #{edulevel,jdbcType=VARCHAR},
      WORKTYPE = #{worktype,jdbcType=VARCHAR},
      WORKSTATE = #{workstate,jdbcType=VARCHAR},
      USERLEVEL = #{userlevel,jdbcType=VARCHAR},
      CURMONTHLEVEL = #{curmonthlevel,jdbcType=VARCHAR},
      STARTDT = #{startdt,jdbcType=VARCHAR},
      STARTLEVEL = #{startlevel,jdbcType=VARCHAR},
      SALARY = #{salary,jdbcType=DECIMAL},
      PROBATIONENDDT = #{probationenddt,jdbcType=VARCHAR},
      REGULARDT = #{regulardt,jdbcType=VARCHAR},
      REGULARLEVEL = #{regularlevel,jdbcType=VARCHAR},
      REGULARSALARY = #{regularsalary,jdbcType=DECIMAL},
      QUITDT = #{quitdt,jdbcType=VARCHAR},
      QUITLEVEL = #{quitlevel,jdbcType=VARCHAR},
      QUITSALARY = #{quitsalary,jdbcType=DECIMAL},
      QUITREASON = #{quitreason,jdbcType=VARCHAR},
      SERVINGAGE = #{servingage,jdbcType=DECIMAL},
      CHECKFLAG = #{checkflag,jdbcType=VARCHAR},
      CHECKOR = #{checkor,jdbcType=VARCHAR},
      JJCARDNO = #{jjcardno,jdbcType=VARCHAR},
      ATTACHTYPE = #{attachtype,jdbcType=VARCHAR},
      BACKGROUND = #{background,jdbcType=VARCHAR},
      "SOURCE" = #{source,jdbcType=VARCHAR},
      BEFOREPOSITIONTYPE = #{beforepositiontype,jdbcType=VARCHAR},
      BEFOREPOSITIONAGE = #{beforepositionage,jdbcType=VARCHAR},
      RECRUIT = #{recruit,jdbcType=VARCHAR},
      RECOMMEND = #{recommend,jdbcType=VARCHAR},
      RECOMMENDTYPE = #{recommendtype,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATDT = #{creatdt,jdbcType=TIMESTAMP},
      MODOR = #{modor,jdbcType=VARCHAR},
      MODDT = #{moddt,jdbcType=TIMESTAMP},
      EMAIL = #{email,jdbcType=VARCHAR},
      TEAMCODE = #{teamcode,jdbcType=VARCHAR},
      OUTLETCODE = #{outletcode,jdbcType=VARCHAR},
      CONSNAME = #{consname,jdbcType=VARCHAR},
      RECOMMENDUSERNO = #{recommenduserno,jdbcType=VARCHAR},
      SUBPOSITIONS = #{subpositions,jdbcType=VARCHAR},
      NEXTTESTDATE = #{nexttestdate,jdbcType=VARCHAR},
      PROBATIONRESULT3M = #{probationresult3m,jdbcType=VARCHAR},
      PROBATIONRESULT6M = #{probationresult6m,jdbcType=VARCHAR},
      CURMONTHSALARY = #{curmonthsalary,jdbcType=DECIMAL},
      QUITINFO = #{quitinfo,jdbcType=VARCHAR},
      PROMOTEDATE = #{promotedate,jdbcType=VARCHAR},
      BXCOMMISSIONWAY = #{bxcommissionway,jdbcType=VARCHAR},
      BEISENID = #{beisenid,jdbcType=VARCHAR},
      PROBATION_RESULT_12M = #{probationResult12m,jdbcType=VARCHAR},
      CENTER_ORG = #{centerOrg,jdbcType=VARCHAR},
      ADJUST_SERVING_MONTH = #{adjustServingMonth,jdbcType=DECIMAL},
      ADJUST_MANAGE_SERVING_MONTH = #{adjustManageServingMonth,jdbcType=DECIMAL},
      DT3M = #{dt3m,jdbcType=VARCHAR},
      DT3M_FLAG = #{dt3mFlag,jdbcType=VARCHAR},
      DT12M = #{dt12m,jdbcType=VARCHAR},
      DT12M_FLAG = #{dt12mFlag,jdbcType=VARCHAR},
      REGULARDT_FLAG = #{regulardtFlag,jdbcType=VARCHAR},
      NEXTTESTDATE_FLAG = #{nexttestdateFlag,jdbcType=VARCHAR},
      NEXT_TEST_PERIOD = #{nextTestPeriod,jdbcType=VARCHAR},
      PROBATION_SALARY_3M = #{probationSalary3m,jdbcType=DECIMAL},
      SALARY_12M = #{salary12m,jdbcType=DECIMAL},
      JOIN_RANK = #{joinRank,jdbcType=VARCHAR},
      PROBATION_RANK_3M = #{probationRank3m,jdbcType=VARCHAR},
      REGULAR_RANK = #{regularRank,jdbcType=VARCHAR},
      RANK_12M = #{rank12m,jdbcType=VARCHAR},
      JOIN_SSB = #{joinSsb,jdbcType=VARCHAR},
      PROBATION_SSB_3M = #{probationSsb3m,jdbcType=VARCHAR},
      REGULAR_SSB = #{regularSsb,jdbcType=VARCHAR},
      SSB_12M = #{ssb12m,jdbcType=VARCHAR},
      PROBATIONLEVEL_3M = #{probationlevel3m,jdbcType=VARCHAR},
      TESTLEVEL_12M = #{testlevel12m,jdbcType=VARCHAR}
    where USERID = #{userid,jdbcType=VARCHAR}
  </update>
</mapper>