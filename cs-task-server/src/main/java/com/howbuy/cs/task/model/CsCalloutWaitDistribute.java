package com.howbuy.cs.task.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class CsCalloutWaitDistribute implements Serializable {

    private static final long serialVersionUID = -506726136698991168L;

    private long waitId;     //等待ID
    private long taskId;  //来源ID
    private int taskType;  //来源类型
    private String waitDate;  //开始等待分配时间
    private int handleState; //处理标识符
    private int distributeFlag;  //分配标识符
    private int disposeNum;  //再分配次数
    private String disposeDate;  //在分配处理时间
    private int subTaskType;  //子来源类型
    private int orderFlag;  //预约标识符
    private String custName;  //客户姓名
    private String remark;  //备注
    private String orderUserId;  //预约座席ID
    private String mobile;
    private String email;
    private String sourceDt;
    private String bookingContent;
    private String cmsNo;
    private String consultDt;  //咨询时间
    private String mobileMask;      // 手机掩码
	private String mobileDigest;    // 手机摘要
	private String mobileCipher;    // 手机密文
	private String emailMask;       // 邮箱掩码
	private String emailDigest;     // 邮箱摘要
	private String emailCipher;     // 邮箱密文

}
