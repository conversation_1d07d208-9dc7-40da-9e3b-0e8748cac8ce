package com.howbuy.cs.task.dao;

import java.util.List;
import java.util.Map;
import org.mybatis.spring.annotation.MapperScan;
import com.howbuy.cs.task.model.CsCalloutWaitDistribute;
import com.howbuy.cs.task.model.CsTaskAssignDay;
import com.howbuy.cs.task.model.CsTaskAssignModel;
import com.howbuy.cs.task.model.CsTaskTargetSource;
import com.howbuy.cs.task.model.ValidateAgentVO;

@MapperScan
public interface AgentDao {

	/**
	 * 获取签到表中已经当天签到，并且配置表中有数据的客服
	 */
	List<CsTaskTargetSource> getValidateAgent();

	/**
	 * 获取座席以分配率升序排序(已签到座席)
	 */
	List<ValidateAgentVO> getValidateAgentByAssignRate();

	/**
	 * 获取等待时间
	 */
	List<String> getAllWaitDate();

	/**
	 * 根据等待时间获取待分配任务
	 */
	List<CsCalloutWaitDistribute> getAllWaitCsCalloutWaitDistribute(Map<String, Object> param);

	/**
	 * 获取最新的分配规则
	 */
	CsTaskAssignModel getValidDisposeMode();

	/**
	 * 更新待分配表中的待分配标识符
	 */
	Integer updateDistributeFlag();

	/**
	 * 批量插入任务今日表中
	 */
	Integer insertCsTaskAssignDayBatch(final List<CsTaskAssignDay> finishTasks);

}
