package com.howbuy.cs.task.service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.cs.task.dao.LeaveMsgCustDao;
import com.howbuy.cs.task.model.LeaveMsgCustModel;

@Service("handleLeaveMsgCustTaskService")
public class HandleLeavemsgCustTaskServiceImpl implements HandleLeaveMsgCustTaskService {
	private static final Logger log = LoggerFactory.getLogger(HandleLeavemsgCustTaskServiceImpl.class);

	@Autowired
	private LeaveMsgCustDao leaveMsgCustDao;
	
	@Autowired
	private EncryptSingleFacade encryptSingleFacade;

	/**
	 *  呼损数据处理方法:将ogg同步过来的呼损原始数据插入到呼损表
	 */
	@Override
	public void handleLeaveMsgCust(String arg) {
		log.info("呼损数据流转处理任务开始执行,接收参数：" + arg);
		
		JSONObject taskJson = JSON.parseObject(arg);
        JSONObject taskParam = taskJson.getJSONObject("taskParam");
        String startTime = taskParam == null ? null : (String) taskParam.get("startTime");
        String endTime = taskParam == null ? null : (String) taskParam.get("endTime");
        Map<String,Object> param = new HashMap<String,Object>(2);
		param.put("startTime", StringUtils.isNotBlank(startTime) ? DateUtil.string2Date(startTime,DateUtil.DEFAULT_DATEPATTERN) : null);
		param.put("endTime", StringUtils.isNotBlank(endTime) ? DateUtil.string2Date(endTime,DateUtil.DEFAULT_DATEPATTERN) : null);
        
		    //Map<String,Object> param = new HashMap<String,Object>(2);
			//param.put("startTime", DateUtil.string2Date("2018-06-01 00:00:00",DateUtil.DEFAULT_DATEPATTERN) );
			//param.put("endTime", DateUtil.string2Date("2019-01-04 18:00:00",DateUtil.DEFAULT_DATEPATTERN) );
			
			
			
			
		List<LeaveMsgCustModel> listLeaveMsgCustModel = null;
		boolean flag = true;
		int optCount= 0;//执行次数：设置单次任务 五次
		
		try {
			while(flag && optCount<5){
				//查询呼损数据：每次查询100条
				listLeaveMsgCustModel = leaveMsgCustDao.listLeaveMsgCustLimit100(param);
				//插入互损表
				int count = this.insertLeaveMsgCustBatch(listLeaveMsgCustModel);
				if(count<100){
					flag = false;
				}
				
				optCount++;
			}
		}catch (Exception e) {
			log.error("======插入呼损表时出现异常，错误信息："  + e.getMessage(),e);
			throw e;
		}
		
		
		log.info("呼损数据流转处理任务执行结束。。。");
	}

	/**
	 * 插入呼损数据
	 * @param list
	 * @return
	 */
	private int insertLeaveMsgCustBatch(List<LeaveMsgCustModel> list){
		if(CollectionUtils.isNotEmpty(list)){
			for(LeaveMsgCustModel model : list){
				String callerno = model.getCallerno();
				if(StringUtils.isNotBlank(callerno)){
					model.setCallernomask(MaskUtil.maskMobile(callerno.trim()));
					model.setCallernodigest(DigestUtil.digest(callerno.trim()));
					model.setCallernocipher(encryptSingleFacade.encrypt(callerno.trim()).getCodecText());
				}
				
			}
			leaveMsgCustDao.insertLeaveMsgCustBatch(list);
		}
		
		return CollectionUtils.isNotEmpty(list) ? list.size() : 0;
	}
}