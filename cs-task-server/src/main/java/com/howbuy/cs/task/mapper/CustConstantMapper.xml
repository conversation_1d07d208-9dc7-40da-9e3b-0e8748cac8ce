<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.cs.task.dao.CustConstantMapper">
    
    <select id="selectCustNosByConsCode" resultType="java.lang.String">
        SELECT DISTINCT CUSTNO
        FROM CM_CUSTCONSTANT
        WHERE CONSCODE = #{consCode,jdbcType=VARCHAR}
        AND STARTDT &lt; #{queryDate,jdbcType=VARCHAR}
        AND (ENDDT IS NULL OR ENDDT >= #{queryDate,jdbcType=VARCHAR})
    </select>
    
    <select id="selectValidCustNosByConsCode" resultType="java.lang.String">
        SELECT DISTINCT CUSTNO
        FROM CM_CUSTCONSTANT cc
        WHERE CONSCODE = #{consCode,jdbcType=VARCHAR}
        AND STARTDT &lt; #{lastMonthLastDay,jdbcType=VARCHAR}
        AND (ENDDT IS NULL OR ENDDT &gt;= #{lastMonthLastDay,jdbcType=VARCHAR})
        AND cc.CUSTNO NOT IN (
            SELECT DISTINCT d.cust_no
            FROM CM_STOCK_SPLIT_CONFIG_TMP c
            INNER JOIN CM_STOCK_SPLIT_DETAIL_TMP d ON c.ID = d.CONFIG_ID
            WHERE c.VALID_FLAG = '1'
            AND c.REC_STAT = '1'
            AND d.REC_STAT = '1'
            AND c.NEWLY_CONS_CODE = #{managementCode, jdbcType=VARCHAR}
            AND c.CONFIG_LEVEL = #{managementLevel,jdbcType=VARCHAR}
            AND c.CAL_START_DT &lt;= #{lastMonthLastDay,jdbcType=VARCHAR}
            and c.CAL_END_DT &gt;= #{lastMonthLastDay,jdbcType=VARCHAR}
        )
        AND EXISTS (
            SELECT 1 
            FROM CM_HIGH_CUSTINFO t 
            WHERE t.CONSCUSTNO = cc.CUSTNO
            AND (t.LSCLLABEL='1' OR t.GDCLLABEL='1')
        )
    </select>
    
    <select id="selectByCustNo" resultType="com.howbuy.cs.task.model.Custconstant">
        SELECT *
        FROM CM_CUSTCONSTANT
        WHERE CUSTNO = #{custNo,jdbcType=VARCHAR}
    </select>
    
    <select id="selectCustNameByCustNo" resultType="java.lang.String">
        SELECT cc.CUSTNAME
        FROM CM_CONSCUST cc
        WHERE cc.CONSCUSTNO = #{custNo,jdbcType=VARCHAR}
    </select>
    
</mapper> 