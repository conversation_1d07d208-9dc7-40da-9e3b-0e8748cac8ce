package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.CmStockSplitDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

/**
 * <AUTHOR>
 * @date 2024-03-26 14:35:00
 * @description 存量分成明细表Mapper接口
 */
@MapperScan
public interface CmStockSplitDetailMapper {
    /**
     * @description: 根据主键删除存量分成明细记录
     * @param id 主键ID
     * @return 删除的记录数
     * @author: hongdong.xie
     * @date: 2025-03-03 17:20:08
     */
    int deleteByPrimaryKey(String id);

    /**
     * @description: 插入存量分成明细记录
     * @param record 存量分成明细记录
     * @return 插入的记录数
     * @author: hongdong.xie
     * @date: 2025-03-03 17:20:08
     */
    int insert(CmStockSplitDetail record);

    /**
     * @description: 选择性插入存量分成明细记录
     * @param record 存量分成明细记录
     * @return 插入的记录数
     * @author: hongdong.xie
     * @date: 2025-03-03 17:20:08
     */
    int insertSelective(CmStockSplitDetail record);

    /**
     * @description: 根据主键查询存量分成明细记录
     * @param id 主键ID
     * @return 存量分成明细记录
     * @author: hongdong.xie
     * @date: 2025-03-03 17:20:08
     */
    CmStockSplitDetail selectByPrimaryKey(String id);

    /**
     * @description: 选择性更新存量分成明细记录
     * @param record 存量分成明细记录
     * @return 更新的记录数
     * @author: hongdong.xie
     * @date: 2025-03-03 17:20:08
     */
    int updateByPrimaryKeySelective(CmStockSplitDetail record);

    /**
     * @description: 更新存量分成明细记录
     * @param record 存量分成明细记录
     * @return 更新的记录数
     * @author: hongdong.xie
     * @date: 2025-03-03 17:20:08
     */
    int updateByPrimaryKey(CmStockSplitDetail record);

    /**
     * @description: 获取序列号
     * @return 序列号
     * @author: hongdong.xie
     * @date: 2025-03-03 17:20:08
     */
    String getId();

    /**
     * @description: 从临时表插入状态有效的明细数据到正式表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @author: hongdong.xie
     * @date: 2025-07-16 09:08:52
     */
    void insertValidDetailsFromTemp(@Param("startDate") String startDate, @Param("endDate") String endDate);
}