<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.cs.task.dao.AssignRuleDao">

    <insert id="updateAssginRule" parameterType="com.howbuy.cs.task.model.CsTaskAssignModel">
        insert into CS_TASK_ASSIGN_MODEL
          (modeid, modedetail, modedate, modifier, modetype)
        values
          (SEQ_CS_TASK_ASSIGN_MODEL.nextval, #{modeDetail ,jdbcType=VARCHAR}, sysdate, #{modifier,jdbcType=VARCHAR}, #{modetype,jdbcType=VARCHAR})
    </insert>

</mapper>