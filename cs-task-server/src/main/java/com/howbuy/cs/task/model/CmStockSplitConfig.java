package com.howbuy.cs.task.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 存量分成 配置表
 */
@Setter
@Getter
public class CmStockSplitConfig {
    /**
     * 主键
     */
    private String id;

    /**
     * 分成类型
     * 2：育成、
     * 3：异动-协商、
     * 4：异动-规则、
     * 5：重复划转-协商、
     * 6：重复划转-规则
     * 7: 重复划转-新老划断
     * 8: 接管-单客户
     * 9：接管-人力
     */
    private String configType;

    /**
     * 原管理层
     */
    private String formerOrgCode;

    /**
     * 原投顾
     */
    private String formerConsCode;

    /**
     * 新管理层
     */
    private String newlyOrgCode;

    /**
     * 新投顾
     */
    private String newlyConsCode;

    /**
     * 层级
     * 1-理财师、
     * 3-分总、
     * 4-区域执行副总、
     * 5-区域总、
     * 6-销售总监
     */
    private String configLevel;

    /**
     * 激活时间 yyyyMMdd
     */
    private String activeDt;

    /**
     * 激活状态:是否激活1是0否
     */
    private String activeFlag;

    /**
     * 是否生效1是0否
     */
    private String validFlag;

    /**
     * 计算起始日期 yyyyMMdd
     */
    private String calStartDt;

    /**
     * 计算结束日期 yyyyMMdd
     */
    private String calEndDt;

    /**
     * 计入比例_原
     */
    private BigDecimal formerCalRate;

    /**
     * 计入比例_新
     */
    private BigDecimal newlyCalRate;

    /**
     * 锁定存续D
     */
    private BigDecimal lockDurationAmt;

    /**
     * 审核状态 0-待审核、2-审核不通过、1-审核通过
     */
    private String auditStatus;

    /**
     * 最近一次审核状态
     */
    private String lastAuditStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTimestamp;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyTimestamp;

    /**
     * 审核人
     */
    private String auditor;

    /**
     * 审核时间
     */
    private Date auditTimestamp;

    /**
     * 记录有效状态（1-正常  0-删除）
     */
    private String recStat;


}