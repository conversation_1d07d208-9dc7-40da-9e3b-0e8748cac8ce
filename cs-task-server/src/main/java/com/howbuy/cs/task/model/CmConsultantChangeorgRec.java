package com.howbuy.cs.task.model;

import lombok.Data;
import java.util.Date;

/**
 * @description: 投顾管理层变更明细实体类
 * @author: your.name
 * @date: 2024/1/17
 */
@Data
public class CmConsultantChangeorgRec {
    /**
     * 记录号
     */
    private String recordNo;
    
    /**
     * 理财顾问代码
     */
    private String conscode;
    
    /**
     * 所属架构代码
     */
    private String teamCode;
    
    /**
     * 所属管理层代码
     */
    private String managementCode;
    
    /**
     * 管理层层级，3-分总；4-区域副；5-区域总
     */
    private String managementLevel;
    
    /**
     * 开始日期
     */
    private String startDate;
    
    /**
     * 结束日期
     */
    private String endDate;
    
    /**
     * 创建日期时间
     */
    private Date createDtm;
    
    /**
     * 更新日期时间
     */
    private Date updateDtm;
}