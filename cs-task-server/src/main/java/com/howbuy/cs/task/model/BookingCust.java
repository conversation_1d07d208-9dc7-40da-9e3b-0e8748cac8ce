package com.howbuy.cs.task.model;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 客户预约信息
 * @Date 2024/7/29 13:31
 */
public class BookingCust implements Serializable {

    private static final long serialVersionUID = -3970332766596611042L;

    /**
     * id
     */
    private String id;
    /**
     * 客户姓名
     */
    private String custName;
    /**
     * 活动类型
     */
    private String activityType;
    /**
     * 手机号码摘要
     */
    private String mobileDigest;
    /**
     * 手机号掩码
     */
    private String mobileMask;
    /**
     * 手机号密文
     */
    private String mobileCipher;
    /**
     * 邮箱摘要
     */
    private String emailDigest;
    /**
     * 邮箱掩码
     */
    private String emailMask;
    /**
     * 邮箱密文
     */
    private String emailCipher;
    /**
     * 预约时间 yyyyMMdd
     */
    private String bookingDt;
    /**
     * 预约内容
     */
    private String bookingContent;
    /**
     * 预约流水号
     */
    private String bookingSerialNo;
    /**
     * 预约详细时间
     */
    private String bookingDetailDt;
    /**
     * 分销渠道code
     */
    private String disCode;
    /**
     * 手机地区码
     */
    private String mobileAreaCode;
    /**
     * 来源系统：1-CMS;2-ACC
     */
    private String sourceSys;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public String getMobileDigest() {
        return mobileDigest;
    }

    public void setMobileDigest(String mobileDigest) {
        this.mobileDigest = mobileDigest;
    }

    public String getMobileMask() {
        return mobileMask;
    }

    public void setMobileMask(String mobileMask) {
        this.mobileMask = mobileMask;
    }

    public String getMobileCipher() {
        return mobileCipher;
    }

    public void setMobileCipher(String mobileCipher) {
        this.mobileCipher = mobileCipher;
    }

    public String getEmailDigest() {
        return emailDigest;
    }

    public void setEmailDigest(String emailDigest) {
        this.emailDigest = emailDigest;
    }

    public String getEmailMask() {
        return emailMask;
    }

    public void setEmailMask(String emailMask) {
        this.emailMask = emailMask;
    }

    public String getEmailCipher() {
        return emailCipher;
    }

    public void setEmailCipher(String emailCipher) {
        this.emailCipher = emailCipher;
    }

    public String getBookingDt() {
        return bookingDt;
    }

    public void setBookingDt(String bookingDt) {
        this.bookingDt = bookingDt;
    }

    public String getBookingContent() {
        return bookingContent;
    }

    public void setBookingContent(String bookingContent) {
        this.bookingContent = bookingContent;
    }

    public String getBookingSerialNo() {
        return bookingSerialNo;
    }

    public void setBookingSerialNo(String bookingSerialNo) {
        this.bookingSerialNo = bookingSerialNo;
    }

    public String getBookingDetailDt() {
        return bookingDetailDt;
    }

    public void setBookingDetailDt(String bookingDetailDt) {
        this.bookingDetailDt = bookingDetailDt;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getMobileAreaCode() {
        return mobileAreaCode;
    }

    public void setMobileAreaCode(String mobileAreaCode) {
        this.mobileAreaCode = mobileAreaCode;
    }

    public String getSourceSys() {
        return sourceSys;
    }

    public void setSourceSys(String sourceSys) {
        this.sourceSys = sourceSys;
    }
}
