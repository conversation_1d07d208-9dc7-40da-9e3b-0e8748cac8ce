package com.howbuy.cs.task.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.cs.task.buss.OrganizationBuss;
import com.howbuy.cs.task.dao.CmConsultantChangeorgRecMapper;
import com.howbuy.cs.task.dao.CmCustconstanthisMapper;
import com.howbuy.cs.task.dao.CmParamChangeInfoMapper;
import com.howbuy.cs.task.dao.CmStockSplitDetailMapper;
import com.howbuy.cs.task.dao.CmStockSplitConfigMapper;
import com.howbuy.cs.task.dao.CustConstantMapper;
import com.howbuy.cs.task.dao.CmStockSplitConfigTmpMapper;
import com.howbuy.cs.task.dao.CmStockSplitDetailTmpMapper;
import com.howbuy.cs.task.dao.CmConsultantMapper;
import com.howbuy.cs.task.model.*;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.Comparator;
import java.util.Objects;
import java.util.function.Function;
import java.util.Arrays;

import com.howbuy.cs.common.Constant.CrmAccountPathConstant;

/**
 * @description: 存量分成自动配置服务类
 * 主要功能：
 * 1. 处理投顾管理层变更的存量分成配置
 * 2. 处理投顾离职划转的存量分成配置
 * 3. 生成相关的客户明细数据
 * @author: hongdong.xie
 * @date: 2024-03-21 15:35:00
 */
@Slf4j
@Service
public class StockSplitAutoConfigService {

    @Autowired
    private CmConsultantChangeorgRecMapper changeorgRecMapper;
    
    @Autowired
    private CmStockSplitDetailMapper stockSplitDetailMapper;

    @Autowired
    private CmStockSplitConfigMapper stockSplitConfigMapper;

    @Autowired
    private CustConstantMapper custConstantMapper;

    @Autowired
    private CmCustconstanthisMapper cmCustconstanthisMapper;

    @Autowired
    private OrganizationBuss organizationBuss;
    
    @Autowired
    private CmParamChangeInfoMapper cmParamChangeInfoMapper;

    @Autowired
    private CmStockSplitConfigTmpMapper stockSplitConfigTmpMapper;

    @Autowired
    private CmStockSplitDetailTmpMapper stockSplitDetailTmpMapper;

    @Autowired
    private CmConsultantMapper cmConsultantMapper;

    /**
     * 批量处理的大小
     */
    private static final int BATCH_SIZE = 200;


    
    /**
     * @description 生成存量分成配置数据
     * 1. 初始化临时表数据
     * 2. 处理投顾管理层变更场景
     * 3. 处理投顾离职划转场景
     * <AUTHOR>
     * @date 2024/3/21
     */
    @Transactional(rollbackFor = Exception.class)
    public void generateStockSplitConfig() {
        // 初始化临时表数据
        initializeTempTables();
        
        // 获取上月的开始和结束日期
        String lastMonthFirstDay = getLastMonthFirstDay();
        String lastMonthLastDay = getLastMonthEndDate();

        // 处理投顾管理层变更场景
        generateManagementChangeConfig(lastMonthFirstDay, lastMonthLastDay);
        // 处理投顾离职划转场景
        generateConsultantTransferConfig(lastMonthFirstDay, lastMonthLastDay);
        
        // 处理临时表中的重复数据
        handleDuplicateCustomerData(lastMonthFirstDay, lastMonthLastDay);

        // 将本次临时表中生成的状态有效的配置和明细插入到正式表
        insertValidDataToFormalTables(lastMonthFirstDay, lastMonthLastDay);

        // 将本次生成的配置数据插入到待审核表CM_PARAM_CHANGE_INFO
//        insertToAuditTable();
    }

    /**
     * @description 将生成的存量分成配置和明细数据插入到待审核表
     * <AUTHOR>
     * @date 2024-06-29 17:20:00
     */
    private void insertToAuditTable() {
        log.info("开始将生成的存量分成配置和明细数据插入到待审核表");
        
        try {
            // 构造当天的开始时间和结束时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date startTime = calendar.getTime();
            
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            Date endTime = calendar.getTime();

            List<CmStockSplitConfigTmp> configs = stockSplitConfigTmpMapper.selectAllFromTempByDateRange(startTime, endTime);
            if (CollectionUtils.isEmpty(configs)) {
                log.info("临时表中无配置数据，无需插入待审核表");
                return;
            }
            
            log.info("查询到{}条配置数据需要插入待审核表", configs.size());
            
            // 遍历配置数据，构建审核信息
            for (CmStockSplitConfigTmp config : configs) {
                processConfigForAudit(config);
            }
            
            log.info("存量分成配置和明细数据全部插入待审核表完成");
        } catch (Exception e) {
            log.error("插入待审核表异常: {}", e.getMessage(), e);
            throw new RuntimeException("插入待审核表异常", e);
        }
    }
    
    /**
     * @description 处理单个配置的审核信息
     * @param config 配置信息
     * <AUTHOR>
     * @date 2024-06-30 10:15:00
     */
    private void processConfigForAudit(CmStockSplitConfigTmp config) {
        try {
            // 检查待审核表中是否已存在该配置记录
            CmParamChangeInfo existingRecord = cmParamChangeInfoMapper.selectByParamId(config.getId());
            if (existingRecord != null) {
                log.info("配置ID:{}在待审核表中已存在，跳过处理", config.getId());
                return;
            }

            List<CmStockSplitDetailTmp> details = stockSplitDetailTmpMapper.selectByConfigIdFromTemp(config.getId());
            if (CollectionUtils.isEmpty(details)) {
                log.warn("配置ID:{}无关联客户明细，跳过插入待审核表", config.getId());
                return;
            }
            
            // 构建审核表记录
            CmParamChangeInfo paramChangeInfo = buildParamChangeInfo(config, details);
            
            // 插入审核表
            cmParamChangeInfoMapper.insert(paramChangeInfo);
            
            log.info("配置ID:{}的数据成功插入待审核表，关联客户数:{}", config.getId(), details.size());
        } catch (Exception e) {
            log.error("处理配置ID:{}插入待审核表异常: {}", config.getId(), e.getMessage(), e);
        }
    }
    
    /**
     * @description 构建参数变更审核信息
     * @param config 配置信息
     * @param details 客户明细信息
     * @return 参数变更审核信息
     * <AUTHOR>
     * @date 2024-06-29 17:25:00
     */
    private CmParamChangeInfo buildParamChangeInfo(CmStockSplitConfigTmp config, List<CmStockSplitDetailTmp> details) {
        CmParamChangeInfo paramChangeInfo = new CmParamChangeInfo();
        
        // 设置基本信息
        paramChangeInfo.setId(stockSplitDetailMapper.getId());
        paramChangeInfo.setParamType(CrmAccountPathConstant.PARAM_TYPE_STOCK_SPLIT);
        paramChangeInfo.setParamId(config.getId());
        paramChangeInfo.setChangeType(CrmAccountPathConstant.CHANGE_TYPE_ADD);
        paramChangeInfo.setAuditStatus(CrmAccountPathConstant.AUDIT_STATUS_PENDING);
        paramChangeInfo.setRecStat(CrmAccountPathConstant.REC_STAT_VALID);
        paramChangeInfo.setCreator(CrmAccountPathConstant.SYSTEM_OPERATOR);
        paramChangeInfo.setCreateTimestamp(new Date());
        
        // 构建changeValue JSON
        Map<String, Object> changeValueMap = new HashMap<>();
        changeValueMap.put("id", config.getId());
        changeValueMap.put("configType", config.getConfigType());
        changeValueMap.put("formerOrgCode", config.getFormerOrgCode());
        changeValueMap.put("formerConsCode", config.getFormerConsCode());
        changeValueMap.put("newlyOrgCode", config.getNewlyOrgCode());
        changeValueMap.put("newlyConsCode", config.getNewlyConsCode());
        changeValueMap.put("configLevel", config.getConfigLevel());
        changeValueMap.put("activeDt", "");
        changeValueMap.put("activeFlag", CrmAccountPathConstant.ACTIVE_FLAG_INACTIVE);
        changeValueMap.put("validFlag", config.getValidFlag());
        changeValueMap.put("calStartDt", config.getCalStartDt());
        changeValueMap.put("calEndDt", config.getCalEndDt());
        changeValueMap.put("formerCalRate", "");
        changeValueMap.put("newlyCalRate", "");
        changeValueMap.put("lockDurationAmt", "");
        changeValueMap.put("auditStatus", CrmAccountPathConstant.AUDIT_STATUS_PENDING);
        
        // 处理客户明细
        List<Map<String, Object>> custDetails = new ArrayList<>();
        for (CmStockSplitDetailTmp detail : details) {
            Map<String, Object> custDetailMap = new HashMap<>();
            custDetailMap.put("custNo", detail.getCustNo());
            
            // 查询客户名称
            String custName = getCustName(detail.getCustNo());
            custDetailMap.put("custName", custName);
            
            custDetails.add(custDetailMap);
        }
        
        changeValueMap.put("detailList", custDetails);
        
        // 转换为JSON字符串
        String changeValue = JSON.toJSONString(changeValueMap);
        paramChangeInfo.setChangeValue(changeValue);
        
        return paramChangeInfo;
    }
    
    /**
     * @description 获取客户名称
     * @param custNo 客户号
     * @return 客户名称
     * <AUTHOR>
     * @date 2024-06-29 17:30:00
     */
    private String getCustName(String custNo) {
        try {
            // 根据custNo查询客户名称
            String custName = custConstantMapper.selectCustNameByCustNo(custNo);
            return StringUtils.isNotBlank(custName) ? custName : "";
        } catch (Exception e) {
            log.error("查询客户{}名称异常: {}", custNo, e.getMessage());
            return "";
        }
    }

    
    /**
     * @description 初始化临时表数据
     * <AUTHOR>
     * @date 2024/3/21
     */
    private void initializeTempTables() {
        log.info("开始初始化临时表数据");
        stockSplitConfigTmpMapper.initTempTable();
        stockSplitDetailTmpMapper.initTempTable();
        log.info("临时表数据初始化完成");
    }

    /**
     * @description 处理投顾管理层变更场景
     * 1. 获取上月变动的投顾记录
     * 2. 获取投顾名下的客户
     * 3. 检查是否存在有效客户和管理层配置
     * 4. 插入存量分成配置
     * 5. 插入存量分成客户明细
     * @param lastMonthFirstDay 上月第一天
     * @param lastMonthLastDay 上月最后一天
     * <AUTHOR>
     * @date 2024/1/17
     */
    private void generateManagementChangeConfig(String lastMonthFirstDay, String lastMonthLastDay) {
        log.info("投顾管理层变更场景开始");
        List<CmConsultantChangeorgRec> changeRecList = getLastMonthChangeRecords(lastMonthFirstDay, lastMonthLastDay);
        if (CollectionUtils.isEmpty(changeRecList)) {
            log.info("上月无投顾管理层变更记录，无需生成存量分成配置");
            return;
        }

        List<CmConsultantChangeorgRec> distinctRecords = filterDistinctRecords(changeRecList);
        log.info("去重后的投顾管理层变更记录数: {}", distinctRecords.size());

        distinctRecords.forEach(changeRec -> processConsultantChangeRecord(changeRec, lastMonthLastDay));
        log.info("投顾管理层变更场景结束");
    }

    
    /**
     * @description 处理投顾离职划转场景
     * 1. 获取上月所有划转的投顾代码列表
     * 2. 按投顾维度处理划转记录
     * 3. 生成存量分成配置和客户明细
     * @param lastMonthFirstDay 上月第一天
     * @param lastMonthLastDay 上月最后一天
     * <AUTHOR>
     * @date 2024-03-21 15:35:00
     */
    private void generateConsultantTransferConfig(String lastMonthFirstDay, String lastMonthLastDay) {
        log.info("投顾客户划转场景开始");
        
        // 获取上月所有划转的转入投顾代码列表，剔除虚拟投顾
        List<String> transferConscodes = cmCustconstanthisMapper.selectLastMonthTransferConscodes(
            lastMonthFirstDay, lastMonthLastDay);
            
        if (CollectionUtils.isEmpty(transferConscodes)) {
            log.info("上月无投顾客户划转记录，无需生成存量分成配置");
            return;
        }

        // 定义需要排除的投顾代码列表
        List<String> excludeConscodes = Arrays.asList("zhenzhen.luo", "chun.lin", "shuang.wang","yang.cao");
        
        // 过滤掉需要排除的投顾代码
        transferConscodes = transferConscodes.stream()
            .filter(conscode -> !excludeConscodes.contains(conscode))
            .collect(Collectors.toList());
            
        log.info("获取到上月发生划转入的投顾数量(排除特定投顾后)：{}", transferConscodes.size());

        // 按投顾维度处理划转记录
        for (String conscode : transferConscodes) {
            processConsultantTransfers(conscode, lastMonthFirstDay, lastMonthLastDay);
        }
        
        log.info("投顾客户划转场景结束");
    }

    /**
     * @description 处理单个投顾的划转记录
     * @param conscode 投顾代码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * <AUTHOR>
     * @date 2024/1/17
     */
    private void processConsultantTransfers(String conscode, String startDate, String endDate) {
        log.info("开始处理投顾{}的划转入记录", conscode);
        
        // 查询该投顾上个月的所有划转划入记录
        List<CmCustconstanthis> transferRecords = cmCustconstanthisMapper
            .selectLastMonthTransferRecordsByConscode(conscode, startDate, endDate);
            
        if (CollectionUtils.isEmpty(transferRecords)) {
            log.warn("投顾{}无划转入记录", conscode);
            return;
        }
        log.info("投顾{}的划转入记录数：{}", conscode, transferRecords.size());

        // 按转出投顾和转出日期分组
        Map<String, List<CmCustconstanthis>> groupedRecords = transferRecords.stream()
            .collect(Collectors.groupingBy(record -> 
                record.getConscode() + "_" + record.getEnddt()));

        // 处理每组划转记录
        for (List<CmCustconstanthis> recordGroup : groupedRecords.values()) {
            if (!CollectionUtils.isEmpty(recordGroup)) {
                processTransferRecordGroup(recordGroup, startDate, endDate);
            }
        }
        
        log.info("投顾{}的划转记录处理完成", conscode);
    }

    /**
     * @description 处理一组划转记录
     * @param recordGroup 记录组
     * @param startDate 开始日期
     * @param lastMonthEndDate 上月最后一天
     * <AUTHOR>
     * @date 2024-03-21 15:35:00
     */
    private void processTransferRecordGroup(List<CmCustconstanthis> recordGroup,String startDate, String lastMonthEndDate) {
        if (CollectionUtils.isEmpty(recordGroup)) {
            log.warn("划转记录组为空");
            return;
        }

        try {
            // 获取代表记录和新老投顾代码
            Pair<CmCustconstanthis, Pair<String, String>> recordInfo = getTransferRecordInfo(recordGroup);
            CmCustconstanthis representRecord = recordInfo.getLeft();
            String oldConsCode = recordInfo.getRight().getLeft();
            String newConsCode = recordInfo.getRight().getRight();

            // 处理虚拟投顾的情况
            if (processVirtualConsultant(oldConsCode, newConsCode, representRecord, recordGroup, lastMonthEndDate)) {
                return;
            }

            // 获取新老投顾的管理层信息
            Pair<Map<String, CmConsultantChangeorgRec>, List<CmConsultantChangeorgRec>> managementInfo = 
                getManagementInfo(oldConsCode, newConsCode, startDate, lastMonthEndDate);
            Map<String, CmConsultantChangeorgRec> oldConsultantMap = managementInfo.getLeft();
            List<CmConsultantChangeorgRec> newConsultantInfo = managementInfo.getRight();

            // 检查新投顾管理层信息
            if (CollectionUtils.isEmpty(newConsultantInfo)) {
                log.warn("新投顾{}无管理层变更记录", newConsCode);
                return;
            }

            // 比对管理层变动信息并生成配置
            generateConfigForDifferentLevels(representRecord, oldConsultantMap, newConsultantInfo, recordGroup);
            
        } catch (Exception e) {
            log.error("处理划转记录组异常, recordGroup size: {}, error: {}", 
                recordGroup.size(), e.getMessage(), e);
            throw new RuntimeException("处理划转记录组异常", e);
        }
    }

    /**
     * @description 获取划转记录信息
     * @param recordGroup 记录组
     * @return Pair<代表记录, Pair<老投顾代码, 新投顾代码>>
     * <AUTHOR>
     * @date 2024-03-21 15:35:00
     */
    private Pair<CmCustconstanthis, Pair<String, String>> getTransferRecordInfo(List<CmCustconstanthis> recordGroup) {
        CmCustconstanthis representRecord = recordGroup.get(0);
        String oldConsCode = representRecord.getConscode();
        String newConsCode = representRecord.getNextcons();
        return Pair.of(representRecord, Pair.of(oldConsCode, newConsCode));
    }

    /**
     * @description 获取新老投顾的管理层信息
     * @param oldConsCode 老投顾代码
     * @param newConsCode 新投顾代码
     * @param startDate 开始日期
     * @param lastMonthEndDate 上月最后一天
     * @return Pair<老投顾管理层Map, 新投顾管理层List>
     * <AUTHOR>
     * @date 2024-03-21 15:35:00
     */
    private Pair<Map<String, CmConsultantChangeorgRec>, List<CmConsultantChangeorgRec>> getManagementInfo(
            String oldConsCode, String newConsCode, String startDate, String lastMonthEndDate) {
        List<CmConsultantChangeorgRec> oldConsultantInfo = changeorgRecMapper.selectLatestManagementInfo(
            oldConsCode, lastMonthEndDate);
        List<CmConsultantChangeorgRec> newConsultantInfo = changeorgRecMapper.selectCurrentManagementInfo(
            newConsCode, startDate, lastMonthEndDate);

        Map<String, CmConsultantChangeorgRec> oldConsultantMap = oldConsultantInfo.stream()
            .collect(Collectors.toMap(
                CmConsultantChangeorgRec::getManagementLevel,
                Function.identity(),
                (existing, replacement) -> existing
            ));

        return Pair.of(oldConsultantMap, newConsultantInfo);
    }

    /**
     * @description 比对管理层变动信息并生成配置
     * @param representRecord 代表记录
     * @param oldConsultantMap 老投顾管理层Map
     * @param newConsultantInfo 新投顾管理层List
     * @param recordGroup 记录组
     * <AUTHOR>
     * @date 2024-03-21 15:35:00
     */
    private void generateConfigForDifferentLevels(CmCustconstanthis representRecord,
            Map<String, CmConsultantChangeorgRec> oldConsultantMap,
            List<CmConsultantChangeorgRec> newConsultantInfo,
            List<CmCustconstanthis> recordGroup) {
        List<CmConsultantChangeorgRec> differentLevels = compareManagementLevels(
            oldConsultantMap, newConsultantInfo);

        if (CollectionUtils.isEmpty(differentLevels)) {
            log.info("客户组的新老投顾管理层信息一致，无需生成配置");
            return;
        }

        log.info("客户组的新老投顾管理层差异数量:{}", differentLevels.size());

        for (CmConsultantChangeorgRec diffLevel : differentLevels) {
            generateTransferConfigAndDetails(representRecord, diffLevel, recordGroup, oldConsultantMap);
        }
    }

    /**
     * @description 生成划转场景的存量分成配置和明细
     * @param representRecord 代表记录
     * @param diffLevel 差异的管理层信息
     * @param recordGroup 记录组
     * <AUTHOR>
     * @date 2024/1/17
     */
    private void generateTransferConfigAndDetails(CmCustconstanthis representRecord, CmConsultantChangeorgRec diffLevel,
            List<CmCustconstanthis> recordGroup, Map<String, CmConsultantChangeorgRec> oldConsultantMap) {
        // 生成存量分成配置
        CmStockSplitConfigTmp config = new CmStockSplitConfigTmp();
        config.setId(stockSplitDetailMapper.getId());
        config.setConfigType(CrmAccountPathConstant.STOCK_SPLIT_CONFIG_TYPE_TAKEOVER);
        CmConsultantChangeorgRec changeorgRec = oldConsultantMap.get(diffLevel.getManagementLevel());

        // 如果转出投顾上一级或中心都查询不到，则用投顾自己的，这种情况一般不会出现
        config.setFormerConsCode(representRecord.getConscode());
        if (Objects.nonNull(changeorgRec)){
            config.setFormerOrgCode(changeorgRec.getTeamCode());
            config.setFormerConsCode(changeorgRec.getManagementCode());
        }else{
            // 如果没有获取到前一条，则取原投顾中心总
            Pair<String,String> pair =organizationBuss.getCenterLeaderUserId(representRecord.getConscode());
            if (Objects.nonNull(pair)){
                config.setFormerOrgCode(pair.getLeft());
                config.setFormerConsCode(pair.getRight());
            }
        }
        config.setNewlyOrgCode(diffLevel.getTeamCode());
        config.setNewlyConsCode(diffLevel.getManagementCode());
        config.setConfigLevel(diffLevel.getManagementLevel());
        config.setCalStartDt(representRecord.getEnddt());
        config.setCalEndDt(CrmAccountPathConstant.STOCK_SPLIT_END_DATE);
        config.setValidFlag(CrmAccountPathConstant.VALID_FLAG);
        config.setAuditStatus(CrmAccountPathConstant.AUDIT_STATUS_APPROVED);
        config.setCreator(CrmAccountPathConstant.SYSTEM_OPERATOR);
        config.setCreateTimestamp(new Date());
        config.setModifier(CrmAccountPathConstant.SYSTEM_OPERATOR);
        config.setModifyTimestamp(new Date());
        config.setRecStat(CrmAccountPathConstant.REC_STAT_VALID);
        config.setOldConsultantCode(representRecord.getConscode());
        config.setNewConsultantCode(diffLevel.getConscode());

        // 获取临时表中已存在的客户
        List<String> existingCustomers = getExistingCustomersFromTemp(config, recordGroup);

        // 过滤掉已存在的客户
        List<CmCustconstanthis> newCustomers = recordGroup.stream()
            .filter(record -> !existingCustomers.contains(record.getCustno()))
            .collect(Collectors.toList());

        log.info("投顾{}在临时表中已存在{}个客户,新增{}个客户", 
            representRecord.getConscode(), existingCustomers.size(), newCustomers.size());

        // 生成所有客户的存量分成明细
        List<CmStockSplitDetailTmp> details = newCustomers.stream()
            .map(record -> {
                CmStockSplitDetailTmp detail = new CmStockSplitDetailTmp();
                detail.setId(stockSplitDetailMapper.getId());
                detail.setConfigId(config.getId());
                detail.setCustNo(record.getCustno());
                detail.setCreator(CrmAccountPathConstant.SYSTEM_OPERATOR);
                detail.setCreateTimestamp(new Date());
                detail.setRecStat(CrmAccountPathConstant.REC_STAT_VALID);
                return detail;
            })
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(details)) {
            log.info("投顾{}无需要新增的客户记录", representRecord.getConscode());
            return;
        }

        // 插入配置到临时表
        stockSplitConfigTmpMapper.insertToTemp(config);
        // 批量插入明细到临时表
        batchInsertDetailsToTemp(details);

        log.info("管理层级{}的存量分成配置生成成功，关联客户数:{}", 
            diffLevel.getManagementLevel(), recordGroup.size());
    }

    /**
     * @description 比对新老投顾管理层信息
     * @param oldInfoMap 原投顾管理层信息Map
     * @param newInfo 新投顾管理层信息
     * @return 差异的管理层信息列表（使用新投顾的信息）
     * <AUTHOR>
     * @date 2024/1/17
     */
    private List<CmConsultantChangeorgRec> compareManagementLevels(
            Map<String, CmConsultantChangeorgRec> oldInfoMap, 
            List<CmConsultantChangeorgRec> newInfo) {
        
        // 收集差异的管理层信息
        return newInfo.stream()
            .filter(newRec -> {
                CmConsultantChangeorgRec oldRec = oldInfoMap.get(newRec.getManagementLevel());
                // 如果老的没有该层级，或者管理层编码不一致，则认为是差异数据
                return oldRec == null || !Objects.equals(
                    oldRec.getManagementCode(), newRec.getManagementCode());
            })
            .collect(Collectors.toList());
    }

    /**
     * @description 获取上月变动的投顾记录
     * @param lastMonthFirstDay 上月第一天
     * @param lastMonthLastDay 上月最后一天
     * @return 投顾变动记录列表
     * <AUTHOR>
     * @date 2024/1/17
     */
    private List<CmConsultantChangeorgRec> getLastMonthChangeRecords(String lastMonthFirstDay, String lastMonthLastDay) {
        List<CmConsultantChangeorgRec> records = changeorgRecMapper.selectLastMonthChangedRecords(
                lastMonthFirstDay, lastMonthLastDay);
        
        if (CollectionUtils.isEmpty(records)) {
            log.info("上月无投顾管理层变更记录");
        }
        return records;
    }

    /**
     * @description 对投顾变动记录进行去重，保留startDate最大的记录
     * @param records 原始记录列表
     * @return 去重后的记录列表
     * <AUTHOR>
     * @date 2024/1/17
     */
    private List<CmConsultantChangeorgRec> filterDistinctRecords(List<CmConsultantChangeorgRec> records) {
        return records.stream()
                .collect(Collectors.groupingBy(rec -> 
                    rec.getConscode() + "_" + rec.getTeamCode() + "_" + 
                    rec.getManagementCode() + "_" + rec.getManagementLevel(),
                    Collectors.collectingAndThen(
                        Collectors.maxBy(Comparator.comparing(CmConsultantChangeorgRec::getStartDate)),
                        optional -> optional.orElse(null)
                    )
                ))
                .values()
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * @description 处理单个投顾变动记录
     * @param changeRec 投顾变动记录
     * @param lastMonthLastDay 上月最后一天
     * <AUTHOR>
     * @date 2024-03-21 15:35:00
     */
    private void processConsultantChangeRecord(CmConsultantChangeorgRec changeRec, String lastMonthLastDay) {
        log.info("处理投顾管理层变更记录: {}", JSON.toJSONString(changeRec));
        
        // 获取有效客户列表
        List<String> validCustNoList = getValidCustomerList(changeRec, lastMonthLastDay);

        // 生成配置和明细
        generateConfigAndDetails(changeRec, validCustNoList);
    }

    /**
     * @description 获取需要新增配置的有效客户列表
     * @param changeRec 投顾变动记录
     * @param lastMonthLastDay 上月最后一天
     * @return 有效客户号列表
     * <AUTHOR>
     * @date 2024-03-21 15:35:00
     */
    private List<String> getValidCustomerList(CmConsultantChangeorgRec changeRec, String lastMonthLastDay) {
        List<String> validCustNoList = custConstantMapper.selectValidCustNosByConsCode(
                changeRec.getConscode(),
                changeRec.getManagementCode(),
                changeRec.getManagementLevel(),
                lastMonthLastDay);

        if (CollectionUtils.isEmpty(validCustNoList)) {
            log.info("validCustNoList 为空，投顾{}无需要新增的客户记录", changeRec.getConscode());
        }
        return validCustNoList;
    }

    /**
     * @description 生成存量分成配置和客户明细
     * @param changeRec 投顾变动记录
     * @param validCustNoList 有效客户列表
     * <AUTHOR>
     * @date 2024-03-21 15:35:00
     */
    private void generateConfigAndDetails(CmConsultantChangeorgRec changeRec, List<String> validCustNoList) {
        if (CollectionUtils.isEmpty(validCustNoList)) {
            log.info("投顾{}无有效客户，直接不生成存量分成配置", changeRec.getConscode());
            return; // 直接返回，不生成配置
        }
        
        log.info("开始生成投顾{}(管理层级:{})的存量分成配置, 变更日期:{}",
            changeRec.getConscode(), changeRec.getManagementLevel(), changeRec.getStartDate());
        
        // 插入存量分成配置到临时表
        CmStockSplitConfigTmp config = buildStockSplitConfig(changeRec);
        if (config == null) {
            log.info("投顾{}无有效客户，直接不生成存量分成配置", changeRec.getConscode());
            return;
        }

        // 分批插入存量分成客户明细到临时表
        List<CmStockSplitDetailTmp> details = buildStockSplitDetails(config, validCustNoList);
        stockSplitConfigTmpMapper.insertToTemp(config);
        batchInsertDetailsToTemp(details);
        
        log.info("投顾{}(管理层级:{})生成存量分成配置成功,新增客户数:{},变更日期:{}",
            changeRec.getConscode(), changeRec.getManagementLevel(), 
            validCustNoList.size(), changeRec.getStartDate());
    }

    /**
     * @description: 批量插入存量分成客户明细到临时表
     * @param details 存量分成客户明细列表
     * @author: hongdong.xie
     * @date: 2024-03-22 11:55:45
     */
    private void batchInsertDetailsToTemp(List<CmStockSplitDetailTmp> details) {
        if (details.size() <= BATCH_SIZE) {
            stockSplitDetailTmpMapper.batchInsertToTemp(details);
            return;
        }

        for (int i = 0; i < details.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, details.size());
            List<CmStockSplitDetailTmp> batchDetails = details.subList(i, endIndex);
            stockSplitDetailTmpMapper.batchInsertToTemp(batchDetails);
        }
    }

    /**
     * @description: 构建存量分成配置对象
     * @param changeRec 投顾管理层变更记录
     * @return 存量分成配置对象
     * @author: hongdong.xie
     * @date: 2024/1/17
     */
    private CmStockSplitConfigTmp buildStockSplitConfig(CmConsultantChangeorgRec changeRec) {
        // 获取前一条团队记录
        List<CmConsultantChangeorgRec> previousRecords = changeorgRecMapper.selectPreviousTeamRecords(
            changeRec.getConscode(),
            changeRec.getManagementLevel(),
            changeRec.getStartDate()
        );
        String formerOrgCode = null;
        // 如果投顾管理层和中心总都查不到就用投顾自己，理论上不会出现都查询不到的
        String formerConsCode = changeRec.getConscode();
        if (CollectionUtils.isEmpty(previousRecords)) {
            // 如果没有获取到前一条，则取原投顾中心总
            Pair<String,String> pair =organizationBuss.getCenterLeaderUserId(changeRec.getConscode());
            if (Objects.nonNull(pair)){
                formerOrgCode = pair.getLeft();
                formerConsCode = pair.getRight();
            }
        } else {
            // 获取第一条记录（如果存在）
            CmConsultantChangeorgRec previousRecord = previousRecords.get(0);
            formerOrgCode = previousRecord.getTeamCode();
            formerConsCode = previousRecord.getManagementCode();
        }

        // 如果 formerConsCode 和 newlyConsCode 相同，跳过生成配置
        if (Objects.equals(formerConsCode, changeRec.getManagementCode())) {
            log.info("formerConsCode 和 newlyConsCode 相同，跳过生成存量分成配置");
            // 返回 null 表示不生成配置
            return null; 
        }

        CmStockSplitConfigTmp config = new CmStockSplitConfigTmp();
        config.setId(stockSplitDetailMapper.getId());
        config.setConfigType(CrmAccountPathConstant.STOCK_SPLIT_CONFIG_TYPE_TAKEOVER);
        // 如果存在前一条记录，使用其团队编码，否则使用默认值
        config.setFormerOrgCode(formerOrgCode);
        config.setFormerConsCode(formerConsCode);
        config.setNewlyOrgCode(changeRec.getTeamCode());
        config.setNewlyConsCode(changeRec.getManagementCode());
        config.setConfigLevel(changeRec.getManagementLevel());
        config.setCalStartDt(changeRec.getStartDate());
        config.setCalEndDt(CrmAccountPathConstant.STOCK_SPLIT_END_DATE);
        config.setValidFlag(CrmAccountPathConstant.VALID_FLAG);
        config.setAuditStatus(CrmAccountPathConstant.AUDIT_STATUS_APPROVED);
        config.setCreator(CrmAccountPathConstant.SYSTEM_OPERATOR);
        config.setCreateTimestamp(new Date());
        config.setModifier(CrmAccountPathConstant.SYSTEM_OPERATOR);
        config.setModifyTimestamp(new Date());
        config.setRecStat(CrmAccountPathConstant.REC_STAT_VALID);
        config.setOldConsultantCode(changeRec.getConscode());
        config.setNewConsultantCode(changeRec.getConscode());
        return config;
    }

    /**
     * @description: 构建存量分成客户明细列表
     * @param config 存量分成配置
     * @param custNoList 客户号列表
     * @return 存量分成客户明细列表
     * @author: hongdong.xie
     * @date: 2024-03-22 11:55:45
     */
    private List<CmStockSplitDetailTmp> buildStockSplitDetails(CmStockSplitConfigTmp config, List<String> custNoList) {
        List<CmStockSplitDetailTmp> details = new ArrayList<>();
        for (String custNo : custNoList) {
            CmStockSplitDetailTmp detail = new CmStockSplitDetailTmp();
            detail.setId(stockSplitDetailMapper.getId());
            detail.setConfigId(config.getId());
            detail.setCustNo(custNo);
            detail.setCreator(CrmAccountPathConstant.SYSTEM_OPERATOR);
            detail.setCreateTimestamp(new Date());
            detail.setRecStat(CrmAccountPathConstant.REC_STAT_VALID);
            details.add(detail);
        }

        return details;
    }

    /**
     * @description: 获取上月最后一天
     * @return 上月最后一天日期字符串,格式:yyyyMMdd
     * @author: hongdong.xie
     * @date: 2024/1/17
     */
    private String getLastMonthEndDate() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, -1);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        return new SimpleDateFormat(CrmAccountPathConstant.DATE_FORMAT_PATTERN).format(cal.getTime());
    }

    /**
     * @description: 获取上月第一天
     * @return 上月第一天日期字符串,格式:yyyyMMdd
     * @author: hongdong.xie
     * @date: 2024/1/17
     */
    private String getLastMonthFirstDay() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, -1);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        return new SimpleDateFormat(CrmAccountPathConstant.DATE_FORMAT_PATTERN).format(cal.getTime());
    }

    /**
     * @description 从临时表中提取已存在的投顾客户关系
     * @param config 配置信息
     * @param recordGroup 记录组
     * @return 已存在的客户号列表
     * <AUTHOR>
     * @date 2024-03-22 16:35:00
     */
    private List<String> getExistingCustomersFromTemp(CmStockSplitConfigTmp config,
            List<CmCustconstanthis> recordGroup) {
        // 从recordGroup中获取所有客户号列表
        List<String> custNoList = recordGroup.stream()
                .map(CmCustconstanthis::getCustno)
                .collect(Collectors.toList());
                
        // 调用批量检查方法
        return stockSplitConfigTmpMapper.batchCheckCustomersExistInTempConfig(
                custNoList,      // 从recordGroup提取的客户号列表
                config.getNewlyConsCode(),        // 新投顾代码
                config.getConfigLevel(),     // 配置层级
                config.getCalStartDt()      // 计算开始日期
        );
    }

    /**
     * @description 处理虚拟投顾的划转记录
     * @param oldConsCode 原投顾代码
     * @param newConsCode 新投顾代码
     * @param representRecord 代表记录
     * @param recordGroup 记录组
     * @param lastMonthEndDate 上月最后一天
     * @return true表示已处理完成，false表示需要继续处理
     * <AUTHOR>
     * @date 2024-03-22 14:35:00
     */
    private boolean processVirtualConsultant(String oldConsCode, String newConsCode, 
            CmCustconstanthis representRecord, List<CmCustconstanthis> recordGroup, String lastMonthEndDate) {
        // 检查原投顾是否为虚拟投顾
        CmConsultant oldConsultant = cmConsultantMapper.selectByConsCode(oldConsCode);
        
        if (oldConsultant == null || !CrmAccountPathConstant.CONSULTANT_VIRTUAL_YES.equals(oldConsultant.getIsvirtual())) {
            return false;
        }
        
        // 如果是虚拟投顾，获取新投顾信息并比较网点编码
        CmConsultant newConsultant = cmConsultantMapper.selectByConsCode(newConsCode);
        if (Objects.equals(oldConsultant.getOutletcode(), newConsultant.getOutletcode())) {
            log.info("原投顾{}是虚拟投顾，且与新投顾{}的网点编码一致，不生成接管记录", oldConsCode, newConsCode);
            return true;
        }
        
        // 网点不一致，只生成分总的接管记录
        log.info("原投顾{}是虚拟投顾，且与新投顾{}的网点编码不一致，只生成分总的接管记录", oldConsCode, newConsCode);
        // 获取新投顾的管理层信息
        List<CmConsultantChangeorgRec> newConsultantInfo = changeorgRecMapper.selectLatestManagementInfo(
            newConsCode, lastMonthEndDate);
        
        // 过滤出分总级别的记录
        List<CmConsultantChangeorgRec> branchManagerRecords = newConsultantInfo.stream()
            .filter(rec -> CrmAccountPathConstant.MANAGEMENT_LEVEL_BRANCH_MANAGER.equals(rec.getManagementLevel()))
            .collect(Collectors.toList());
            
        if (!CollectionUtils.isEmpty(branchManagerRecords)) {
            // 生成分总级别的配置
            generateConfigForDifferentLevels(representRecord, 
                new HashMap<>(), branchManagerRecords, recordGroup);
        }
        return true;
    }

    /**
     * @description 处理临时表中相同客户号、相同管理层、同一层级的重复数据
     * 当客户号在临时表中出现多次时，保留CAL_START_DT较早的那条记录，将较晚的那条中该客户的记录REC_STAT设置为0（删除）
     * 在处理完所有重复客户后，更新所有没有有效明细的配置状态为删除
     * @param lastMonthFirstDay 上月第一天
     * @param lastMonthLastDay 上月最后一天
     * <AUTHOR>
     * @date 2024-07-01 14:30:00
     */
    private void handleDuplicateCustomerData(String lastMonthFirstDay, String lastMonthLastDay) {
        log.info("开始处理临时表中的重复客户数据，计算区间：{} - {}", lastMonthFirstDay, lastMonthLastDay);
        
        try {
            // 查询临时表中的所有客户明细
            List<Map<String, Object>> duplicateCustomers = stockSplitDetailTmpMapper.findDuplicateCustomersInTemp(lastMonthFirstDay, lastMonthLastDay);
            
            if (CollectionUtils.isEmpty(duplicateCustomers)) {
                log.info("临时表中无重复客户数据，无需处理");
                return;
            }
            
            log.info("查询到{}个重复客户数据需要处理", duplicateCustomers.size());
            
            // 遍历重复客户数据，分别处理
            for (Map<String, Object> dupCustomer : duplicateCustomers) {
                String custNo = (String) dupCustomer.get("CUST_NO");
                String configLevel = (String) dupCustomer.get("CONFIG_LEVEL");
                String newlyConsCode = (String) dupCustomer.get("NEWLY_CONS_CODE");
                
                // 获取该客户关联的所有配置ID，按CAL_START_DT排序
                List<Map<String, Object>> configDetails = stockSplitConfigTmpMapper.findConfigsByCustNoAndLevelOrderByStartDate(
                    custNo, configLevel, newlyConsCode, lastMonthFirstDay, lastMonthLastDay);
                
                if (configDetails.size() <= 1) {
                    continue; // 只有一条记录，不需要处理
                }
                
                // 保留第一条（日期最早的），对其余的配置关联的该客户明细设置为无效
                for (int i = 1; i < configDetails.size(); i++) {
                    String configId = (String) configDetails.get(i).get("ID");
                    
                    // 只将当前客户在该配置下的记录设置为无效
                    stockSplitDetailTmpMapper.updateRecStatByConfigIdAndCustNoInTemp(
                        configId, custNo, CrmAccountPathConstant.REC_STAT_INVALID);
                    
                    log.info("将重复客户[{}]在配置[{}]下的记录设置为无效，保留较早的配置", custNo, configId);
                }
            }
            
            // 处理完所有重复客户后，更新所有没有有效明细的配置状态为删除
            stockSplitConfigTmpMapper.updateConfigWithNoValidDetailsInTemp(
                lastMonthFirstDay, lastMonthLastDay, CrmAccountPathConstant.REC_STAT_INVALID);
            
            log.info("重复客户数据处理完成，已更新没有有效明细的配置状态为删除");
        } catch (Exception e) {
            log.error("处理重复客户数据异常: {}", e.getMessage(), e);
            throw new RuntimeException("处理重复客户数据异常", e);
        }
    }

    /**
     * @description 将本次临时表中生成的状态有效的配置和明细插入到正式表
     * @param lastMonthFirstDay 上月第一天
     * @param lastMonthLastDay 上月最后一天
     * <AUTHOR>
     * @date 2025-07-16 09:08:52
     */
    private void insertValidDataToFormalTables(String lastMonthFirstDay, String lastMonthLastDay) {
        log.info("开始将临时表中状态有效的配置和明细插入到正式表，计算区间：{} - {}", lastMonthFirstDay, lastMonthLastDay);

        try {
            // 直接从临时表插入配置数据到正式表
            stockSplitConfigMapper.insertValidConfigsFromTemp(lastMonthFirstDay, lastMonthLastDay);
            log.info("成功从临时表插入配置数据到正式表");

            // 直接从临时表插入明细数据到正式表
            stockSplitDetailMapper.insertValidDetailsFromTemp(lastMonthFirstDay, lastMonthLastDay);
            log.info("成功从临时表插入明细数据到正式表");

            log.info("临时表数据成功插入正式表完成");
        } catch (Exception e) {
            log.error("将临时表数据插入正式表异常", e);
            // throw new RuntimeException("将临时表数据插入正式表异常", e);
        }
    }


}