package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.CmStockSplitConfig;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;
@MapperScan
public interface CmStockSplitConfigMapper {
    int deleteByPrimaryKey(String id);

    int insert(CmStockSplitConfig record);

    int insertSelective(CmStockSplitConfig record);

    CmStockSplitConfig selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CmStockSplitConfig record);

    int updateByPrimaryKey(CmStockSplitConfig record);

    /**
     * @description: 从临时表插入状态有效的配置数据到正式表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @author: hongdong.xie
     * @date: 2025-07-16 09:08:52
     */
    void insertValidConfigsFromTemp(@Param("startDate") String startDate, @Param("endDate") String endDate);
}