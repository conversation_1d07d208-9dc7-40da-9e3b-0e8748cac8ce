package com.howbuy.cs.task.model;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 投顾HR扩展表
 */
public class CmConsultantExp {
    /**
    * 投顾编号
    */
    private String userid;

    /**
    * 工号
    */
    private String userno;

    /**
    * 省份code
    */
    private String provcode;

    /**
    * 城市code
    */
    private String citycode;

    /**
    * 性别1：男；2：女
    */
    private String gender;

    /**
    * 出生日期YYYYMMDD
    */
    private String birthday;

    /**
    * 学历：1高中及以下、2专科、3本科、4硕士、5MBA、6博士
    */
    private String edulevel;

    /**
    * 是否在职：1在职、2离职、3在途新人
    */
    private String worktype;

    /**
    * 在职状态：1正式、2试用期、3在途
    */
    private String workstate;

    /**
    * 层级：1理财师、2团队总监、3分总、4区域执行副总、5区域总、6销售总监、7中后台
    */
    private String userlevel;

    /**
    * 当月职级：1观察期理财师、2理财师1级、3理财师2级、4理财师3级、5资深理财师1级、6资深理财师2级、7资深理财师3级、8私人银行家1级、9私人银行家2级、10私人银行家3级、11私人银行家4级、12首席私行家1级、13首席私行家2级、14一级财富管理部总监、15二级财富管理部总监、16三级财富管理部总监、17四级财富管理部总监、18见习分公司总经理、19观察期分公司总经理、20一级分公司总经理、21二级分公司总经理、22三级分公司总经理、23四级分公司总经理、24区域执行副总、25观察期区域总经理、26一级区域总经理、27二级区域总经理、28三级区域总经理、29pink/Selina、30中后台、31在途、32离职、99其他
    */
    private String curmonthlevel;

    /**
    * 入职日期YYYYMMDD
    */
    private String startdt;

    /**
    * 入职职级：枚举同当月职级
    */
    private String startlevel;

    /**
    * 入职薪资
    */
    private BigDecimal salary;

    /**
    * 试用期截止日期YYYYMMDD
    */
    private String probationenddt;

    /**
    * 转正日期YYYYMMDD
    */
    private String regulardt;

    /**
    * 转正职级：枚举同当月职级
    */
    private String regularlevel;

    /**
    * 转正薪资
    */
    private BigDecimal regularsalary;

    /**
    * 离职日期YYYYMMDD
    */
    private String quitdt;

    /**
    * 离职职级：枚举同当月职级
    */
    private String quitlevel;

    /**
    * 离职薪资
    */
    private BigDecimal quitsalary;

    /**
    * 离职原因:1主动离职、2淘汰、3转岗
    */
    private String quitreason;

    /**
    * 好买司龄
    */
    private BigDecimal servingage;

    /**
    * 审核状态：1待审核；2审核通过；3审核不通过 4 已导入待修改
    */
    private String checkflag;

    /**
    * 审核人
    */
    private String checkor;

    /**
    * 基金从业资格编码
    */
    private String jjcardno;

    /**
    * 是否挂靠:1是；2否
    */
    private String attachtype;

    /**
    * 上家公司
    */
    private String background;

    /**
    * 背景来源
    */
    private String source;

    /**
    * 入职好买前职位类型
    */
    private String beforepositiontype;

    /**
    * 入职好买前工作年限
    */
    private String beforepositionage;

    /**
    * 招聘经理
    */
    private String recruit;

    /**
    * 推荐人
    */
    private String recommend;

    /**
    * 招聘渠道：1MGM、2管理层自带、3管理层自带-层级穿透、4其他
    */
    private String recommendtype;

    /**
    * 备注
    */
    private String remark;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private Date creatdt;

    /**
    * 修改人
    */
    private String modor;

    /**
    * 修改时间
    */
    private Date moddt;

    /**
    * 邮箱
    */
    private String email;

    /**
    * 所属小组
    */
    private String teamcode;

    /**
    * 所属部门
    */
    private String outletcode;

    /**
    * 投顾名称
    */
    private String consname;

    /**
    * 推荐人工号
    */
    private String recommenduserno;

    /**
    * 副职 字典subpositionslevel
    */
    private String subpositions;

    /**
    * 下次考核截止日期 YYYYMMDD
    */
    private String nexttestdate;

    /**
    * 试用3M考核结果
    */
    private String probationresult3m;

    /**
    * 试用6M考核结果
    */
    private String probationresult6m;

    /**
    * 当月薪资
    */
    private BigDecimal curmonthsalary;

    /**
    * 离职去向
    */
    private String quitinfo;

    /**
    * 管理日期
    */
    private String promotedate;

    /**
    * 投顾创新方案
    */
    private String bxcommissionway;

    /**
    * 北森ID
    */
    private String beisenid;

    /**
    * 12m考核结果
    */
    private String probationResult12m;

    /**
    * 业务中心
    */
    private String centerOrg;

    /**
    * 调整司龄(月)
    */
    private BigDecimal adjustServingMonth;

    /**
    * 调整管理司龄(月)
    */
    private BigDecimal adjustManageServingMonth;

    /**
    * 3M日期
    */
    private String dt3m;

    /**
    * 3M日期人工修改标识1是0否
    */
    private String dt3mFlag;

    /**
    * 12M日期
    */
    private String dt12m;

    /**
    * 12M日期人工修改标识1是0否
    */
    private String dt12mFlag;

    /**
    * 转正日期人工修改标识1是0否
    */
    private String regulardtFlag;

    /**
    * 下次考核截止日期人工修改标识1是0否
    */
    private String nexttestdateFlag;

    /**
    * 下次考核周期
    */
    private String nextTestPeriod;

    /**
    * 试用3M薪资
    */
    private BigDecimal probationSalary3m;

    /**
    * 12M薪资
    */
    private BigDecimal salary12m;

    /**
    * 入职档级
    */
    private String joinRank;

    /**
    * 试用3M档级
    */
    private String probationRank3m;

    /**
    * 转正档级
    */
    private String regularRank;

    /**
    * 12M档级
    */
    private String rank12m;

    /**
    * 入职社保基数
    */
    private String joinSsb;

    /**
    * 试用3M社保基数
    */
    private String probationSsb3m;

    /**
    * 转正社保基数
    */
    private String regularSsb;

    /**
    * 12M社保基数
    */
    private String ssb12m;

    /**
    * 试用3M考核职级
    */
    private String probationlevel3m;

    /**
    * 12M考核职级
    */
    private String testlevel12m;

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getUserno() {
        return userno;
    }

    public void setUserno(String userno) {
        this.userno = userno;
    }

    public String getProvcode() {
        return provcode;
    }

    public void setProvcode(String provcode) {
        this.provcode = provcode;
    }

    public String getCitycode() {
        return citycode;
    }

    public void setCitycode(String citycode) {
        this.citycode = citycode;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getEdulevel() {
        return edulevel;
    }

    public void setEdulevel(String edulevel) {
        this.edulevel = edulevel;
    }

    public String getWorktype() {
        return worktype;
    }

    public void setWorktype(String worktype) {
        this.worktype = worktype;
    }

    public String getWorkstate() {
        return workstate;
    }

    public void setWorkstate(String workstate) {
        this.workstate = workstate;
    }

    public String getUserlevel() {
        return userlevel;
    }

    public void setUserlevel(String userlevel) {
        this.userlevel = userlevel;
    }

    public String getCurmonthlevel() {
        return curmonthlevel;
    }

    public void setCurmonthlevel(String curmonthlevel) {
        this.curmonthlevel = curmonthlevel;
    }

    public String getStartdt() {
        return startdt;
    }

    public void setStartdt(String startdt) {
        this.startdt = startdt;
    }

    public String getStartlevel() {
        return startlevel;
    }

    public void setStartlevel(String startlevel) {
        this.startlevel = startlevel;
    }

    public BigDecimal getSalary() {
        return salary;
    }

    public void setSalary(BigDecimal salary) {
        this.salary = salary;
    }

    public String getProbationenddt() {
        return probationenddt;
    }

    public void setProbationenddt(String probationenddt) {
        this.probationenddt = probationenddt;
    }

    public String getRegulardt() {
        return regulardt;
    }

    public void setRegulardt(String regulardt) {
        this.regulardt = regulardt;
    }

    public String getRegularlevel() {
        return regularlevel;
    }

    public void setRegularlevel(String regularlevel) {
        this.regularlevel = regularlevel;
    }

    public BigDecimal getRegularsalary() {
        return regularsalary;
    }

    public void setRegularsalary(BigDecimal regularsalary) {
        this.regularsalary = regularsalary;
    }

    public String getQuitdt() {
        return quitdt;
    }

    public void setQuitdt(String quitdt) {
        this.quitdt = quitdt;
    }

    public String getQuitlevel() {
        return quitlevel;
    }

    public void setQuitlevel(String quitlevel) {
        this.quitlevel = quitlevel;
    }

    public BigDecimal getQuitsalary() {
        return quitsalary;
    }

    public void setQuitsalary(BigDecimal quitsalary) {
        this.quitsalary = quitsalary;
    }

    public String getQuitreason() {
        return quitreason;
    }

    public void setQuitreason(String quitreason) {
        this.quitreason = quitreason;
    }

    public BigDecimal getServingage() {
        return servingage;
    }

    public void setServingage(BigDecimal servingage) {
        this.servingage = servingage;
    }

    public String getCheckflag() {
        return checkflag;
    }

    public void setCheckflag(String checkflag) {
        this.checkflag = checkflag;
    }

    public String getCheckor() {
        return checkor;
    }

    public void setCheckor(String checkor) {
        this.checkor = checkor;
    }

    public String getJjcardno() {
        return jjcardno;
    }

    public void setJjcardno(String jjcardno) {
        this.jjcardno = jjcardno;
    }

    public String getAttachtype() {
        return attachtype;
    }

    public void setAttachtype(String attachtype) {
        this.attachtype = attachtype;
    }

    public String getBackground() {
        return background;
    }

    public void setBackground(String background) {
        this.background = background;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getBeforepositiontype() {
        return beforepositiontype;
    }

    public void setBeforepositiontype(String beforepositiontype) {
        this.beforepositiontype = beforepositiontype;
    }

    public String getBeforepositionage() {
        return beforepositionage;
    }

    public void setBeforepositionage(String beforepositionage) {
        this.beforepositionage = beforepositionage;
    }

    public String getRecruit() {
        return recruit;
    }

    public void setRecruit(String recruit) {
        this.recruit = recruit;
    }

    public String getRecommend() {
        return recommend;
    }

    public void setRecommend(String recommend) {
        this.recommend = recommend;
    }

    public String getRecommendtype() {
        return recommendtype;
    }

    public void setRecommendtype(String recommendtype) {
        this.recommendtype = recommendtype;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreatdt() {
        return creatdt;
    }

    public void setCreatdt(Date creatdt) {
        this.creatdt = creatdt;
    }

    public String getModor() {
        return modor;
    }

    public void setModor(String modor) {
        this.modor = modor;
    }

    public Date getModdt() {
        return moddt;
    }

    public void setModdt(Date moddt) {
        this.moddt = moddt;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTeamcode() {
        return teamcode;
    }

    public void setTeamcode(String teamcode) {
        this.teamcode = teamcode;
    }

    public String getOutletcode() {
        return outletcode;
    }

    public void setOutletcode(String outletcode) {
        this.outletcode = outletcode;
    }

    public String getConsname() {
        return consname;
    }

    public void setConsname(String consname) {
        this.consname = consname;
    }

    public String getRecommenduserno() {
        return recommenduserno;
    }

    public void setRecommenduserno(String recommenduserno) {
        this.recommenduserno = recommenduserno;
    }

    public String getSubpositions() {
        return subpositions;
    }

    public void setSubpositions(String subpositions) {
        this.subpositions = subpositions;
    }

    public String getNexttestdate() {
        return nexttestdate;
    }

    public void setNexttestdate(String nexttestdate) {
        this.nexttestdate = nexttestdate;
    }

    public String getProbationresult3m() {
        return probationresult3m;
    }

    public void setProbationresult3m(String probationresult3m) {
        this.probationresult3m = probationresult3m;
    }

    public String getProbationresult6m() {
        return probationresult6m;
    }

    public void setProbationresult6m(String probationresult6m) {
        this.probationresult6m = probationresult6m;
    }

    public BigDecimal getCurmonthsalary() {
        return curmonthsalary;
    }

    public void setCurmonthsalary(BigDecimal curmonthsalary) {
        this.curmonthsalary = curmonthsalary;
    }

    public String getQuitinfo() {
        return quitinfo;
    }

    public void setQuitinfo(String quitinfo) {
        this.quitinfo = quitinfo;
    }

    public String getPromotedate() {
        return promotedate;
    }

    public void setPromotedate(String promotedate) {
        this.promotedate = promotedate;
    }

    public String getBxcommissionway() {
        return bxcommissionway;
    }

    public void setBxcommissionway(String bxcommissionway) {
        this.bxcommissionway = bxcommissionway;
    }

    public String getBeisenid() {
        return beisenid;
    }

    public void setBeisenid(String beisenid) {
        this.beisenid = beisenid;
    }

    public String getProbationResult12m() {
        return probationResult12m;
    }

    public void setProbationResult12m(String probationResult12m) {
        this.probationResult12m = probationResult12m;
    }

    public String getCenterOrg() {
        return centerOrg;
    }

    public void setCenterOrg(String centerOrg) {
        this.centerOrg = centerOrg;
    }

    public BigDecimal getAdjustServingMonth() {
        return adjustServingMonth;
    }

    public void setAdjustServingMonth(BigDecimal adjustServingMonth) {
        this.adjustServingMonth = adjustServingMonth;
    }

    public BigDecimal getAdjustManageServingMonth() {
        return adjustManageServingMonth;
    }

    public void setAdjustManageServingMonth(BigDecimal adjustManageServingMonth) {
        this.adjustManageServingMonth = adjustManageServingMonth;
    }

    public String getDt3m() {
        return dt3m;
    }

    public void setDt3m(String dt3m) {
        this.dt3m = dt3m;
    }

    public String getDt3mFlag() {
        return dt3mFlag;
    }

    public void setDt3mFlag(String dt3mFlag) {
        this.dt3mFlag = dt3mFlag;
    }

    public String getDt12m() {
        return dt12m;
    }

    public void setDt12m(String dt12m) {
        this.dt12m = dt12m;
    }

    public String getDt12mFlag() {
        return dt12mFlag;
    }

    public void setDt12mFlag(String dt12mFlag) {
        this.dt12mFlag = dt12mFlag;
    }

    public String getRegulardtFlag() {
        return regulardtFlag;
    }

    public void setRegulardtFlag(String regulardtFlag) {
        this.regulardtFlag = regulardtFlag;
    }

    public String getNexttestdateFlag() {
        return nexttestdateFlag;
    }

    public void setNexttestdateFlag(String nexttestdateFlag) {
        this.nexttestdateFlag = nexttestdateFlag;
    }

    public String getNextTestPeriod() {
        return nextTestPeriod;
    }

    public void setNextTestPeriod(String nextTestPeriod) {
        this.nextTestPeriod = nextTestPeriod;
    }

    public BigDecimal getProbationSalary3m() {
        return probationSalary3m;
    }

    public void setProbationSalary3m(BigDecimal probationSalary3m) {
        this.probationSalary3m = probationSalary3m;
    }

    public BigDecimal getSalary12m() {
        return salary12m;
    }

    public void setSalary12m(BigDecimal salary12m) {
        this.salary12m = salary12m;
    }

    public String getJoinRank() {
        return joinRank;
    }

    public void setJoinRank(String joinRank) {
        this.joinRank = joinRank;
    }

    public String getProbationRank3m() {
        return probationRank3m;
    }

    public void setProbationRank3m(String probationRank3m) {
        this.probationRank3m = probationRank3m;
    }

    public String getRegularRank() {
        return regularRank;
    }

    public void setRegularRank(String regularRank) {
        this.regularRank = regularRank;
    }

    public String getRank12m() {
        return rank12m;
    }

    public void setRank12m(String rank12m) {
        this.rank12m = rank12m;
    }

    public String getJoinSsb() {
        return joinSsb;
    }

    public void setJoinSsb(String joinSsb) {
        this.joinSsb = joinSsb;
    }

    public String getProbationSsb3m() {
        return probationSsb3m;
    }

    public void setProbationSsb3m(String probationSsb3m) {
        this.probationSsb3m = probationSsb3m;
    }

    public String getRegularSsb() {
        return regularSsb;
    }

    public void setRegularSsb(String regularSsb) {
        this.regularSsb = regularSsb;
    }

    public String getSsb12m() {
        return ssb12m;
    }

    public void setSsb12m(String ssb12m) {
        this.ssb12m = ssb12m;
    }

    public String getProbationlevel3m() {
        return probationlevel3m;
    }

    public void setProbationlevel3m(String probationlevel3m) {
        this.probationlevel3m = probationlevel3m;
    }

    public String getTestlevel12m() {
        return testlevel12m;
    }

    public void setTestlevel12m(String testlevel12m) {
        this.testlevel12m = testlevel12m;
    }
}