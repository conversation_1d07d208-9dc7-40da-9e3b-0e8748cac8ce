package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.CmStockSplitDetail;
import com.howbuy.cs.task.model.CmStockSplitDetailTmp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-03-26 14:35:00
 * @description 存量分成明细临时表Mapper接口
 */
@Mapper
public interface CmStockSplitDetailTmpMapper {
    int deleteByPrimaryKey(String id);

    int insert(CmStockSplitDetailTmp record);

    int insertSelective(CmStockSplitDetailTmp record);

    CmStockSplitDetailTmp selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CmStockSplitDetailTmp record);

    int updateByPrimaryKey(CmStockSplitDetailTmp record);

    /**
     * @description 初始化临时表
     * <AUTHOR>
     * @date 2024-03-26 14:35:00
     */
    void initTempTable();

    /**
     * @description: 批量插入存量分成明细到临时表
     * @param details 存量分成明细列表
     * @author: hongdong.xie
     * @date: 2024-03-26 14:35:00
     */
    void batchInsertToTemp(@Param("list") List<CmStockSplitDetailTmp> details);

    /**
     * @description: 根据配置ID查询临时表中的客户明细
     * @param configId 配置ID
     * @return 客户明细列表
     * @author: hongdong.xie
     * @date: 2024-03-26 14:35:00
     */
    List<CmStockSplitDetailTmp> selectByConfigIdFromTemp(String configId);

    /**
     * @description: 查询临时表中的重复客户数据
     * 查找同一个客户号、同一管理层、同一层级出现多次的记录
     * @param startDate 计算开始日期
     * @param endDate 计算结束日期
     * @return 重复客户的信息（客户号、配置层级、新投顾代码）
     * @author: hongdong.xie
     * @date: 2024-07-01 14:35:00
     */
    List<Map<String, Object>> findDuplicateCustomersInTemp(@Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * @description: 根据配置ID更新临时表中的记录状态
     * @param configId 配置ID
     * @param recStat 记录状态
     * @author: hongdong.xie
     * @date: 2024-07-01 14:35:00
     */
    void updateRecStatByConfigIdInTemp(@Param("configId") String configId, @Param("recStat") String recStat);

    /**
     * @description: 根据配置ID和客户号更新临时表中的记录状态
     * @param configId 配置ID
     * @param custNo 客户号
     * @param recStat 记录状态
     * @author: hongdong.xie
     * @date: 2024-07-01 15:35:00
     */
    void updateRecStatByConfigIdAndCustNoInTemp(
        @Param("configId") String configId, 
        @Param("custNo") String custNo, 
        @Param("recStat") String recStat
    );
}