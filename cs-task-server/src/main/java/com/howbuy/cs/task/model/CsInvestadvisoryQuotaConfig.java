package com.howbuy.cs.task.model;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

@Data
public class CsInvestadvisoryQuotaConfig implements Serializable{

	private static final long serialVersionUID = -8895145444537164286L;
	
	private BigDecimal id;
	private String advisorId; 
	private String advisorName;
	private BigDecimal targetNum;
	private String numfrequency;
	private String workFlag;
	private BigDecimal levelNum;
	private int validateFlag;
	private String orgCode;
	private String orgName;
	private String createDt;
	private String modifyDt;
	private String modifierId;
	private String createrId;
	private int taskSourceType;
	private BigDecimal multiRate;   
	private BigDecimal currCustNum; 
	private BigDecimal maxCustLimit;//投顾最大分配数
	private BigDecimal usedAdvisoryQuota;//目前投顾已分配客户数
	
}
