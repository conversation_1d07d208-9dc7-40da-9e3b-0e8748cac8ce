package com.howbuy.cs.task.service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.auth.facade.response.CodecSingleResponse;
import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.client.enums.custinfo.SpecialConsCodeEnum;
import com.howbuy.crm.account.client.request.custinfo.CreateConsCustRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.CreateConsCustRespVO;
import com.howbuy.crm.base.model.CenterOrgEnum;
import com.howbuy.crm.base.model.CustVisitTypeEnum;
import com.howbuy.crm.base.response.CoreReturnMessageDto;
import com.howbuy.crm.conscust.dto.CustconstantInfoDomain;
import com.howbuy.crm.conscust.request.QueryConstantInfoRequest;
import com.howbuy.crm.conscust.request.UpdateCustconstantInfoRequest;
import com.howbuy.crm.conscust.response.QueryConstantInfoResponse;
import com.howbuy.crm.conscust.response.UpdateCustconstantInfoResponse;
import com.howbuy.crm.conscust.service.UpdateCustconstantInfoService;
import com.howbuy.crm.consultant.dto.ConsultantInfoDto;
import com.howbuy.crm.consultant.service.ConsultantInfoService;
import com.howbuy.crm.custvisit.dto.CommunicateVisitInsertVo;
import com.howbuy.crm.custvisit.service.CsCommunicateVisitService;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import com.howbuy.cs.base.Constants;
import com.howbuy.cs.bookcust.enums.BookingCustSourceSysEnum;
import com.howbuy.cs.common.Constant.CrmAccountPathConstant;
import com.howbuy.cs.bookcust.enums.CmsDisCodeEnum;
import com.howbuy.cs.task.model.BookingCust;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.dubbo.response.BaseResponse;
import crm.howbuy.base.utils.HttpUtils;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.howbuy.cs.task.dao.SourceDataDao;
import com.howbuy.cs.task.model.CsCalloutWaitDistribute;
import com.howbuy.cs.task.util.SourceDataType;
import com.howbuy.cs.task.util.ValidCalleeNO;

@Slf4j
@Service("agentDataCollectTaskService")
public class AgentDataCollectTaskServiceImpl implements AgentDataCollectTaskService {

    @Autowired
    private SourceDataDao sourceDataDao;
    @Autowired
    private ConsultantInfoService consultantInfoService;
    @Autowired
    private DecryptSingleFacade decryptSingleFacade;
    @Autowired
    private CmPushMsgService cmPushMsgService;
    @Autowired
    private CsCommunicateVisitService csCommunicateVisitService;
    @Autowired
    private UpdateCustconstantInfoService updateCustconstantInfoService;


    @Value("${GLOBAL_CRM_ACCOUNT}")
    public String crmAccountPath;

    /**
     * 同步坐席任务基础数据
     */
    @Override
    public void collectBasicData(String arg) {
        // 同步预约原始数据方法
        syncBookingCustData();

        // 同步呼损原始数据方法
        // syncCallLossCustData();

        // 同步云翌原始数据方法
        syncYunEasyCustData();
    }

    /**
     * 同步预约原始数据方法
     */
    public void syncBookingCustData() {
        log.info("同步预约数据方法开始执行。。。");
        List<BookingCust> orderDataList = sourceDataDao.getBookingCustData();
        if (CollectionUtils.isEmpty(orderDataList)) {
            log.info("同步预约数据方法执行结束。。。本次未查到待同步数据！");
            return;
        }

        // 香港渠道cms来源数据列表
        List<BookingCust> hkBookingCustList = new ArrayList<>();
        // 其他数据来源
        List<BookingCust> bookingCustList = new ArrayList<>();
        for (BookingCust bookingCust : orderDataList) {
            if (null != bookingCust && CmsDisCodeEnum.HK_CHANNEL.getCode().equals(bookingCust.getDisCode())
                    && BookingCustSourceSysEnum.CMS.getCode().equals(bookingCust.getSourceSys())) {
                hkBookingCustList.add(bookingCust);
            } else {
                bookingCustList.add(bookingCust);
            }
        }

        // 处理香港渠道预约客户数据
        for (BookingCust bookingCust : hkBookingCustList) {
            this.handleHkBookingCustData(bookingCust);
        }

        List<CsCalloutWaitDistribute> csCalloutWaitDistributeList = new LinkedList<>();
        List<Integer> idList = new LinkedList<>();
        for (BookingCust bookingCust : bookingCustList) {
            int taskID = Integer.parseInt(bookingCust.getId());
            idList.add(taskID);
            CsCalloutWaitDistribute csCalloutWaitDistribute = buildCsCalloutWaitDistribute(bookingCust);
            csCalloutWaitDistributeList.add(csCalloutWaitDistribute);
        }

        if (CollectionUtils.isNotEmpty(csCalloutWaitDistributeList)) {
            Integer result = sourceDataDao.insertBatchAddSourceData(csCalloutWaitDistributeList);
            boolean addFlag = result == csCalloutWaitDistributeList.size();
            if (addFlag) {
                log.info("预约已经将原数据表中的数据插入到待分配表中");
                boolean updateFlag = sourceDataDao.updateBookCustReadFlag(idList) >= 0;
                if (updateFlag) {
                    log.info("预约原数据表标识符已经更新好了");
                }
            } else {
                log.info("预约原数据表中的数据插入到待分配表中失败或没有数据");
            }
        }
        log.info("同步预约数据方法执行结束。。。");
    }

    /**
     * 预约客户信息数据构建 客服待分配任务数据
     *
     * @param bookingCust 预约客户数据
     * @return CsCalloutWaitDistribute
     */
    private CsCalloutWaitDistribute buildCsCalloutWaitDistribute(BookingCust bookingCust) {
        CsCalloutWaitDistribute csCalloutWaitDistribute = new CsCalloutWaitDistribute();
        csCalloutWaitDistribute.setTaskType(SourceDataType.Booking.getSourceDataTypeVal());

        int taskID = Integer.parseInt(bookingCust.getId());
        csCalloutWaitDistribute.setTaskId(taskID);
        csCalloutWaitDistribute.setCustName(bookingCust.getCustName());

        if (bookingCust.getActivityType() != null) {
            int subType = Integer.parseInt(bookingCust.getActivityType());
            csCalloutWaitDistribute.setSubTaskType(subType);
        }
        csCalloutWaitDistribute.setMobileMask(bookingCust.getMobileMask());
        csCalloutWaitDistribute.setMobileDigest(bookingCust.getMobileDigest());
        csCalloutWaitDistribute.setMobileCipher(bookingCust.getMobileCipher());
        csCalloutWaitDistribute.setEmailDigest(bookingCust.getEmailDigest());
        csCalloutWaitDistribute.setEmailMask(bookingCust.getEmailMask());
        csCalloutWaitDistribute.setEmailCipher(bookingCust.getEmailCipher());
        csCalloutWaitDistribute.setSourceDt(bookingCust.getBookingDt());
        csCalloutWaitDistribute.setBookingContent(bookingCust.getBookingContent());
        csCalloutWaitDistribute.setCmsNo(bookingCust.getBookingSerialNo());
        csCalloutWaitDistribute.setConsultDt(bookingCust.getBookingDetailDt());
        // 好臻需求 处理cms推送过来的数据内容
        if (bookingCust.getDisCode() != null && bookingCust.getBookingContent() != null) {
            String discode = bookingCust.getDisCode();
            String descByCode = CmsDisCodeEnum.getDescByCode(discode);
            csCalloutWaitDistribute.setBookingContent(descByCode + "-" + bookingCust.getBookingContent());
        }
        return csCalloutWaitDistribute;
    }

    /**
     * 处理香港渠道预约客户数据
     * 业务逻辑:处理 业务渠道=香港 的预约客户数据
     * 预约手机号查询客户所属投顾
     * -有投顾客户信息
     *    -判断是否有真实投顾
     *      是：①站内信&企微 通知投顾；②预约信息在沟通记录中留档
     *      否：①虚拟投顾是 客户服务部-高端 或 客户服务部-零售 下时划转到 HW开户-未维护库；②预约信息在沟通记录中留档
     * -无投顾客户信息
     *   ①创建投顾客户号
     *   ②预约信息在沟通记录中留档
     * @param cust 预约客户数据
     */
    private void handleHkBookingCustData(BookingCust cust) {
        try {
            QueryConstantInfoRequest request = new QueryConstantInfoRequest();
            request.setMobileDigest(cust.getMobileDigest());
            request.setMobileAreaCode(cust.getMobileAreaCode());
            // 根据预约客户手机号查询投顾客户信息
            QueryConstantInfoResponse info = consultantInfoService.getConsultantInfo(request);
            String returnCode = info.getReturnCode();
            List<ConsultantInfoDto> consultantList = info.getConsultantList();
            if (!BaseConstantEnum.SUCCESS.getCode().equals(returnCode)) {
                log.warn("AgentDataCollectTaskServiceImpl handleHkBookingCustData getConsultantInfo error! request={}", JSON.toJSONString(request));
                return;
            }

            if (CollectionUtils.isNotEmpty(consultantList)) {
                // 手机号存在投顾客户信息时
                for (ConsultantInfoDto dto : consultantList) {
                    // 是否虚拟投顾（1 是/0 否）
                    String isVirtual = dto.getIsVirtual();
                    if (YesOrNoEnum.NO.getCode().equals(isVirtual)) {
                        // 真实投顾：
                        // ①站内信&企微 通知投顾；
                        pushMsg2tg(cust, dto);
                        // ②预约信息在沟通记录中留档
                        addCommunicateVisit(cust, dto);
                        // 更新数据已读状态
                        updateBookCustReadFlag(Integer.parseInt(cust.getId()));
                    } else {
                        // 虚拟投顾
                        if (CenterOrgEnum.CS_GD.getCode().equals(dto.getCenterOrgCode()) || CenterOrgEnum.CS_LS.getCode().equals(dto.getCenterOrgCode())) {
                            // 所属部门是 客户服务部-高端 或 高端服务部-零售时
                            // ①将客户划转至“HW开户-未维护（HWKH-WWH）”库；
                            CustconstantInfoDomain custDomain = new CustconstantInfoDomain();
                            custDomain.setCustno(dto.getConscustno());
                            // 历史逻辑，分配投顾逻辑里只用到了 consCode，未使用 nextcons
                            custDomain.setConscode(SpecialConsCodeEnum.HWKH_WWH.getCode());
                            custDomain.setNextcons(SpecialConsCodeEnum.HWKH_WWH.getCode());
                            custDomain.setModifier(Constants.CONSTANT_OPERATE_SYS);
                            UpdateCustconstantInfoRequest updateRequest = new UpdateCustconstantInfoRequest();
                            updateRequest.setCustconstantInfoDomain(custDomain);
                            UpdateCustconstantInfoResponse updateCustconstant = updateCustconstantInfoService.updateCustconstant(updateRequest);
                            log.info("AgentDataCollectTaskServiceImpl handleHkBookingCustData 香港渠道 将客户划转至“HW开户-未维护（HWKH-WWH）”库 params=[cust={}] Response={}", JSON.toJSONString(cust), JSON.toJSONString(updateCustconstant));
                        }
                        // ②预约信息进CRM呼出任务表
                        boolean result = insertCsCallOutWaitData(cust);
                        log.info("AgentDataCollectTaskServiceImpl handleHkBookingCustData 香港渠道 虚拟投顾 创建呼出任务结果={}!", result);
                    }
                }
            } else {
                // 手机号不存在时，则创建投顾客户号
                CreateConsCustRequest conscust = new CreateConsCustRequest();
                conscust.setCustsource("PH2407W01");
                conscust.setCustname(cust.getCustName());
                // 设置投资者类型为个人
                conscust.setInvsttype(Constants.CUST_INVEST_TYPE_PERSON);
                conscust.setMobileAreaCode(cust.getMobileAreaCode());
                CodecSingleResponse emailResponse = decryptSingleFacade.decrypt(cust.getEmailCipher());
                conscust.setEmail(emailResponse.getCodecText());
                CodecSingleResponse mobileResponse = decryptSingleFacade.decrypt(cust.getMobileCipher());
                conscust.setMobile(mobileResponse.getCodecText());
                conscust.setOperator(Constants.CONSTANT_OPERATE_SYS);
                // HW开户-未维护（HWKH-WWH）
                conscust.setConsCode(SpecialConsCodeEnum.HWKH_WWH.getCode());
                // hboneNo 暂不支持
                Response<CreateConsCustRespVO> voResponse = createCustInfo(conscust);
                log.info("AgentDataCollectTaskServiceImpl handleHkBookingCustData 香港渠道 创建客户信息 params=[cust={}] Response={}", JSON.toJSONString(cust), JSON.toJSONString(voResponse));
                if (voResponse != null && voResponse.isSuccess()) {
                    // 投顾客户数据创建成功后 预约信息进CRM呼出任务表
                    boolean result = insertCsCallOutWaitData(cust);
                    log.info("AgentDataCollectTaskServiceImpl handleHkBookingCustData 香港渠道 创建呼出任务结果={}!", result);
                }
            }
        } catch (Exception e) {
            log.error("AgentDataCollectTaskServiceImpl handleHkBookingCustData error! cust={}, error={}", JSON.toJSONString(cust), e.getMessage(), e);
        }
    }

    /**
     * 新增客服呼出任务
     *
     * @param cust
     * @return
     */
    private boolean insertCsCallOutWaitData(BookingCust cust) {
        // 投顾客户数据创建成功后 预约信息进CRM呼出任务表
        CsCalloutWaitDistribute csCalloutWaitDistribute = buildCsCalloutWaitDistribute(cust);
        Integer result = sourceDataDao.insertBatchAddSourceData(Lists.newArrayList(csCalloutWaitDistribute));
        if (result == 0) {
            log.info("AgentDataCollectTaskServiceImpl handleHkBookingCustData 香港渠道 创建呼出任务失败 error!");
            return false;
        }
        return updateBookCustReadFlag(Integer.parseInt(cust.getId()));
    }

    /**
     * 更新预约客户数据为已读状态
     * @param id    预约客户id
     * @return  boolean
     */
    private boolean updateBookCustReadFlag(int id) {
        // 更新预约客户数据为已读状态
        Integer updateNum = sourceDataDao.updateBookCustReadFlag(Lists.newArrayList(id));
        if (updateNum == 0) {
            log.info("AgentDataCollectTaskServiceImpl handleHkBookingCustData 香港渠道 预约客户表已读状态更新失败 error!");
            return false;
        }
        log.info("AgentDataCollectTaskServiceImpl updateBookCustReadFlag 香港渠道 预约客户表已读状态更新成功！");
        return true;
    }

    /**
     * 新增沟通记录
     *
     * @param cust
     * @param dto
     */
    private void addCommunicateVisit(BookingCust cust, ConsultantInfoDto dto) {
        CommunicateVisitInsertVo vo = new CommunicateVisitInsertVo();
        vo.setConscustno(dto.getConscustno());
        vo.setOperator(Constants.CONSTANT_OPERATE_SYS);
        vo.setVisitType(CustVisitTypeEnum.TELEPHONE.getCode());
        String content = "客户：%s（投顾客户号%s），咨询好买香港内容：%s，请尽快联系该客户！";
        vo.setCommContent(String.format(content, cust.getCustName(), dto.getConscustno(), cust.getBookingContent()));
        CoreReturnMessageDto communicateMsg = csCommunicateVisitService.addCommunicateVisit(vo);
        log.info("AgentDataCollectTaskServiceImpl handleHkBookingCustData 香港渠道 预约信息在沟通记录中留档 params=[cust={}] Response={}", JSON.toJSONString(cust), JSON.toJSONString(communicateMsg));
    }

    /**
     * 站内信&企微 通知投顾
     *
     * @param cust
     * @param dto
     */
    private void pushMsg2tg(BookingCust cust, ConsultantInfoDto dto) {
        try {
            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put("custname", cust.getCustName());
            paramMap.put("custno", dto.getConscustno());
            // 邮箱解密
            CodecSingleResponse emailResponse = decryptSingleFacade.decrypt(cust.getEmailCipher());
            String emailAddr = StringUtil.isEmpty(cust.getEmailCipher()) ? "暂无" : emailResponse.getCodecText();
            paramMap.put("Email", emailAddr);
            // 手机号解密
            CodecSingleResponse mobileResponse = decryptSingleFacade.decrypt(cust.getMobileCipher());
            String mobile = StringUtil.isEmpty(cust.getMobileAreaCode()) ? mobileResponse.getCodecText() : StringUtils.join(cust.getMobileAreaCode(), "-", mobileResponse.getCodecText());
            paramMap.put("custMobile", mobile);
            paramMap.put("noticeContent", cust.getBookingContent());
            BaseResponse baseResponse = cmPushMsgService.pushMsgByConsCodeList(Constants.HK_MSG_TEMPLATE_ID_201695, Lists.newArrayList(dto.getConsCode()), paramMap);
            log.info("AgentDataCollectTaskServiceImpl handleHkBookingCustData 香港渠道 站内信&企微 通知投顾 params=[cust={}] Response={}", JSON.toJSONString(cust), JSON.toJSONString(baseResponse));
        } catch (Exception e) {
            log.info("AgentDataCollectTaskServiceImpl handleHkBookingCustData 香港渠道 站内信&企微 通知投顾 发生异常！ params=[cust={}] error={}", JSON.toJSONString(cust), e.getMessage(), e);
        }
    }

    /**
     * 创建投顾客户信息
     *
     * @param createCustReq
     * @return
     */
    public Response<CreateConsCustRespVO> createCustInfo(CreateConsCustRequest createCustReq) {
        String url = crmAccountPath + CrmAccountPathConstant.CREATE_CONS_CUST;
        String res = "";
        try {
            res = HttpUtils.jsonPost(url, createCustReq);
            log.info("http请求url:{}|入参:{}|出参:{}", url, JSON.toJSONString(createCustReq), res);
            Response response = JSON.parseObject(res, Response.class);
            response.setData(JSON.parseObject(JSON.toJSONString(response.getData()), CreateConsCustRespVO.class));
            return response;
        } catch (Exception e) {
            log.error("http请求url:{}|入参:{}|,出参:{}", url, JSON.toJSONString(createCustReq), res, e);
        }
        return null;
    }

    /**
     * 同步呼损原始数据方法
     */
    public void syncCallLossCustData() {
        log.info("同步呼损数据方法开始执行。。。");
        List<Map<String, Object>> callLossList = sourceDataDao.getCallLossCustData();
        if (callLossList.size() > 0) {
            List<CsCalloutWaitDistribute> csCalloutWaitDistributeList = new LinkedList<>();
            List<Integer> idList = new LinkedList<>();
            List<Integer> callLossIdList = new LinkedList<>();
            for (int k = 0; k < callLossList.size(); k++) {
                Map<String, Object> tempMap = callLossList.get(k);
                String tempCalleeNO = tempMap.get("CALLEENO").toString();
                String keyNum = tempMap.get("KEYNUM") == null ? null : tempMap.get("KEYNUM").toString();

                int taskID = Integer.parseInt(tempMap.get("ID").toString());
                callLossIdList.add(taskID);

                // 不是有效的calleeNO
                if (!ValidCalleeNO.isValidCalleeNO(tempCalleeNO)) {
                    continue;
                }
                CsCalloutWaitDistribute csCalloutWaitDistribute = new CsCalloutWaitDistribute();
                csCalloutWaitDistribute.setTaskType(SourceDataType.CallLoss.getSourceDataTypeVal());

                int subType = Integer.parseInt(tempCalleeNO);
                if (keyNum != null) {
                    if (keyNum.length() == 1) {
                        keyNum = "0" + keyNum;
                    }
                    subType = Integer.parseInt(subType + keyNum);
                }

                csCalloutWaitDistribute.setTaskId(taskID);
                csCalloutWaitDistribute.setSubTaskType(subType);

                idList.add(taskID);

                csCalloutWaitDistribute.setMobileMask(tempMap.get("CALLERNO_MASK") != null ? tempMap.get("CALLERNO_MASK").toString() : null);
                csCalloutWaitDistribute.setMobileDigest(tempMap.get("CALLERNO_DIGEST") != null ? tempMap.get("CALLERNO_DIGEST").toString() : null);
                csCalloutWaitDistribute.setMobileCipher(tempMap.get("CALLERNO_CIPHER") != null ? tempMap.get("CALLERNO_CIPHER").toString() : null);

                if (tempMap.get("CALLDATE") != null) {
                    String callDate = tempMap.get("CALLDATE").toString();
                    csCalloutWaitDistribute.setSourceDt(callDate);
                    csCalloutWaitDistribute.setConsultDt(callDate);  // 新增咨询时间字段
                }
                csCalloutWaitDistributeList.add(csCalloutWaitDistribute);
            }

            if (csCalloutWaitDistributeList.size() > 0) {
                boolean addFlag = sourceDataDao.insertBatchAddSourceData(csCalloutWaitDistributeList) == csCalloutWaitDistributeList.size();
                if (addFlag) {
                    log.info("已经将呼损原数据表中的数据插入到待分配表中");
                    boolean updateFlag = sourceDataDao.updateCallLossReadFlag(idList) >= 0;
                    if (updateFlag) {
                        log.info("呼损原数据表标识符已经更新好了");
                    }
                } else {
                    log.info("呼损原数据表中的数据插入到待分配表中失败或没有数据");
                }
            } else {
                boolean updateFlag = sourceDataDao.updateCallLossReadFlag(idList) >= 0;
                if (updateFlag) {
                    log.info("呼损原数据表标识符已经更新好了");
                }
            }
        }
        log.info("同步呼损数据方法执行结束。。。");
    }

    /**
     * 同步云翌原始数据方法
     */
    public void syncYunEasyCustData() {
        log.info("同步云翌数据方法开始执行。。。");
        List<Map<String, Object>> yunEasyCustData = sourceDataDao.getYunEasyCustData();
        if (yunEasyCustData.size() > 0) {
            List<CsCalloutWaitDistribute> csCalloutWaitDistributeList = new LinkedList<>();
            List<Long> idList = new LinkedList<>();
            for (int k = 0; k < yunEasyCustData.size(); k++) {
                Map<String, Object> tempMap = yunEasyCustData.get(k);
                CsCalloutWaitDistribute csCalloutWaitDistribute = new CsCalloutWaitDistribute();
                csCalloutWaitDistribute.setTaskType(SourceDataType.CallLoss.getSourceDataTypeVal());

                Long taskID = Long.valueOf(tempMap.get("ID").toString());
                csCalloutWaitDistribute.setTaskId(taskID);

                // IVR按键值：9-变更银行卡或者手机号；1-高端产品；2-咨询交易和业务；3-寻求合作；6-投诉建议；0-其他服务
                String keyNum = tempMap.get("DTMF") == null ? "00" : tempMap.get("DTMF").toString();
                if (keyNum != null) {
                    if (keyNum.length() == 1) {
                        keyNum = "0" + keyNum;
                    }
                }

                // 任务规则匹配：3500对应云翌分组号码001（客服部技能组）
                int subType = Integer.parseInt(3500 + keyNum);
                csCalloutWaitDistribute.setSubTaskType(subType);
                idList.add(taskID);
                csCalloutWaitDistribute.setMobileMask(tempMap.get("CALLER_NUM_MASK") != null ? tempMap.get("CALLER_NUM_MASK").toString() : null);
                csCalloutWaitDistribute.setMobileDigest(tempMap.get("CALLER_NUM_DIGEST") != null ? tempMap.get("CALLER_NUM_DIGEST").toString() : null);
                csCalloutWaitDistribute.setMobileCipher(tempMap.get("CALLER_NUM_CIPHER") != null ? tempMap.get("CALLER_NUM_CIPHER").toString() : null);
                if (tempMap.get("START_TIME") != null) {
                    String callDate = tempMap.get("START_TIME").toString();
                    csCalloutWaitDistribute.setSourceDt(callDate);
                    csCalloutWaitDistribute.setConsultDt(callDate);  // 新增咨询时间
                }
                csCalloutWaitDistributeList.add(csCalloutWaitDistribute);
            }

            if (csCalloutWaitDistributeList.size() > 0) {
                boolean addFlag = sourceDataDao.insertBatchAddSourceData(csCalloutWaitDistributeList) == csCalloutWaitDistributeList.size();
                if (addFlag) {
                    log.info("已经将云翌原数据表中的数据插入到待分配表中");
                    boolean updateFlag = sourceDataDao.updateYunEasyReadFlag(idList) >= 0;
                    if (updateFlag) {
                        log.info("云翌原数据表标识符已经更新好了");
                    }
                } else {
                    log.info("云翌原数据表中的数据插入到待分配表中失败或没有数据");
                }
            }
        }
        log.info("同步云翌数据方法执行结束。。。");
    }

}