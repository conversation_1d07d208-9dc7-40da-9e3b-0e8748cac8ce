<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.cs.task.dao.LeaveMsgCustDao">
	<!-- 功能菜单信息实体与数据库表字段映射 -->
    <resultMap id="leaveMsgCustModel" type="com.howbuy.cs.task.model.LeaveMsgCustModel">
        <result column="ID" property="id"/>
        <result column="CALLDATE" property="calldate"/>
        <result column="CALLEENO" property="calleeno"/>
        <result column="CALLERNO" property="callerno"/>
        <result column="AGENTNO" property="agentno"/>
        <result column="AGENTNAME" property="agentname"/>
        <result column="MSG" property="msg"/>
        <result column="READFLAG" property="readflag"/>
        <result column="LOSSTYPE" property="losstype"/>
        <result column="DUMPDATE" property="dumpdate"/>
        <result column="KEYNUM" property="keynum"/>
        <result column="CUSTOMERCALLID" property="customercallid"/>
        <result column="CALLERNO_MASK" property="callernomask"/>
        <result column="CALLERNO_DIGEST" property="callernodigest"/>
        <result column="CALLERNO_CIPHER" property="callernocipher"/>
    </resultMap>

    <select id="listLeaveMsgCustLimit100" parameterType="Map" resultMap="leaveMsgCustModel" useCache="false">
        SELECT T1.CALLDATE,
             T1.CALLERNO,
             SUBSTR(T2.REMARK, 0, 4) AS CALLEENO,
             T1.AGENTNO,
             T1.AGENTNAME,
             CASE
               WHEN T2.REMARK IS NOT NULL THEN
                SUBSTR(T2.REMARK, 0, 4) || '~' || T1.CALLEEKEY
             END AS MSG,
             T1.READFLAG,
             T1.LOSSTYPE,
             T1.DUMPDATE,
             T1.CALLEEKEY as KEYNUM,
             T1.CUSTOMERCALLID
        FROM (SELECT T.CUSTOMERNO AS CALLERNO,
                     T.AGTID AS AGENTNO,
                     T.AGTNAME AS AGENTNAME,
                     0 AS READFLAG,
                     CASE
                       WHEN T.STARTTIME IS NOT NULL THEN
                        TO_DATE(SUBSTR(T.STARTTIME, 0, 19),
                                'yyyy-MM-dd HH24:mi:ss')
                     END AS CALLDATE,
                     CASE
                       WHEN T.HANGUPENTITY = '分机' THEN
                        1
                       WHEN T.HANGUPENTITY = 'IVR' THEN
                        2
                       WHEN T.HANGUPENTITY = '业务' THEN
                        3
                       WHEN T.HANGUPENTITY = '中继' THEN
                        4
                     END AS LOSSTYPE,
                     SYSDATE AS DUMPDATE,
                     CASE
                       WHEN T.OBLFIELD9 = '好买人工服务' OR T.OBLFIELD9 = '人工服务' OR T.OBLFIELD9 = '如需其他服务' THEN
                        0
                       WHEN T.OBLFIELD9 = '好买咨询高端产品' OR T.OBLFIELD9 = '咨询高端产品' OR T.OBLFIELD9 = '好买高端产品咨询' THEN
                        1
                       WHEN T.OBLFIELD9 = '好买咨询交易及业务' OR T.OBLFIELD9 = '咨询交易及业务' OR T.OBLFIELD9 = '好买交易及业务咨询' THEN
                        2
                       WHEN T.OBLFIELD9 = '好买咨询掌上基金' OR T.OBLFIELD9 = '咨询掌上基金' OR T.OBLFIELD9 = '好买掌上基金咨询' THEN
                        3
                       WHEN T.OBLFIELD9 = '好买咨询活动' OR T.OBLFIELD9 = '咨询活动' OR T.OBLFIELD9 = '好买活动咨询' THEN
                        4
                       WHEN T.OBLFIELD9 = '寻求合作' OR T.OBLFIELD9 = '好买寻求合作' THEN
                        5
                       WHEN T.OBLFIELD9 = '好买投诉建议' OR T.OBLFIELD9 = '投诉建议' THEN
                        6
                       WHEN T.OBLFIELD9 = '好买变更银行卡或手机号' OR T.OBLFIELD9 = '变更银行卡或手机号' THEN
                        9
                       WHEN T.OBLFIELD9 IS NULL AND (T.OBLFIELD10 = '好买' OR T.OBLFIELD10 = '腾讯' OR T.OBLFIELD10 = '好买咨询活动') THEN
                       10
                     END AS CALLEEKEY,
                     CASE
                       WHEN T.OBLFIELD10 = '腾讯高端' THEN
                        '腾讯高端'
                       WHEN T.OBLFIELD10 = '腾讯400高端' THEN
                        '腾讯400高端'
                       WHEN T.OBLFIELD10 = '腾讯低端' OR T.OBLFIELD10 = '腾讯400低端' THEN
                        '腾讯400低端'
                       WHEN T.OBLFIELD10 = '好买高端掌基' THEN
                        '好买高端(掌基)'
                       WHEN T.OBLFIELD10 = 'SEM' THEN
                        'SEM来电'
                       WHEN T.OBLFIELD10 = 'AON' THEN
                        'AON来电'
                       WHEN T.OBLFIELD10 = '百度高端' THEN
                        '百度金融之心'
                       WHEN T.OBLFIELD10 = '百度理财' THEN
                        '百度理财'
                       ELSE
                        T.OBLFIELD10
                     END AS CALLEE,
                     T.CUSTOMERCALLID
                FROM APP_INF_CUSTOMERCALL_DTL_RAW T
               WHERE T.OBLFIELD10 IS NOT NULL
                 AND T.ISCONNAGENT = '否'
                 AND T.DIRECTION = '呼入'
                 AND T.OBLFIELD10 != '好买高端'
                 AND T.OBLFIELD10 != '好买寻求合作') T1
          LEFT JOIN CS_TASK_SOURCE_CONFIG T2
          ON T2.SOURCE_NAME = T1.CALLEE
       WHERE T2.PARENT_ID LIKE '3%' AND T2.SOURCE_ID !='310010' 
       <choose>
           <when test = "startTime != null and endTime != null" >
                and T1.CALLDATE &gt; #{startTime} and T1.CALLDATE &lt;  #{endTime}
           </when>
           <otherwise>
                and T1.CALLDATE &gt; (SELECT MAX(calldate)  FROM CS_CALLLOSS_CUST ) 
                and rownum &lt;= 100
           </otherwise>
       </choose> 
    </select>
    
    <insert id="insertLeaveMsgCustBatch" parameterType="list" useGeneratedKeys="false">
        insert into CS_CALLLOSS_CUST (ID, CALLDATE, CALLEENO, AGENTNO, AGENTNAME, MSG, READFLAG, LOSSTYPE, DUMPDATE,KEYNUM,CUSTOMERCALLID,CALLERNO_MASK,CALLERNO_DIGEST,CALLERNO_CIPHER)
        select SEQ_CS_CALLLOSS_CUST.nextval, A.* from (
        <foreach item="item" index="index" collection="list" separator="union all">
            ( select
            	#{item.calldate,jdbcType=TIMESTAMP } as a1,
            	#{item.calleeno, jdbcType=VARCHAR } as a3,
            	#{item.agentno, jdbcType=VARCHAR } as a4,
            	#{item.agentname, jdbcType=VARCHAR } as a5,
            	#{item.msg, jdbcType=VARCHAR } as a6,
            	#{item.readflag, jdbcType=INTEGER } as a7,
            	#{item.losstype, jdbcType=INTEGER } as a8,
            	#{item.dumpdate,jdbcType=TIMESTAMP } as a9,
            	#{item.keynum,jdbcType=VARCHAR } as a10,
            	#{item.customercallid,jdbcType=NUMERIC } as a11, 
            	#{item.callernomask, jdbcType=VARCHAR } as a12,
            	#{item.callernodigest, jdbcType=VARCHAR } as a13,
            	#{item.callernocipher, jdbcType=VARCHAR } as a14
              from dual
            )
        </foreach>
        )A
    </insert>
</mapper>