package com.howbuy.cs.task.model;

import java.math.BigDecimal;

/**
 * 理财顾问信息表
 */
public class CmConsultant {
    /**
    * 理财顾问代码
    */
    private String conscode;

    /**
    * 理财顾问名称
    */
    private String consname;

    /**
    * 理财顾问级别
    */
    private String conslevel;

    /**
    * 所属小组
    */
    private String teamcode;

    /**
    * 理财顾问特点
    */
    private String character;

    /**
    * 理财顾问状态1有效，0无效
    */
    private String consstatus;

    /**
    * 记录状态
    */
    private String recstat;

    /**
    * 复核标志
    */
    private String checkflag;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 复核人
    */
    private String checker;

    /**
    * 记录创建日期
    */
    private String credt;

    /**
    * 记录修改日期
    */
    private String moddt;

    /**
    * 联系电话
    */
    private String telno;

    /**
    * 手机
    */
    private String mobile;

    /**
    * 所属部门
    */
    private String deptcode;

    /**
    * 所属理财中心
    */
    private String outletcode;

    /**
    * 照片地址
    */
    private String pictureurl;

    /**
    * 电子邮件
    */
    private String email;

    /**
    * 简历
    */
    private String resume;

    /**
    * 入职时间
    */
    private String startdt;

    /**
    * 离职时间
    */
    private String enddt;

    /**
    * 从业资格证书号码
    */
    private String empcardno;

    /**
    * 登陆状态（1：能登陆，0：不能登陆）
    */
    private String loginflag;

    /**
    * ����������1������2������
    */
    private String isseniormgr;

    /**
    * 工作地点（PD：浦东，HK：虹口， QT：其他）
    */
    private String workplace;

    /**
    * 拨号通道（RHPT：睿狐普通，RHCC：睿狐CC，SLT：商路通）
    */
    private String dialchannel;

    /**
    * 用户IP地址
    */
    private String ip;

    /**
    * 投顾2013年底前高净值总销量
    */
    private BigDecimal salaryamtbefore;

    /**
    * 是否虚拟投顾（1：是，0：否）
    */
    private String isvirtual;

    /**
    * 是否内部投顾（1：是，0：否）
    */
    private String isinside;

    /**
    * 投顾照片链接地址
    */
    private String picaddr;

    /**
    * 投顾照片链接地址1
    */
    private String picaddr1;

    /**
    * 理财顾问特点1
    */
    private String character1;

    /**
    * 理财顾问职位
    */
    private String position;

    /**
    * 1，成交备用库；2，优质潜客库；3，一般潜客库；4，陌call库；5，公募库;
    */
    private String ownertype;

    /**
    * 企业微信二维码地址
    */
    private String codepicaddr;

    /**
    * 手机号掩码
    */
    private String mobileDigest;

    /**
    * 坐席工号
    */
    private String agentno;

    /**
    * 企业微信账号
    */
    private String wechatconscode;

    public String getConscode() {
        return conscode;
    }

    public void setConscode(String conscode) {
        this.conscode = conscode;
    }

    public String getConsname() {
        return consname;
    }

    public void setConsname(String consname) {
        this.consname = consname;
    }

    public String getConslevel() {
        return conslevel;
    }

    public void setConslevel(String conslevel) {
        this.conslevel = conslevel;
    }

    public String getTeamcode() {
        return teamcode;
    }

    public void setTeamcode(String teamcode) {
        this.teamcode = teamcode;
    }

    public String getCharacter() {
        return character;
    }

    public void setCharacter(String character) {
        this.character = character;
    }

    public String getConsstatus() {
        return consstatus;
    }

    public void setConsstatus(String consstatus) {
        this.consstatus = consstatus;
    }

    public String getRecstat() {
        return recstat;
    }

    public void setRecstat(String recstat) {
        this.recstat = recstat;
    }

    public String getCheckflag() {
        return checkflag;
    }

    public void setCheckflag(String checkflag) {
        this.checkflag = checkflag;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getChecker() {
        return checker;
    }

    public void setChecker(String checker) {
        this.checker = checker;
    }

    public String getCredt() {
        return credt;
    }

    public void setCredt(String credt) {
        this.credt = credt;
    }

    public String getModdt() {
        return moddt;
    }

    public void setModdt(String moddt) {
        this.moddt = moddt;
    }

    public String getTelno() {
        return telno;
    }

    public void setTelno(String telno) {
        this.telno = telno;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getDeptcode() {
        return deptcode;
    }

    public void setDeptcode(String deptcode) {
        this.deptcode = deptcode;
    }

    public String getOutletcode() {
        return outletcode;
    }

    public void setOutletcode(String outletcode) {
        this.outletcode = outletcode;
    }

    public String getPictureurl() {
        return pictureurl;
    }

    public void setPictureurl(String pictureurl) {
        this.pictureurl = pictureurl;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getResume() {
        return resume;
    }

    public void setResume(String resume) {
        this.resume = resume;
    }

    public String getStartdt() {
        return startdt;
    }

    public void setStartdt(String startdt) {
        this.startdt = startdt;
    }

    public String getEnddt() {
        return enddt;
    }

    public void setEnddt(String enddt) {
        this.enddt = enddt;
    }

    public String getEmpcardno() {
        return empcardno;
    }

    public void setEmpcardno(String empcardno) {
        this.empcardno = empcardno;
    }

    public String getLoginflag() {
        return loginflag;
    }

    public void setLoginflag(String loginflag) {
        this.loginflag = loginflag;
    }

    public String getIsseniormgr() {
        return isseniormgr;
    }

    public void setIsseniormgr(String isseniormgr) {
        this.isseniormgr = isseniormgr;
    }

    public String getWorkplace() {
        return workplace;
    }

    public void setWorkplace(String workplace) {
        this.workplace = workplace;
    }

    public String getDialchannel() {
        return dialchannel;
    }

    public void setDialchannel(String dialchannel) {
        this.dialchannel = dialchannel;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public BigDecimal getSalaryamtbefore() {
        return salaryamtbefore;
    }

    public void setSalaryamtbefore(BigDecimal salaryamtbefore) {
        this.salaryamtbefore = salaryamtbefore;
    }

    public String getIsvirtual() {
        return isvirtual;
    }

    public void setIsvirtual(String isvirtual) {
        this.isvirtual = isvirtual;
    }

    public String getIsinside() {
        return isinside;
    }

    public void setIsinside(String isinside) {
        this.isinside = isinside;
    }

    public String getPicaddr() {
        return picaddr;
    }

    public void setPicaddr(String picaddr) {
        this.picaddr = picaddr;
    }

    public String getPicaddr1() {
        return picaddr1;
    }

    public void setPicaddr1(String picaddr1) {
        this.picaddr1 = picaddr1;
    }

    public String getCharacter1() {
        return character1;
    }

    public void setCharacter1(String character1) {
        this.character1 = character1;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getOwnertype() {
        return ownertype;
    }

    public void setOwnertype(String ownertype) {
        this.ownertype = ownertype;
    }

    public String getCodepicaddr() {
        return codepicaddr;
    }

    public void setCodepicaddr(String codepicaddr) {
        this.codepicaddr = codepicaddr;
    }

    public String getMobileDigest() {
        return mobileDigest;
    }

    public void setMobileDigest(String mobileDigest) {
        this.mobileDigest = mobileDigest;
    }

    public String getAgentno() {
        return agentno;
    }

    public void setAgentno(String agentno) {
        this.agentno = agentno;
    }

    public String getWechatconscode() {
        return wechatconscode;
    }

    public void setWechatconscode(String wechatconscode) {
        this.wechatconscode = wechatconscode;
    }
}