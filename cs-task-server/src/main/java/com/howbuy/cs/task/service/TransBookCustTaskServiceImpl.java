package com.howbuy.cs.task.service;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.conscust.dto.CustconstantInfoDomain;
import com.howbuy.crm.conscust.request.UpdateCustconstantInfoRequest;
import com.howbuy.crm.conscust.response.UpdateCustconstantInfoResponse;
import com.howbuy.crm.conscust.service.UpdateCustconstantInfoService;
import com.howbuy.cs.base.model.CallOutStatueEnum;
import com.howbuy.cs.base.response.BaseResponse;
import com.howbuy.cs.task.model.TransMobileCustModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;
import com.howbuy.crm.conscust.dto.CmSourceInfoDomain;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.CreateConscustInfoRequest;
import com.howbuy.crm.conscust.request.QueryCmSourceInfoRequest;
import com.howbuy.crm.conscust.request.QueryRepeatCustRequest;
import com.howbuy.crm.conscust.response.CreateConscustInfoResponse;
import com.howbuy.crm.conscust.response.QueryCmSourceInfoResponse;
import com.howbuy.crm.conscust.response.QueryRepeatCustResponse;
import com.howbuy.crm.conscust.service.CreateConscustInfoService;
import com.howbuy.crm.conscust.service.QueryCmSourceInfoService;
import com.howbuy.crm.conscust.service.QueryRepeatCustService;
import com.howbuy.cs.task.buss.TenpayCustTaskBuss;
import com.howbuy.cs.task.dao.TransBookCustDao;
import com.howbuy.cs.task.model.TransBookCustModel;
import com.howbuy.cs.task.util.DateTimeUtil;

@Slf4j
@Service("transBookCustTaskService")
public class TransBookCustTaskServiceImpl implements TransBookCustTaskService {
    @Autowired
    private TransBookCustDao transBookCustDao;

    @Autowired
    private TenpayCustTaskBuss tenpayCustTaskBuss;

    @Autowired
    private CreateConscustInfoService createConscustInfoService;

    @Autowired
    private QueryCmSourceInfoService queryCmSourceInfoService;

    @Autowired
    private QueryRepeatCustService queryRepeatCustService;

    @Autowired
    private UpdateCustconstantInfoService updateCustconstantInfoService;

    /**
     * T+14预约客户及8个公共库客户处理方法
     */
    @Override
    public void transBookCust(String arg) {

//        processT14();

        // 针对以下8个公共库客户跑批处理
        log.info("手机异常客户划转任务开始执行。。。");
        List<TransMobileCustModel> mobileCustList = transBookCustDao.getTransMobileCust();
        if (CollectionUtils.isNotEmpty(mobileCustList)) {
            log.info("共找到手机异常客户数量为：" + mobileCustList.size());
            for (TransMobileCustModel transMobileCustModel : mobileCustList) {
                updateCustconstant(transMobileCustModel.getConscustno(),"sys_task");
            }
            log.info("手机异常客户划转任务执行结束。。。");
        } else {
            log.info("未找到手机异常客户划转数据");
        }
    }

    /**
     * 根据taskId[CS_TASK_ASSIGN_DAY]
     * 针对 T+7的数据 [CS_CALLOUT_WAITDISTRIBUTE.DISPOSENUM=3-T+7 ] 失败的  跑批入库
     * @param taskId
     * @return BaseResponse
     */
    @Override
    public BaseResponse processFinalTransBookCust(Integer taskId, CallOutStatueEnum callOutStatueEnum){
        BaseResponse resp=new BaseResponse();

        TransBookCustModel model=transBookCustDao.getTransBookCustByTaskId(taskId);
        if(model!=null){
            int updateTaskCount=transBookCustDao.updateDealedTaskAssignDay(taskId,callOutStatueEnum.getCode());
            log.info("更新taskId:{} 变更为：已处理。更新条数：{}",taskId,updateTaskCount);
            pocessFinalFail(model);
            resp.success();
        }else{
            resp.noData(String.format("根据taskId:%s 未找到待处理的T+7数据！",taskId));
        }
        return resp;

    }


    /**
     * 历史跑批任务：针对T+14 [CS_CALLOUT_WAITDISTRIBUTE.DISPOSENUM=4-T+14 ] 跑批入库
     * @return BaseResponse
     */
    @Deprecated
    @Override
    public  BaseResponse processT14(){
        BaseResponse resp=new BaseResponse();

        log.info("预约客户划转任务[T+14]开始执行......");
        List<TransBookCustModel> bookCustList = transBookCustDao.getTransBookCust();
        log.info("[T+14]预约客户条数为：{}" , bookCustList.size());
        for (TransBookCustModel transBookCustModel : bookCustList) {
            pocessFinalFail(transBookCustModel);
        }
        resp.success();
        return resp;
    }



    /**
     * 预约处理 最终入库操作处理
     * @param transBookCustModel
     */
    private void pocessFinalFail(TransBookCustModel transBookCustModel){
        log.info("处理T+7失败的预约任务 ：{} ", JSONObject.toJSONString(transBookCustModel));
        boolean mobileError = false;

        // 客户手机号为空或长度不为11位的，则将客户划转至“错误信息库”
        if (StringUtils.isBlank(transBookCustModel.getMobilemask()) || transBookCustModel.getMobilemask().length() != 11) {
            mobileError = true;
        }

        // 如果是客户不存在，则新增客户
        if (transBookCustModel.getExistcount() == 0) {
            // 组装客户信息
            ConscustInfoDomain conscust = new ConscustInfoDomain();
            String conscustno = tenpayCustTaskBuss.getConsCustNo();
            transBookCustModel.setConscustno(conscustno);
            conscust.setConscustno(conscustno);
            conscust.setCustname(transBookCustModel.getCustname());
            conscust.setMobileCipher(transBookCustModel.getMobilecipher());
            conscust.setMobileMask(transBookCustModel.getMobilemask());
            conscust.setMobileDigest(transBookCustModel.getMobiledigest());
            conscust.setEmailCipher(transBookCustModel.getEmailcipher());
            conscust.setEmailMask(transBookCustModel.getEmailmask());
            conscust.setEmailDigest(transBookCustModel.getEmaildigest());
            conscust.setRegdt(DateTimeUtil.getCurrYMD());

            // 获取客户来源
            String newsourceno;
            if (StringUtils.isNotBlank(transBookCustModel.getNewsourceno())) {
                newsourceno = transBookCustModel.getNewsourceno();
            } else {
                newsourceno = getKycBookNewsourceno(transBookCustModel.getWaitid(), transBookCustModel.getRegoutletcode(), transBookCustModel.getHboneregoutletcode());
            }
            conscust.setNewsourceno(newsourceno);

            // 获取客户投顾
            String conscode;
            if (mobileError) {
                conscode = "false";
            } else {
                conscode = getConscode(transBookCustModel.getCalloutstatus());
            }
            conscust.setConscode(conscode);
            conscust.setConscuststatus("0");
            conscust.setInvsttype("1"); // 设置投资者类型为个人
            conscust.setCreator("sys_task");
            try {
                boolean existsRepeatCust = false;
                //验证重复客户
                if (StringUtils.isNotBlank(transBookCustModel.getMobiledigest())) {
                    QueryRepeatCustRequest queryRepeatCustRequest = new QueryRepeatCustRequest();
                    ConscustInfoDomain domain = new ConscustInfoDomain();
                    domain.setMobileDigest(transBookCustModel.getMobiledigest());
                    queryRepeatCustRequest.setConscustInfoDomain(domain);
                    QueryRepeatCustResponse repeatCustResponse = queryRepeatCustService.queryRepeatCust(queryRepeatCustRequest);
                    if (repeatCustResponse != null && "0000".equals(repeatCustResponse.getReturnCode()) && CollectionUtils.isNotEmpty(repeatCustResponse.getConscustInfoDomains())) {
                        existsRepeatCust = true;
                        transBookCustModel.setConscustno(repeatCustResponse.getConscustInfoDomains().get(0).getConscustno());
                    }
                }

                if (!existsRepeatCust) {
                    // 成功插入到客户表
                    CreateConscustInfoRequest request = new CreateConscustInfoRequest();
                    request.setConscustinfo(conscust);
                    CreateConscustInfoResponse createConscustInfoResponse = createConscustInfoService.insertConsCustInfo(request);
                    if (createConscustInfoResponse != null && "0000".equals(createConscustInfoResponse.getReturnCode())) {
                        log.info("======插入客户表成功！");
                    }
                }
            } catch (Exception e) {
                log.error("======插入客户表时出现异常，信息：" + e.getMessage());
            }
        } else {
            // 客户手机号为空或长度不为11位的，则将客户划转至“错误信息库”
            if (mobileError) {
                updateCustconstant(transBookCustModel.getConscustno(),transBookCustModel.getUserid());
            }
        }

        // 修改任务状态
        transBookCustDao.updateTransBookFlag(transBookCustModel);
    }


	/**
	 * 划转客户投顾至：错误信息库
	 */
	public void updateCustconstant(String custNo,String userid) {
		if (StringUtils.isNotBlank(custNo)) {
			CustconstantInfoDomain custDomain = new CustconstantInfoDomain();
			custDomain.setCustno(custNo);
			custDomain.setConscode("false");
			custDomain.setModifier(userid);
			custDomain.setModdt(DateTimeUtil.getCurDate());
			UpdateCustconstantInfoRequest updateRequest = new UpdateCustconstantInfoRequest();
			updateRequest.setCustconstantInfoDomain(custDomain);
			log.info("updateCustconstant|updateRequest：{}", updateRequest);
			UpdateCustconstantInfoResponse updateResponse = updateCustconstantInfoService.updateCustconstant(updateRequest);
			log.info("updateCustconstant|updateResponse：{}", updateResponse);
		}
	}

    /**
     * 获取预约客户来源
     */
    public String getKycBookNewsourceno(Integer waitid, String regoutletcode, String hboneregoutletcode) {
        String newsourceno = "RO1509P05"; // RO1509P05：其他来源-未知
        try {
            if (StringUtils.isNotBlank(regoutletcode)) {
                QueryCmSourceInfoRequest request = new QueryCmSourceInfoRequest();
                request.setSourceNo(regoutletcode);
                QueryCmSourceInfoResponse response = queryCmSourceInfoService.queryCmSourceInfo(request);
                log.info("预约客户任务waitid为：" + waitid + " ，根据regoutletcode获得来源response：" + JSON.toJSONString(response));
                if (response != null && "0000".equals(response.getReturnCode())) {
                    CmSourceInfoDomain cmSourceInfoDomain = response.getCmSourceInfo();
                    if (cmSourceInfoDomain != null && StringUtils.isNotBlank(cmSourceInfoDomain.getSourceNo())) {
                        newsourceno = cmSourceInfoDomain.getSourceNo();
                    }
                }
            } else if (StringUtils.isNotBlank(hboneregoutletcode)) {
                QueryCmSourceInfoRequest request = new QueryCmSourceInfoRequest();
                request.setSourceNo(hboneregoutletcode);
                QueryCmSourceInfoResponse response = queryCmSourceInfoService.queryCmSourceInfo(request);
                log.info("预约客户任务waitid为：" + waitid + " ，根据hboneregoutletcode获得来源response：" + JSON.toJSONString(response));
                if (response != null && "0000".equals(response.getReturnCode())) {
                    CmSourceInfoDomain cmSourceInfoDomain = response.getCmSourceInfo();
                    if (cmSourceInfoDomain != null && StringUtils.isNotBlank(cmSourceInfoDomain.getSourceNo())) {
                        newsourceno = cmSourceInfoDomain.getSourceNo();
                    }
                }
            }
        } catch (Exception e) {
            log.error("======查询客户来源时出现异常，信息：" + e.getMessage());
        }
        return newsourceno;
    }

    /**
     * 根据最近一次呼出状态获取划转客户投顾
     */
    public String getConscode(String calloutstatus) {
        String conscode = "";
        if ("3".equals(calloutstatus)) {// 无人接听，则将客户划转至“无人接听库”
            conscode = "wurenjieting";
        } else if ("4".equals(calloutstatus)) {// 占线（秒挂断），则将客户划转至“秒挂断库”
            conscode = "miaogua";
        } else if ("5".equals(calloutstatus)) {// 暂停服务/停机，则将客户划转至“暂停服务库”
            conscode = "ZTFW";
        } else if ("7".equals(calloutstatus)) {// 关机，则将客户划转至“关机库”
            conscode = "GJ";
        } else if ("8".equals(calloutstatus)) {// 空号/错误电话，则将客户划转至“错误信息库”
            conscode = "false";
        }
        return conscode;
    }

}