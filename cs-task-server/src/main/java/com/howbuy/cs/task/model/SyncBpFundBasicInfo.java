package com.howbuy.cs.task.model;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 基金基本信息表
 */
public class SyncBpFundBasicInfo {
    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金名称
     */
    private String fundName;

    /**
     * TA代码
     */
    private String taCode;

    /**
     * 基金管理人代码
     */
    private String fundManCode;

    /**
     * 产品分类ID
     */
    private String categoryId;

    /**
     * 币种
     * 156-人民币
     */
    private String currency;

    /**
     * 基金默认分红方式
     */
    private String dfltDivMode;

    /**
     * 转托管方式
     * 1-一次转托管；
     * 2-两次转托管
     */
    private String chgTrusteeMode;

    /**
     * 交易开始时间
     */
    private String startTm;

    /**
     * 交易结束时间
     */
    private String endTm;

    /**
     * 追加申购判断规则
     * 第1位：有未完成的认申购；第2位：有过成功的认购购确认；第3位：有过余额；4到8位暂未使用
     */
    private String suppleSubsRule;

    /**
     * 基金面值
     */
    private BigDecimal faceValue;

    /**
     * 费用计算方式
     * 0-外扣法；1-内扣法
     */
    private String feeCalMode;

    /**
     * 最低持有份额
     */
    private BigDecimal minAcctVol;

    /**
     * 基金风险等级
     */
    private String fundRiskLevel;

    /**
     * 记录状态
     * 0-正常
     */
    private String recStat;

    /**
     * 复核标志
     * 0-未复核；1-已复核
     */
    private String checkFlag;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 复核人
     */
    private String checker;

    /**
     * 记录创建日期
     */
    private String creDt;

    /**
     * 记录修改日期
     */
    private String modDt;

    /**
     * 基金类型
     * 0-股票型
     * 1-混合型
     * 2-债券型
     * 3-货币型
     * 4-QDII
     * 5-封闭式
     * 6-结构型
     */
    private String fundType;

    /**
     * 基金简称拼音
     */
    private String fundAttrPinyin;

    /**
     * 募集期交易截止时间
     */
    private String ipoEndTm;

    /**
     * 主基金代码
     */
    private String mainFundCode;

    /**
     * 可销售规模
     */
    private BigDecimal distributeSize;

    /**
     * 募集开始日期
     */
    private String ipoStartDt;

    /**
     * 募集结束日期
     */
    private String ipoEndDt;

    /**
     * 成立日期
     */
    private String establishDt;

    /**
     * 基金托管人代码
     */
    private String fundCustodianCode;

    /**
     * 同步时间
     */
    private Date syncDate;

    /**
     * 基金简称
     */
    private String fundAttr;

    /**
     * 结算人代码
     */
    private String partCode;

    /**
     * 好买基金简称
     */
    private String fundAttrHb;

    /**
     * 基金二级类型
     */
    private String fundSubType;

    /**
     * 理财产品开放周期
     */
    private String redeOpenTerm;

    /**
     * 备注
     */
    private String summary;

    /**
     * 产品开放类型（1-滚动开放；2-定期开放）
     */
    private String fundOpenMode;

    /**
     * 年化收益率类型（1-七日年化收益率；2-最近运作期年化收益率）
     */
    private String yieldType;

    /**
     * 产品推荐信息
     */
    private String recommInfo;

    /**
     * 基金类别 1货币型 2非货币型
     */
    private String fundClass;

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }

    public String getFundManCode() {
        return fundManCode;
    }

    public void setFundManCode(String fundManCode) {
        this.fundManCode = fundManCode;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getDfltDivMode() {
        return dfltDivMode;
    }

    public void setDfltDivMode(String dfltDivMode) {
        this.dfltDivMode = dfltDivMode;
    }

    public String getChgTrusteeMode() {
        return chgTrusteeMode;
    }

    public void setChgTrusteeMode(String chgTrusteeMode) {
        this.chgTrusteeMode = chgTrusteeMode;
    }

    public String getStartTm() {
        return startTm;
    }

    public void setStartTm(String startTm) {
        this.startTm = startTm;
    }

    public String getEndTm() {
        return endTm;
    }

    public void setEndTm(String endTm) {
        this.endTm = endTm;
    }

    public String getSuppleSubsRule() {
        return suppleSubsRule;
    }

    public void setSuppleSubsRule(String suppleSubsRule) {
        this.suppleSubsRule = suppleSubsRule;
    }

    public BigDecimal getFaceValue() {
        return faceValue;
    }

    public void setFaceValue(BigDecimal faceValue) {
        this.faceValue = faceValue;
    }

    public String getFeeCalMode() {
        return feeCalMode;
    }

    public void setFeeCalMode(String feeCalMode) {
        this.feeCalMode = feeCalMode;
    }

    public BigDecimal getMinAcctVol() {
        return minAcctVol;
    }

    public void setMinAcctVol(BigDecimal minAcctVol) {
        this.minAcctVol = minAcctVol;
    }

    public String getFundRiskLevel() {
        return fundRiskLevel;
    }

    public void setFundRiskLevel(String fundRiskLevel) {
        this.fundRiskLevel = fundRiskLevel;
    }

    public String getRecStat() {
        return recStat;
    }

    public void setRecStat(String recStat) {
        this.recStat = recStat;
    }

    public String getCheckFlag() {
        return checkFlag;
    }

    public void setCheckFlag(String checkFlag) {
        this.checkFlag = checkFlag;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getChecker() {
        return checker;
    }

    public void setChecker(String checker) {
        this.checker = checker;
    }

    public String getCreDt() {
        return creDt;
    }

    public void setCreDt(String creDt) {
        this.creDt = creDt;
    }

    public String getModDt() {
        return modDt;
    }

    public void setModDt(String modDt) {
        this.modDt = modDt;
    }

    public String getFundType() {
        return fundType;
    }

    public void setFundType(String fundType) {
        this.fundType = fundType;
    }

    public String getFundAttrPinyin() {
        return fundAttrPinyin;
    }

    public void setFundAttrPinyin(String fundAttrPinyin) {
        this.fundAttrPinyin = fundAttrPinyin;
    }

    public String getIpoEndTm() {
        return ipoEndTm;
    }

    public void setIpoEndTm(String ipoEndTm) {
        this.ipoEndTm = ipoEndTm;
    }

    public String getMainFundCode() {
        return mainFundCode;
    }

    public void setMainFundCode(String mainFundCode) {
        this.mainFundCode = mainFundCode;
    }

    public BigDecimal getDistributeSize() {
        return distributeSize;
    }

    public void setDistributeSize(BigDecimal distributeSize) {
        this.distributeSize = distributeSize;
    }

    public String getIpoStartDt() {
        return ipoStartDt;
    }

    public void setIpoStartDt(String ipoStartDt) {
        this.ipoStartDt = ipoStartDt;
    }

    public String getIpoEndDt() {
        return ipoEndDt;
    }

    public void setIpoEndDt(String ipoEndDt) {
        this.ipoEndDt = ipoEndDt;
    }

    public String getEstablishDt() {
        return establishDt;
    }

    public void setEstablishDt(String establishDt) {
        this.establishDt = establishDt;
    }

    public String getFundCustodianCode() {
        return fundCustodianCode;
    }

    public void setFundCustodianCode(String fundCustodianCode) {
        this.fundCustodianCode = fundCustodianCode;
    }

    public Date getSyncDate() {
        return syncDate;
    }

    public void setSyncDate(Date syncDate) {
        this.syncDate = syncDate;
    }

    public String getFundAttr() {
        return fundAttr;
    }

    public void setFundAttr(String fundAttr) {
        this.fundAttr = fundAttr;
    }

    public String getPartCode() {
        return partCode;
    }

    public void setPartCode(String partCode) {
        this.partCode = partCode;
    }

    public String getFundAttrHb() {
        return fundAttrHb;
    }

    public void setFundAttrHb(String fundAttrHb) {
        this.fundAttrHb = fundAttrHb;
    }

    public String getFundSubType() {
        return fundSubType;
    }

    public void setFundSubType(String fundSubType) {
        this.fundSubType = fundSubType;
    }

    public String getRedeOpenTerm() {
        return redeOpenTerm;
    }

    public void setRedeOpenTerm(String redeOpenTerm) {
        this.redeOpenTerm = redeOpenTerm;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getFundOpenMode() {
        return fundOpenMode;
    }

    public void setFundOpenMode(String fundOpenMode) {
        this.fundOpenMode = fundOpenMode;
    }

    public String getYieldType() {
        return yieldType;
    }

    public void setYieldType(String yieldType) {
        this.yieldType = yieldType;
    }

    public String getRecommInfo() {
        return recommInfo;
    }

    public void setRecommInfo(String recommInfo) {
        this.recommInfo = recommInfo;
    }

    public String getFundClass() {
        return fundClass;
    }

    public void setFundClass(String fundClass) {
        this.fundClass = fundClass;
    }
}