package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.FundFileProcessDtlRec;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.Date;
import java.util.List;

@MapperScan
public interface FundFileProcessDtlRecMapper {
    int deleteByPrimaryKey(String recordNo);

    int insert(FundFileProcessDtlRec record);

    int insertSelective(FundFileProcessDtlRec record);

    FundFileProcessDtlRec selectByPrimaryKey(String recordNo);

    int updateByPrimaryKeySelective(FundFileProcessDtlRec record);

    int updateByPrimaryKey(FundFileProcessDtlRec record);

    /**
     * @description: 根据taTradeDt、fileType查询未处理的文件记录
     * @param taTradeDt 交易日期
     * @param fileType	文件类型
     * @return java.util.List<com.howbuy.cs.task.model.FundFileProcessDtlRec>
     * @author: hongdong.xie
     * @date: 2024/11/21 13:47
     * @since JDK 1.8
     */
    List<FundFileProcessDtlRec> selectUnProcess(@Param("taTradeDt") String taTradeDt, @Param("fileType") String fileType);

    /**
     * @description: 更新文件处理状态
     * @param recordNo 文件记录号
     * @param oldOpStatus 旧状态
     * @param newOpStatus 新状态
     * @param updateTime 更新时间
     * @return int
     * @author: hongdong.xie
     * @date: 2024/11/21 13:57
     * @since JDK 1.8
     */
    int updateOpStatusByRecordNo(@Param("recordNo") String recordNo, @Param("oldOpStatus") String oldOpStatus, @Param("newOpStatus") String newOpStatus, @Param("updateTime") Date updateTime);
}