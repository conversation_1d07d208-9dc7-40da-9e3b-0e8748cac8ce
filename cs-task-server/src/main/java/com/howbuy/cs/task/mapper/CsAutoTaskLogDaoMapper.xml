<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.cs.task.dao.CsAutoTaskLogDao">
    
	<insert id="insertBatchAddAutoDistributeLog" parameterType="java.util.List" useGeneratedKeys="false">
	      insert into cs_autodistribute_log
			  (id,
			   advisorid,
			   advisorname,
			   custno,
			   custname,
			   orgcode,
			   custtype,
			   pubcustflag,
			   procid,
			   autotype,
			   distributedate,
			   teamCode,
			   custSendFlag,
			   waitid,
			   statistictype,
			   custMobileMask,
			   custMobileDigest,
			   custMobileCipher,
		DEAL_PUSH_MSG_FLAG
			   )
			   select seq_cs_autodistribute_log.nextval, A.* from (
		       <foreach item="item" index="index" collection="list" separator="union all">
		            (select
		            #{item.advisorId, jdbcType=VARCHAR} as a1,
		            #{item.advisorName, jdbcType=VARCHAR} as a2,
		            #{item.custNo, jdbcType=VARCHAR} as a3,
		            #{item.custName, jdbcType=VARCHAR} as a4,
		            #{item.orgCode, jdbcType=VARCHAR} as a5,
		            #{item.custType, jdbcType=INTEGER} as a6,
		            #{item.pubCustFlag, jdbcType=INTEGER} as a7,
		            #{item.procId, jdbcType=VARCHAR} as a8,
		            #{item.autoType, jdbcType=INTEGER} as a9,
		            sysdate as a10,
		            #{item.teamCode, jdbcType=VARCHAR} as a11,
		            #{item.custSmsFlag, jdbcType=INTEGER} as a13,
		            #{item.waitId, jdbcType=INTEGER} as a14,
		            #{item.statisticType, jdbcType=INTEGER} as a15,
		            #{item.custMobileMask, jdbcType=VARCHAR} as a16,
		            #{item.custMobileDigest, jdbcType=VARCHAR} as a17,
		            #{item.custMobileCipher, jdbcType=VARCHAR} as a18,
				   '0' as a19
		            from dual)
		        </foreach>
		        ) A
    </insert>

</mapper>