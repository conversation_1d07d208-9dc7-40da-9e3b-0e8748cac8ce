package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.CmParamChangeInfo;
import org.apache.ibatis.annotations.Param;

/**
 * @description 参数变更信息Mapper接口
 * <AUTHOR>
 * @date 2024-01-17 10:30:25
 */
public interface CmParamChangeInfoMapper {
    
    /**
     * @description 根据主键删除记录
     * @param id 主键ID
     * @return 受影响的行数
     * <AUTHOR>
     * @date 2024-01-17 10:30:25
     */
    int deleteByPrimaryKey(String id);

    /**
     * @description 插入完整的记录
     * @param record 参数变更信息实体
     * @return 受影响的行数
     * <AUTHOR>
     * @date 2024-01-17 10:30:25
     */
    int insert(CmParamChangeInfo record);

    /**
     * @description 选择性插入记录（只插入非空字段）
     * @param record 参数变更信息实体
     * @return 受影响的行数
     * <AUTHOR>
     * @date 2024-01-17 10:30:25
     */
    int insertSelective(CmParamChangeInfo record);

    /**
     * @description 根据主键查询记录
     * @param id 主键ID
     * @return 参数变更信息实体
     * <AUTHOR>
     * @date 2024-01-17 10:30:25
     */
    CmParamChangeInfo selectByPrimaryKey(String id);

    /**
     * @description 根据主键选择性更新记录（只更新非空字段）
     * @param record 参数变更信息实体
     * @return 受影响的行数
     * <AUTHOR>
     * @date 2024-01-17 10:30:25
     */
    int updateByPrimaryKeySelective(CmParamChangeInfo record);

    /**
     * @description 根据主键更新完整记录
     * @param record 参数变更信息实体
     * @return 受影响的行数
     * <AUTHOR>
     * @date 2024-01-17 10:30:25
     */
    int updateByPrimaryKey(CmParamChangeInfo record);

    /**
     * @description 根据参数ID查询待审核记录
     * @param paramId 参数ID
     * @return 参数变更信息实体
     * <AUTHOR>
     * @date 2024-01-17 10:30:25
     */
    CmParamChangeInfo selectByParamId(@Param("paramId") String paramId);
}