/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.cs.common.Constant.MessageBusinessIdConstant;
import com.howbuy.cs.outservice.accenter.QueryAccCenterOuterService;
import com.howbuy.cs.outservice.cms.report.CmsReportOutService;
import com.howbuy.cs.outservice.crm.core.CrmJjxxOuterService;
import com.howbuy.cs.task.buss.CustDailyReportBuss;
import com.howbuy.cs.task.buss.CustDailyReportPushBuss;
import com.howbuy.cs.task.model.ReportWithFunds;
import com.howbuy.cs.task.util.StaticVar;
import com.howbuy.member.dto.report.ProductReportUpdatePushDTO;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 客户每日报告服务实现类
 * <p>
 * !!!!!! TEST：用于产品测试、验收
 * @date 2025-06-16 13:25:20
 * @since JDK 1.8
 */
@Slf4j
@Service("custDailyReportTestService")
public class CustDailyReportTestServiceImpl implements CustDailyReportTestService {

    @Autowired
    private CmsReportOutService cmsReportOutService;

    @Autowired
    private CrmJjxxOuterService crmJjxxOuterService;

    @Autowired
    private QueryAccCenterOuterService queryAccCenterOuterService;


    /**
     * 基金信息临时缓存，任务执行期间避免重复查询
     */
    private final Map<String, JjxxInfo> fundInfoCache = new ConcurrentHashMap<>();

    /**
     * 分销渠道信息临时缓存，任务执行期间避免重复查询
     */
    private final Map<String, DisChannelCodeEnum> disChannelCache = new ConcurrentHashMap<>();

    /**
     * 臻财公众号 appId
     */
    @Value("${zc.official.account.app.id}")
    private String ZC_OFFICIAL_ACCOUNT_APP_ID;

    @Autowired
    private CustDailyReportBuss custDailyReportBuss;

    @Autowired
    private CustDailyReportPushBuss custDailyReportPushBuss;


    /**
     * @param arg 传入参数
     * @description: 每日给客户发送报告
     * @author: hongdong.xie
     * @date: 2025-06-16 13:25:20
     * @since JDK 1.8
     */
    @Override
    public void sendDailyReport(String arg) {
        log.info("客户每日报告任务开始执行...");

        try {
            // 初始化缓存
            initCache();
            JSONObject json = JSON.parseObject(arg);
            String startTimeStr = json.getString("startTime");
            String endTimeStr = json.getString("endTime");
            List<String> reportIds = json.getObject("reportIds", List.class);
            JSONArray hboneNoList = json.getJSONArray("hboneNoList");


            // 1. 获取每日更新的报告列表
            List<ProductReportUpdatePushDTO> reportList = cmsReportOutService.getUpdatedReports(startTimeStr, endTimeStr, reportIds);
            if (CollectionUtils.isEmpty(reportList)) {
                log.info("今日没有需要推送的报告");
                return;
            }
            log.info("获取到报告数量: {}", reportList.size());

            // 2. 获取最终要推送的客户和客户关联的报告列表
            Map<String, List<ReportWithFunds>> hboneNoAndReportMap = custDailyReportBuss.buildCustomerReportMap(reportList);
            log.info("需要推送的客户数量: {}", hboneNoAndReportMap.size());

            // 3. 遍历客户报告映射，进行幂等性检查和推送处理
            // ！！！！ 给客户推送报告这一步，仅日志汇总发送情况，并不真的发送
            // ！！！！ 给入参中指定的 hboneNoList 进行推送（hboneNoList 为产品人员指定的验收账号，用于测试）
            pushMessageForLog(hboneNoAndReportMap);


            List<ReportWithFunds> reportWithFundsList = reportList.stream()
                    .map(report -> new ReportWithFunds(report, report.getFundCodeList()))
                    .collect(Collectors.toList());

            Map<String, List<ReportWithFunds>> testHboneNoAndReportMap = new HashMap<>();
            hboneNoList.forEach(hboneNo -> testHboneNoAndReportMap.put(hboneNo.toString(), reportWithFundsList));

            custDailyReportPushBuss.pushMessage(testHboneNoAndReportMap);

            log.info("客户每日报告任务结束");
        } finally {
            // 任务结束，清理缓存
            clearCache();
        }
    }


    /**
     * @description: 初始化缓存
     * @author: jin.wang03
     * @date: 2025-06-27 09:35:38
     * @since JDK 1.8
     */
    private void initCache() {
        fundInfoCache.clear();
        disChannelCache.clear();
        log.debug("基金信息缓存已初始化");
    }

    /**
     * @description: 清理缓存，释放内存
     * @author: jin.wang03
     * @date: 2025-06-27 09:35:38
     * @since JDK 1.8
     */
    private void clearCache() {
        int fundCacheSize = fundInfoCache.size();
        int disCacheSize = disChannelCache.size();

        fundInfoCache.clear();
        disChannelCache.clear();

        log.info("基金信息缓存已清理，基金信息缓存条数: {}, 分销渠道缓存条数: {}", fundCacheSize, disCacheSize);
    }

    /**
     * @param fundCode 基金代码
     * @return JjxxInfo 基金信息
     * @description: 带缓存的基金信息查询
     * @author: jin.wang03
     * @date: 2025-06-27 09:35:38
     * @since JDK 1.8
     */
    private JjxxInfo getJjxxInfoWithCache(String fundCode) {
        return fundInfoCache.computeIfAbsent(fundCode, code -> {
            log.debug("查询基金{}信息，加入缓存", code);
            return crmJjxxOuterService.getJjxxByFundCode(code);
        });
    }

    /**
     * @param fundCode 基金代码
     * @return DisChannelCodeEnum 分销渠道枚举
     * @description: 带缓存的分销渠道查询
     * @author: jin.wang03
     * @date: 2025-06-27 09:35:38
     * @since JDK 1.8
     */
    private DisChannelCodeEnum getDisChannelWithCache(String fundCode) {
        return disChannelCache.computeIfAbsent(fundCode, code -> {
            log.debug("查询基金{}分销渠道，加入缓存", code);
            return crmJjxxOuterService.getDisCodeEnumByJjdm(code);
        });
    }

    /**
     * @param hboneNoAndReportMap
     * @return void
     * @description: 遍历客户报告映射，进行幂等性检查和推送处理
     * @author: jin.wang03
     * @date: 2025/6/26 14:03
     * @since JDK 1.8
     */
    private void pushMessageForLog(Map<String, List<ReportWithFunds>> hboneNoAndReportMap) {
        String sendDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        int processedCount = 0;
        int skippedCount = 0;
        int failedCount = 0;

        // 用于汇总每个客户的处理结果
        StringBuilder resultSummary = new StringBuilder();
        resultSummary.append("\n=== 客户每日报告推送测试结果汇总 ===\n");
        resultSummary.append("总客户数: ").append(hboneNoAndReportMap.size()).append("\n\n");

        for (Map.Entry<String, List<ReportWithFunds>> entry : hboneNoAndReportMap.entrySet()) {
            String hboneNo = entry.getKey();
            List<ReportWithFunds> reportWithFundsList = entry.getValue();
            if (CollectionUtils.isEmpty(reportWithFundsList)) {
                log.info("客户{}没有需要推送的报告，跳过处理", hboneNo);
                skippedCount++;
                continue;
            }

            try {
                // TEST模式：不校验流水的幂等性，也不写入流水表
                log.info("TEST模式：开始处理客户{}的报告推送，关联报告数量: {}", hboneNo, reportWithFundsList.size());

                // 汇总该客户的推送结果
                StringBuilder customerResult = new StringBuilder();
                customerResult.append("客户: ").append(hboneNo).append("\n");
                customerResult.append("要推送的报告列表及关联的基金列表: ").append("\n");
                for (ReportWithFunds reportWithFunds : reportWithFundsList) {
                    customerResult.append("  - ").append(reportWithFunds.getReport().getReportTitle()).append(": ")
                            .append(JSON.toJSONString(reportWithFunds.getFunds())).append("\n");
                }

                // 模拟推送[好买基金APP]并记录结果
                String hbAppResult = simulatePushHbApp(hboneNo, reportWithFundsList);
                customerResult.append("  - 好买基金APP: ").append(hbAppResult).append("\n");

                // 是否关注了[臻财公众号]
                boolean followOfficialAccount =
                        queryAccCenterOuterService.isFollowOfficialAccount(hboneNo, ZC_OFFICIAL_ACCOUNT_APP_ID);
                if (followOfficialAccount) {
                    // 模拟推送[臻财公众号]并记录结果
                    log.info("客户{}已关注[臻财公众号]，模拟推送[臻财公众号]", hboneNo);
                    String zcGzhResult = simulatePushZcGzh(hboneNo, reportWithFundsList);
                    customerResult.append("客户已关注[臻财公众号]").append("\n");
                    customerResult.append("  - 臻财公众号: ").append(zcGzhResult).append("\n");
                } else {
                    // 模拟发送[短信]并记录结果
                    log.info("客户{}未关注[臻财公众号]，模拟发送[短信]", hboneNo);
                    String smsResult = simulatePushSms(hboneNo, reportWithFundsList);
                    customerResult.append("客户未关注[臻财公众号]").append("\n");
                    customerResult.append("  - 短信: ").append(smsResult).append("\n");
                }

                // TEST模式：不插入[持仓报告推送流水表]
                resultSummary.append(customerResult).append("\n");
                processedCount++;
                log.info("TEST模式：处理客户{}的报告推送成功", hboneNo);
            } catch (Exception e) {
                log.error("TEST模式：处理客户{}的报告推送失败", hboneNo, e);
                resultSummary.append("客户: ").append(hboneNo).append(" - 处理失败: ").append(e.getMessage()).append("\n\n");
                failedCount++;
                // 继续处理下一个客户
            }
        }

        resultSummary.append("=== 处理统计 ===\n");
        resultSummary.append("成功处理客户数: ").append(processedCount).append("\n");
        resultSummary.append("跳过客户数: ").append(skippedCount).append("\n");
        resultSummary.append("失败客户数: ").append(failedCount).append("\n");
        resultSummary.append("=== 汇总结束 ===\n");

        // 统一输出汇总结果
        log.info(resultSummary.toString());
        log.info("TEST模式：客户每日报告任务执行成功，处理客户数: {}, 跳过客户数: {}, 失败客户数： {}", processedCount, skippedCount, failedCount);
    }


    /**
     * @param reportWithFundsList 报告与基金关联列表
     * @return Set<String> 唯一基金代码集合
     * @description: 获取报告列表中所有关联的基金代码
     * @author: jin.wang03
     * @date: 2025-07-15 09:34:40
     * @since JDK 1.8
     */
    private Set<String> getUniqueFundCodes(List<ReportWithFunds> reportWithFundsList) {
        Set<String> uniqueFundCodes = new HashSet<>();
        if (CollectionUtils.isNotEmpty(reportWithFundsList)) {
            for (ReportWithFunds reportWithFunds : reportWithFundsList) {
                if (CollectionUtils.isNotEmpty(reportWithFunds.getFunds())) {
                    uniqueFundCodes.addAll(reportWithFunds.getFunds());
                }
            }
        }
        return uniqueFundCodes;
    }

    /**
     * @param hboneNo             客户号
     * @param reportWithFundsList 报告与基金关联列表
     * @return String 推送结果描述
     * @description: 模拟推送[好买基金App]，返回推送结果描述
     * @author: jin.wang03
     * @date: 2025-07-18
     * @since JDK 1.8
     */
    private String simulatePushHbApp(String hboneNo, List<ReportWithFunds> reportWithFundsList) {
        // 获取所有关联的基金代码
        Set<String> uniqueFundCodes = getUniqueFundCodes(reportWithFundsList);

        StringBuilder result = new StringBuilder();

        // 只有一个基金代码时，走单个报告场景
        if (uniqueFundCodes.size() == 1) {
            String fundCode = uniqueFundCodes.iterator().next();
            JjxxInfo jjxxInfo = getJjxxInfoWithCache(fundCode);

            String templateId = MessageBusinessIdConstant.FUND_APP_REPORT_UPDATE_ONE_FUND;

            result.append("单个报告推送，模板ID: ").append(templateId)
                    .append(", 产品名称: ").append(jjxxInfo.getJjjc());

            log.info("TEST模式 - hbone: {} 单个报告 模拟推送[好买基金App]，模版id:{}, 产品名称: {}",
                    hboneNo, templateId, jjxxInfo.getJjjc());
        } else {
            String templateId = MessageBusinessIdConstant.FUND_APP_REPORT_UPDATE_MORE_THAN_ONE_FUND;

            result.append("多个报告推送，模板ID: ").append(templateId)
                    .append(", 基金数量: ").append(uniqueFundCodes.size());

            log.info("TEST模式 - hbone: {} 多个报告 模拟推送[好买基金App]，模版id:{}, 基金数量: {}",
                    hboneNo, templateId, uniqueFundCodes.size());
        }

        return result.toString();
    }

    /**
     * @param hboneNo             客户号
     * @param reportWithFundsList 报告与基金关联列表
     * @return String 推送结果描述
     * @description: 模拟推送[臻财公众号]，返回推送结果描述
     * @author: jin.wang03
     * @date: 2025-07-18
     * @since JDK 1.8
     */
    private String simulatePushZcGzh(String hboneNo, List<ReportWithFunds> reportWithFundsList) {
        StringBuilder result = new StringBuilder();
        List<String> mockMessageIds = new ArrayList<>();

        for (ReportWithFunds reportWithFunds : reportWithFundsList) {
            ProductReportUpdatePushDTO report = reportWithFunds.getReport();

            for (String fund : reportWithFunds.getFunds()) {
                JjxxInfo jjxxInfo = getJjxxInfoWithCache(fund);
                String mockMessageId = "ZC_GZH_" + System.currentTimeMillis() + "_" + fund.hashCode();
                mockMessageIds.add(mockMessageId);

                log.info("TEST模式 - hboneNo: {} 模拟推送臻财公众号，产品名称: {}, 报告类型: {}, 报告标题: {}, 模拟消息ID: {}",
                        hboneNo, jjxxInfo.getJjjc(), report.getReportTypeName(), report.getReportTitle(), mockMessageId);
            }
        }

        result.append("推送条数: ").append(mockMessageIds.size())
                .append(", 消息ID: ").append(MessageBusinessIdConstant.ZC_OFFICIAL_ACCOUNT_REPORT_UPDATE);

        return result.toString();
    }

    /**
     * @param hboneNo             客户号
     * @param reportWithFundsList 报告与基金关联列表
     * @return String 推送结果描述
     * @description: 模拟推送[短信]，返回推送结果描述
     * @author: jin.wang03
     * @date: 2025-07-18
     * @since JDK 1.8
     */
    private String simulatePushSms(String hboneNo, List<ReportWithFunds> reportWithFundsList) {
        String mobile = queryAccCenterOuterService.getMobile(hboneNo);
        if (StringUtils.isEmpty(mobile)) {
            log.warn("TEST模式 - 客户{}手机号码为空，跳过发送短信", hboneNo);
            return "手机号码为空，跳过发送";
        }

        // 将List<报告>按照基金的分销渠道，分为 好买、好臻、香港三个List。
        Map<String, ProductReportUpdatePushDTO> hbMap = new HashMap<>();
        Map<String, ProductReportUpdatePushDTO> hzMap = new HashMap<>();
        Map<String, ProductReportUpdatePushDTO> hkMap = new HashMap<>();

        for (ReportWithFunds reportWithFunds : reportWithFundsList) {
            ProductReportUpdatePushDTO report = reportWithFunds.getReport();
            for (String fund : reportWithFunds.getFunds()) {
                JjxxInfo jjxxInfo = getJjxxInfoWithCache(fund);
                if (StaticVar.SFXG_YES.equals(jjxxInfo.getSfxg())) {
                    hkMap.put(fund, report);
                    continue;
                }

                DisChannelCodeEnum disCodeEnumByJjdm = getDisChannelWithCache(fund);
                if (Objects.nonNull(disCodeEnumByJjdm) && disCodeEnumByJjdm == DisChannelCodeEnum.HZ) {
                    hzMap.put(fund, report);
                    continue;
                }
                hbMap.put(fund, report);
            }
        }

        StringBuilder result = new StringBuilder();
        List<String> mockMessageIds = new ArrayList<>();

        // 模拟好买渠道短信推送
        if (!hbMap.isEmpty()) {
            String mockMessageId = simulatePushSMSByDisCode(mobile, hbMap, "好买渠道");
            mockMessageIds.add(mockMessageId);
        }

        // 模拟好臻渠道短信推送
        if (!hzMap.isEmpty()) {
            String mockMessageId = simulatePushSMSByDisCode(mobile, hzMap, "好臻渠道");
            mockMessageIds.add(mockMessageId);
        }

        // 模拟香港渠道短信推送
        if (!hkMap.isEmpty()) {
            String mockMessageId = simulatePushSMSByDisCode(mobile, hkMap, "香港渠道");
            mockMessageIds.add(mockMessageId);
        }

        mockMessageIds.forEach(result::append);

        log.info("TEST模式 - hboneNo: {} 模拟发送短信，根据分销渠道汇总，好买渠道:{}, 好臻渠道:{}, 香港渠道: {}",
                hboneNo, hbMap.size(), hzMap.size(), hkMap.size());

        return result.toString();
    }

    /**
     * @param mobile      手机号
     * @param channelMap  渠道基金映射
     * @param channelName 渠道名称
     * @return String 模拟消息ID
     * @description: 模拟按分销渠道推送短信
     * @author: jin.wang03
     * @date: 2025-07-18
     * @since JDK 1.8
     */
    private String simulatePushSMSByDisCode(String mobile, Map<String, ProductReportUpdatePushDTO> channelMap, String channelName) {
        StringBuilder mockMessageId = new StringBuilder("分销：" +channelName).append("\n");


        if (channelMap.size() == 1) {
            String fundCode = new ArrayList<>(channelMap.keySet()).get(0);
            JjxxInfo jjxxInfo = getJjxxInfoWithCache(fundCode);
            log.info("TEST模式 - {} 单基金短信推送，产品名称: {}, 模拟消息ID: {}", channelName, jjxxInfo.getJjjc(), mockMessageId);
        } else {
            log.info("TEST模式 - {} 多基金短信推送，基金数量: {}, 模拟消息ID: {}", channelName, channelMap.size(), mockMessageId);
        }

        for (Map.Entry<String, ProductReportUpdatePushDTO> entry : channelMap.entrySet()) {
            String fundCode = entry.getKey();
            ProductReportUpdatePushDTO report = entry.getValue();
            mockMessageId.append(fundCode).append("_").append(report.getReportTitle()).append("\n");
        }

        return mockMessageId.toString();
    }

}