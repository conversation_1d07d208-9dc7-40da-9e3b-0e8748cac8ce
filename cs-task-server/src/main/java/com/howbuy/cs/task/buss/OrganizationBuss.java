package com.howbuy.cs.task.buss;

import com.howbuy.cs.task.model.HbOrganization;
import com.howbuy.cs.task.dao.HbOrganizationMapper;
import com.howbuy.cs.task.model.CmConsultant;
import com.howbuy.cs.task.dao.CmConsultantMapper;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @description: 组织架构业务处理类
 * @author: hongdong.xie
 * @date: 2024/1/17
 */
@Service
public class OrganizationBuss {

    @Autowired
    private HbOrganizationMapper hbOrganizationMapper;

    @Autowired
    private CmConsultantMapper cmConsultantMapper;

    /**
     * 获取指定部门到顶层的所有部门列表(不包含部门编码为0的记录)
     * @param orgCode 部门编码
     * @return 部门列表，从当前部门到顶层部门的顺序
     */
    public List<HbOrganization> getOrganizationPath(String orgCode) {
        if (!StringUtils.hasText(orgCode)) {
            return new ArrayList<>();
        }
        return hbOrganizationMapper.selectOrgPathByOrgCode(orgCode);
    }

    /**
     * 获取单个组织机构信息
     *
     * @description 根据组织机构代码获取单个组织机构信息，如果不存在则返回null
     * @param orgCode 组织机构代码
     * @return 组织机构代码，不存在时返回null
     * <AUTHOR>
     * @date 2024-03-21 17:30:00
     */
    public String getOrganization(String orgCode) {
        if (!StringUtils.hasText(orgCode)) {
            return null;
        }
        List<HbOrganization> orgList = hbOrganizationMapper.selectOrgPathByOrgCode(orgCode);
        return Optional.ofNullable(orgList)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0).getOrgcode())
                .orElse(null);
    }

    /**
     * 根据投顾编码查询中心领导的用户ID
     *
     * @description 先通过投顾编码获取营业部编码，再获取最顶层组织机构代码，然后查询该组织下的中心领导用户ID
     * @param consCode 投顾编码
     * @return Pair<String,String> 返回值为Pair对象，left为顶层机构编码，right为中心领导用户ID；不存在时返回null
     * <AUTHOR>
     * @date 2024-03-21 16:30:00
     */
    public Pair<String,String> getCenterLeaderUserId(String consCode) {
        if (!StringUtils.hasText(consCode)) {
            return null;
        }

        // 通过投顾编码查询营业部编码
        CmConsultant consultant = cmConsultantMapper.selectByConsCode(consCode);
        if (consultant == null || !StringUtils.hasText(consultant.getOutletcode())) {
            return null;
        }

        // 获取组织机构路径
        List<HbOrganization> orgPath = getOrganizationPath(consultant.getOutletcode());
        if (CollectionUtils.isEmpty(orgPath)) {
            return null;
        }

        // 获取最顶层组织机构代码
        String topOrgCode = orgPath.get(0).getOrgcode();
        if (!StringUtils.hasText(topOrgCode)) {
            return null;
        }

        // 查询中心领导的userId
        String leadUserId = hbOrganizationMapper.selectCenterLeaderUserId(topOrgCode);
        if (!StringUtils.hasText(leadUserId)) {
            return null;
        }

        return Pair.of(topOrgCode, leadUserId);
    }
} 