package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.SyncBpFundBasicInfo;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface SyncBpFundBasicInfoMapper {
    int deleteByPrimaryKey(String fundCode);

    int insert(SyncBpFundBasicInfo record);

    int insertSelective(SyncBpFundBasicInfo record);

    SyncBpFundBasicInfo selectByPrimaryKey(String fundCode);

    int updateByPrimaryKeySelective(SyncBpFundBasicInfo record);

    int updateByPrimaryKey(SyncBpFundBasicInfo record);

    /**
     * @return int
     * @description: 从基金信息表同步基金信息
     * @author: hongdong.xie
     * @date: 2024/12/23 15:57
     * @since JDK 1.8
     */
    int insertFromJJXX1(@Param("createDate") String createDate);
}