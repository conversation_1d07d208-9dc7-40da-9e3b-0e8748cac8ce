package com.howbuy.cs.task.model;

import java.io.Serializable;
import lombok.Data;

/**
 * 理财通客户实体类
 */
@Data
public class TenpayCust implements Serializable {
	private static final long serialVersionUID = -3221696279702242598L;
	private String id;// 序号
	private String tenpayId;// 理财通序号
	private String tenpaycustNo;// 理财通客户号
	private String custName;// 姓名
	private String mobile;// 手机
	private String idType;// 证件类型
	private String idNo;// 证件号码
	private String email;// 邮箱
	private String confirmDt;// 确认日期
	private String remark;// 备注
	private String newSourceNo;// 客户来源
	private int recState;// 数据状态
	private int custNum;// 客户数
	private int succNum;// 导入成功数
	private int errorNum;// 导入失败数据
	private String batchId;// 批次号
	private String fileName;// 文件名称
	private String syncDate;// 导入日期（同步）
	private String syncStartDt;// 导入开始日期
	private String syncEndDt;// 导入结束日期	
	private String conscustNo;// 投顾客户号
	private String province;// 省份
	private String city;// 城市
	private String visittime;// 最近拜访日期
	private String visitSummary;// 拜访摘要
	private String assignConsDt;// 分配日期
	private String assignStartDt;// 分配开始日期
	private String assignEndDt;// 分配结束日期
	private String orgName;// 所属部门
	private String consCode;// 所属投顾编码
	private String consName;// 所属投顾名称
	private String regDt;// 开户日期

}
