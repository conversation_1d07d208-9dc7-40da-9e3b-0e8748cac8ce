<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.cs.task.dao.CsDistributeNumLogDao">

    <select id="getCsDistributeNumLog" resultType="com.howbuy.cs.task.model.CsDistributeNumLog">
      select *
		  from (select rownum r, f.*
		          from (select *
		                  from (SELECT *
		                          FROM CS_DISTRIBUTE_NUM_LOG
		                         order by creddt desc) e) f
		         where rownum <![CDATA[<=]]> 1 * 1)
		 where r > (1 - 1) * 1
    </select>
    
     
    <insert id="insertCsDistributeNumLog" parameterType="com.howbuy.cs.task.model.CsDistributeNumLog">
        insert into cs_distribute_num_log (id, hbcustnum, nhbcustnum, creddt)
         values
        (seq_cs_distribute_num_log.nextval, #{item.hbCustNum, jdbcType=INTEGER}, #{item.nHbCustNum, jdbcType=INTEGER}, SYSDATE)
    </insert>
   
</mapper>