package com.howbuy.cs.task.dao;

import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface CustConstantDao {

	/**
	 * 根据客户号查询  投顾code
	 * @param conscustNo not null
	 * @return
	 */
	String getConsCodeByCustNo(@Param("conscustNo") String  conscustNo);

	/**
	 * @description: 查询所有的投顾code
	 * @return java.util.List<java.lang.String>
	 * @author: hongdong.xie
	 * @date: 2024/11/25 16:48
	 * @since JDK 1.8
	 */
	List<String> selectAllConsCode();
}