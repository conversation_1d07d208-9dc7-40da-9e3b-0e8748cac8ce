/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.service;

import com.google.common.base.Throwables;
import com.howbuy.cs.task.dao.TransOwnerDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.CountDownLatch;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/9/13 下午2:01
 * @since JDK 1.8
 */
@Slf4j
public class TransCustConstantTask implements Runnable{
    private final String custNo;
    private final String newConsCode;
    private final CountDownLatch latch;
    private final AdvisorAutoTransTaskServiceImpl advisorAutoTransTaskService;
    private boolean noHis;

    public TransCustConstantTask(String custNo, String newConsCode, CountDownLatch latch,
                                 AdvisorAutoTransTaskServiceImpl advisorAutoTransTaskService,
                                 boolean noHis) {
        this.custNo = custNo;
        this.newConsCode = newConsCode;
        this.latch = latch;
        this.advisorAutoTransTaskService = advisorAutoTransTaskService;
        this.noHis = noHis;
    }

    @Override
    public void run() {
        try {
            if (noHis) {
                advisorAutoTransTaskService.updateCustConstantWithNoHis(custNo, newConsCode);
            } else {
                advisorAutoTransTaskService.copyHisAndUpdateCustConstant(custNo, newConsCode);
            }
        } catch (Exception e) {
            log.error("客户投顾划转第一部分异常,custNo={},ex:{}", custNo, Throwables.getStackTraceAsString(e));
        } finally {
            latch.countDown();
        }
    }
}