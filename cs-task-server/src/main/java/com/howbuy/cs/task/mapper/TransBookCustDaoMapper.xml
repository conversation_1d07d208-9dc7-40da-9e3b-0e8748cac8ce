<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.cs.task.dao.TransBookCustDao">
	<!-- VO类与数据库表字段映射 -->
    <resultMap id="transBookCustModel" type="com.howbuy.cs.task.model.TransBookCustModel">
        <result column="waitid" property="waitid"/>
        <result column="userid" property="userid"/>
        <result column="calloutstatus" property="calloutstatus"/>
        <result column="handle_flag" property="handleflag"/>
        <result column="conscustno" property="conscustno"/>
        <result column="custname" property="custname"/>
        <result column="reg_outlet_code" property="regoutletcode"/>
        <result column="hbone_reg_outlet_code" property="hboneregoutletcode"/>
        <result column="newsourceno" property="newsourceno"/>
        <result column="existcount" property="existcount"/>
        <result column="mobile_cipher" property="mobilecipher"/>
        <result column="mobile_mask" property="mobilemask"/>
        <result column="mobile_digest" property="mobiledigest"/>
        <result column="email_cipher" property="emailcipher"/>
        <result column="email_mask" property="emailmask"/>
        <result column="email_digest" property="emaildigest"/>
    </resultMap>

	<!-- VO类与数据库表字段映射 -->
    <resultMap id="transMobileCustModel" type="com.howbuy.cs.task.model.TransMobileCustModel">
        <result column="conscustno" property="conscustno"/>
        <result column="mobile_mask" property="mobilemask"/>
        <result column="linkmobile_mask" property="linkmobilemask"/>
        <result column="linktel_mask" property="linktelmask"/>
    </resultMap>

    <select id="getTransBookCust" resultMap="transBookCustModel" useCache="false">
        select t1.waitid,
        	   t1.userid,
		       t1.calloutstatus,
		       t1.handle_flag as handleflag,
		       t2.conscustno,
		       t2.custname,
		       t2.mobile_cipher,
		       t2.mobile_mask,
		       t2.mobile_digest,
		       t2.email_cipher,
		       t2.email_mask,
		       t2.email_digest,
		       t3.reg_outlet_code as regoutletcode,
		       t3.hbone_reg_outlet_code as hboneregoutletcode,
		       t4.newsourceno,
		       (select count(1)
		          from cm_conscust c
		         where c.conscuststatus = '0'
		           and (
		           		c.mobile_digest = t2.mobile_digest or
		                c.mobile2_digest = t2.mobile_digest or
		                c.linkmobile_digest = t2.mobile_digest or
		                c.conscustno = t2.conscustno
		           )
		        ) existcount
		  from cs_task_assign_day t1
		  left join cs_callout_waitdistribute t2
		    on t1.waitid = t2.waitid
		  left join cm_bookingcust t3
    		on t3.id = t2.taskid
		  left join cm_bookingtypesource t4
		  	on t4.bookingtype = t3.bookingtype
		   and t4.activitytype = t3.activitytype
		   and t4.wireless_channel = t3.wireless_channel
		   and t4.qualified_status = t3.qualified_status
		   and t4.recstat = '0'		  
		 where t1.handle_flag != 0
		   and t1.calloutstatus in (3, 4, 5, 7, 8)
		   and t2.task_type = 1001
		   and t2.disposenum = 4
		   and t2.handle_state = 1
		   order by t2.source_dt asc ,t2.waitdate asc
    </select>

	<select id="getTransBookCustByTaskId" resultMap="transBookCustModel" useCache="false" >
		select t1.waitid,
        	   t1.userid,
		       t1.calloutstatus,
		       t1.handle_flag as handleflag,
		       t2.conscustno,
		       t2.custname,
		       t2.mobile_cipher,
		       t2.mobile_mask,
		       t2.mobile_digest,
		       t2.email_cipher,
		       t2.email_mask,
		       t2.email_digest,
		       t3.reg_outlet_code as regoutletcode,
		       t3.hbone_reg_outlet_code as hboneregoutletcode,
		       t4.newsourceno,
		       (select count(1)
		          from cm_conscust c
		         where c.conscuststatus = '0'
		           and (
		           		c.mobile_digest = t2.mobile_digest or
		                c.mobile2_digest = t2.mobile_digest or
		                c.linkmobile_digest = t2.mobile_digest or
		                c.conscustno = t2.conscustno
		           )
		        ) existcount
		  from cs_task_assign_day t1
		  left join cs_callout_waitdistribute t2    on t1.waitid = t2.waitid
		  left join cm_bookingcust t3	on t3.id = t2.taskid
		  left join cm_bookingtypesource t4
		  	on t4.bookingtype = t3.bookingtype
		   and t4.activitytype = t3.activitytype
		   and t4.wireless_channel = t3.wireless_channel
		   and t4.qualified_status = t3.qualified_status
		   and t4.recstat = '0'
		 where t1.handle_flag != 2
		   <!--and t1.calloutstatus in (3, 4, 5, 7, 8) -->
		   and t2.task_type = 1001
		   and t2.disposenum = 3 <!-- 只针对T+7的处理-->
		   and t2.handle_state = 1
		   and t1.taskid = #{taskId, jdbcType=INTEGER}
	</select>

	<select id="getTransMobileCust" resultMap="transMobileCustModel" useCache="false">
        select a.conscustno
		  from cm_conscust a
		  join cm_custconstant b
			on a.conscustno = b.custno
		 where conscuststatus = '0'
		   and (a.mobile_mask is null or length(a.mobile_mask) != 11)
		   and (a.linkmobile_mask is null or length(a.linkmobile_mask) != 11)
		   and (a.linktel_mask is null)
		   and b.conscode in ('weijietong',
							  'shenqianku',
							  'wuyixiang',
							  'qkslk',
							  'GJ',
							  'wurenjieting',
							  'ZTFW',
							  'miaogua')
    </select>
    
    <update id="updateTransBookFlag" parameterType="com.howbuy.cs.task.model.TransBookCustModel" >
       update cs_callout_waitdistribute t
	      set t.handle_state    = 2,
	          t.distribute_flag = 1,
	          <!--t.disposenum      = 4,-->
	          t.disposedate     = to_date(to_char(sysdate, 'yyyy-mm-dd'), 'yyyy-mm-dd'),
	          t.conscustno      = #{conscustno, jdbcType=VARCHAR}
	    where t.waitid = #{waitid, jdbcType=INTEGER}
    </update>

	<update id="updateDealedTaskAssignDay" parameterType="map" >
		update CS_TASK_ASSIGN_DAY
		set handle_flag ='2', <!--2-已处理-->
		HANDLEDATE = SYSDATE,
		CALLOUTSTATUS = #{calloutStatus, jdbcType=VARCHAR}
		where taskId  = #{taskId, jdbcType=INTEGER}
	</update>


</mapper>