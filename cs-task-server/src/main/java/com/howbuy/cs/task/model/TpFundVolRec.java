package com.howbuy.cs.task.model;

import java.math.BigDecimal;
import java.util.Date;

/**
 * TP客户份额对账文件记录表
 */
public class TpFundVolRec {
    /**
    * 记录号
    */
    private String recordNo;

    /**
    * 交易账号
    */
    private String txAcctNo;

    /**
    * 基金交易账号
    */
    private String fundTxAcctNo;

    /**
    * 分销机构
    */
    private String disCode;

    /**
    * 资金账号
    */
    private String cpAcctNo;

    /**
    * 基金代码
    */
    private String fundCode;

    /**
    * 基金份额类型
    */
    private String fundShareClass;

    /**
    * TA代码
    */
    private String taCode;

    /**
    * TA日期
    */
    private String taDate;

    /**
    * 总余额
    */
    private BigDecimal balanceVol;

    /**
    * 可用余额
    */
    private BigDecimal availVol;

    /**
    * 冻结余额
    */
    private BigDecimal frznVol;

    /**
    * 司法冻结份额
    */
    private BigDecimal justFrznVol;

    /**
    * 协议号
    */
    private String protocolNo;

    /**
    * 创建日期时间
    */
    private Date createDtm;

    /**
    * 更新日期时间
    */
    private Date updateDtm;

    public String getRecordNo() {
        return recordNo;
    }

    public void setRecordNo(String recordNo) {
        this.recordNo = recordNo;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getFundTxAcctNo() {
        return fundTxAcctNo;
    }

    public void setFundTxAcctNo(String fundTxAcctNo) {
        this.fundTxAcctNo = fundTxAcctNo;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundShareClass() {
        return fundShareClass;
    }

    public void setFundShareClass(String fundShareClass) {
        this.fundShareClass = fundShareClass;
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }

    public String getTaDate() {
        return taDate;
    }

    public void setTaDate(String taDate) {
        this.taDate = taDate;
    }

    public BigDecimal getBalanceVol() {
        return balanceVol;
    }

    public void setBalanceVol(BigDecimal balanceVol) {
        this.balanceVol = balanceVol;
    }

    public BigDecimal getAvailVol() {
        return availVol;
    }

    public void setAvailVol(BigDecimal availVol) {
        this.availVol = availVol;
    }

    public BigDecimal getFrznVol() {
        return frznVol;
    }

    public void setFrznVol(BigDecimal frznVol) {
        this.frznVol = frznVol;
    }

    public BigDecimal getJustFrznVol() {
        return justFrznVol;
    }

    public void setJustFrznVol(BigDecimal justFrznVol) {
        this.justFrznVol = justFrznVol;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public Date getCreateDtm() {
        return createDtm;
    }

    public void setCreateDtm(Date createDtm) {
        this.createDtm = createDtm;
    }

    public Date getUpdateDtm() {
        return updateDtm;
    }

    public void setUpdateDtm(Date updateDtm) {
        this.updateDtm = updateDtm;
    }
}