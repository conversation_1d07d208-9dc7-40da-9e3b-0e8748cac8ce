package com.howbuy.cs.task.model;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 实体类TransBookCustModel.java
 * @version 1.0
 */
@Data
public class TransBookCustModel implements Serializable {

	private static final long serialVersionUID = 1L;

	private Integer waitid;
	
	private String userid;
	
	private String calloutstatus;
	
	private String handleflag;
	
	private String conscustno;
	
	private String custname;
	
	private String mobile;
	
	private String email;
	
	private String newsourceno;
	
	private String regoutletcode;
	
	private String hboneregoutletcode;
	
	private Integer existcount;
	
	private String mobilecipher;
	
	private String mobilemask;
	
	private String mobiledigest;
	
	private String emailcipher;
	
	private String emailmask;
	
	private String emaildigest;
	
}
