package com.howbuy.cs.task.service;

import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.howbuy.cs.task.dao.RecyTaskDao;
import com.howbuy.cs.task.model.CsCalloutWaitDistribute;

@Service("agentRecycleTaskService")
public class AgentRecycleTaskServiceImpl implements AgentRecycleTaskService {
	private static final Logger log = LoggerFactory.getLogger(AgentRecycleTaskServiceImpl.class);
	
	@Autowired
    private RecyTaskDao recyTaskDao;
	 
	public Map<String, List<CsCalloutWaitDistribute>> taskMap = null;
	
	/**
	 * 坐席任务回收方法
	 */
	@Override
	public void agentTaskRecycle(String arg) {
		log.info("坐席任务回收方法开始执行。。。");
		boolean updateOne;
        boolean updateTwo = false;
        boolean updateThree = false;
        //int upCallOUtstatusCount = 0;

        // 将未处理和再处理的task从新置成未分配
        updateOne = recyTaskDao.updateDistributeFlag() >= 0;
        if (updateOne) {
            // 将今日处理过的任务 插入到历史表中
            updateTwo = recyTaskDao.insertDataTransferToHis() >= 0;
            log.info("待分配表中标识符更新完成");
        } else {
            log.info("待分配表中标识符更新失败");
        }

        if (updateTwo) {
        	//回写状态
        	//upCallOUtstatusCount = recyTaskDao.mergeUpCalloutstatus();
        	//log.info("回写cs_callout_waitdistribute表中状态条数："+upCallOUtstatusCount);
            // 删除今日表中的数据
            updateThree = recyTaskDao.deleteCsTaskAssignDay() >= 0;
            log.info("将今日处理过的任务插入到历史表中完成");
        } else {
            log.info("将今日处理过的任务插入到历史表中失败");
        }

        if (updateThree) {
            log.info("删除今日表中的数据完成");
        } else {
            log.info("删除今日表中的数据失败");
        }
		log.info("坐席任务回收方法执行结束。。。");
	}

}