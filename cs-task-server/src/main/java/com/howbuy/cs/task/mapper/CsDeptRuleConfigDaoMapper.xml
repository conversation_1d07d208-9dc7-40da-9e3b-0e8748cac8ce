<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.cs.task.dao.CsDeptRuleConfigDao">

    <select id="getValidateAgentByAssignRate" resultType="com.howbuy.cs.task.model.CsDeptRule">
      select *
		  from (select rownum r, f.*
		          from (select *
		                  from (SELECT * FROM cs_dept_rule_config order by id desc) e) f
		         where rownum <![CDATA[<=]]> 1 * 1)
		 where r > (1 - 1) * 1
    </select>
   
</mapper>