package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.CmStockSplitConfig;
import com.howbuy.cs.task.model.CmStockSplitConfigTmp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-03-26 14:35:00
 * @description 存量分成配置临时表Mapper接口
 */
@Mapper
public interface CmStockSplitConfigTmpMapper {
    int deleteByPrimaryKey(String id);

    int insert(CmStockSplitConfigTmp record);

    int insertSelective(CmStockSplitConfigTmp record);

    CmStockSplitConfigTmp selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CmStockSplitConfigTmp record);

    int updateByPrimaryKey(CmStockSplitConfigTmp record);

    /**
     * 初始化临时表
     */
    void initTempTable();

    /**
     * @description: 插入存量分成配置到临时表
     * @param config 存量分成配置对象
     * @author: hongdong.xie
     * @date: 2024-03-26 14:35:00
     */
    void insertToTemp(CmStockSplitConfigTmp config);

    /**
     * @description: 根据时间范围查询临时表中的所有配置
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 配置列表
     * @author: hongdong.xie
     * @date: 2024-03-26 14:35:00
     */
    List<CmStockSplitConfigTmp> selectAllFromTempByDateRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * @description: 检查是否存在有效的客户和管理层配置（临时表）
     * @param custNoList 客户号列表
     * @param managementCode 管理层代码
     * @param managementLevel 管理层级别
     * @param lastMonthEndDate 上月最后一天
     * @return 已存在有效配置的客户号列表
     * @author: hongdong.xie
     * @date: 2024-03-26 14:35:00
     */
    List<String> checkExistsValidConfig(@Param("custNoList") List<String> custNoList,
                                      @Param("managementCode") String managementCode,
                                      @Param("managementLevel") String managementLevel,
                                      @Param("lastMonthEndDate") String lastMonthEndDate);

    /**
     * @description 批量检查客户是否存在于临时表的有效配置中
     * @param custNos 客户号列表
     * @param newlyConsCode 新投顾代码
     * @param configLevel 配置层级
     * @param calStartDt 计算开始日期
     * @return 已存在的客户号列表
     * <AUTHOR>
     * @date 2024-03-26 15:35:00
     */
    List<String> batchCheckCustomersExistInTempConfig(
        @Param("custNos") List<String> custNos,
        @Param("newlyConsCode") String newlyConsCode,
        @Param("configLevel") String configLevel,
        @Param("calStartDt") String calStartDt
    );

    /**
     * @description: 查询临时表中根据客户号、层级和新投顾代码查询配置信息
     * 按照日期排序，最早的配置在最前面
     * @param custNo 客户号
     * @param configLevel 配置层级
     * @param newlyConsCode 新投顾代码
     * @param startDate 计算开始日期
     * @param endDate 计算结束日期
     * @return 配置信息列表（按CAL_START_DT升序排序）
     * @author: hongdong.xie
     * @date: 2024-07-01 14:35:00
     */
    List<Map<String, Object>> findConfigsByCustNoAndLevelOrderByStartDate(
        @Param("custNo") String custNo,
        @Param("configLevel") String configLevel,
        @Param("newlyConsCode") String newlyConsCode,
        @Param("startDate") String startDate,
        @Param("endDate") String endDate
    );
    
    /**
     * @description: 更新临时表中的记录状态
     * @param id 配置ID
     * @param recStat 记录状态
     * @author: hongdong.xie
     * @date: 2024-07-01 14:35:00
     */
    void updateRecStatInTemp(@Param("id") String id, @Param("recStat") String recStat);

    /**
     * @description: 更新所有没有有效明细的配置状态为无效
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param recStat 记录状态
     * @author: hongdong.xie
     * @date: 2024-07-01 16:35:00
     */
    void updateConfigWithNoValidDetailsInTemp(
        @Param("startDate") String startDate,
        @Param("endDate") String endDate,
        @Param("recStat") String recStat
    );
}