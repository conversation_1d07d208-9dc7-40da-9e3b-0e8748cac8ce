/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.service;

import com.howbuy.cs.task.dao.TpFundVolRecMapper;
import com.howbuy.cs.task.model.TpFundVolRec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: TP份额明细记录表服务实现类
 * <AUTHOR>
 * @date 2024/11/21 14:42
 * @since JDK 1.8
 */
@Service("tpFundVolRecServiceImpl")
public class TpFundVolRecServiceImpl {
    @Autowired
    private TpFundVolRecMapper tpFundVolRecMapper;

    /**
     * @description: 根据基金代码删除导入的份额记录
     * @param fundCode 基金代码
     * @return int
     * @author: hongdong.xie
     * @date: 2024/11/21 19:45
     * @since JDK 1.8
     */
    public int deleteByFundCode(String fundCode) {
        return tpFundVolRecMapper.deleteByFundCode(fundCode);
    }

    /**
     * @description: 批量插入
     * @param list
     * @return int
     * @author: hongdong.xie
     * @date: 2024/11/22 10:43
     * @since JDK 1.8
     */
    public int insertBatch(List<TpFundVolRec> list) {
        return tpFundVolRecMapper.batchInsert(list);
    }

}