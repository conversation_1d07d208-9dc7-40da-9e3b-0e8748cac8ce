package com.howbuy.cs.task.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @version 1.0
 * @Description: 实体类CsDeptAssignRuleOrgConfig.java
 */
@Data
public class CsDeptAssignRuleOrgConfig implements Serializable {

	private static final long serialVersionUID = 2265594515510810310L;

	private BigDecimal id;

    private BigDecimal roleconfigid;

    private String orgcode;

    private String orgname;

    private BigDecimal payoutratio;

    private BigDecimal payoutupper;

    private Date createdt;

    private Date modifydt;
    
    private String modifier;
    
    private String creator;

    private String isdel;
    
}
