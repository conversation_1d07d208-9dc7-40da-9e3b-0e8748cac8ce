package com.howbuy.cs.task.util;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.MDC;

import java.util.UUID;

/**
 * @className LoggerUtils
 * @description 日志工具类
 * <AUTHOR>
 * @date 2019-05-28 下午7:37:35
 *
 */
public class LoggerUtils {

    private static final String DEFAULT = "default";
    public static final String CTX_UUID = "uuid";
    public static final String CTX_TXCODE = "txCode";
    public static final String REQ_ID = "reqId";
    public static final String CTX_RANNO = "ranNo";

    /**
     * @description: 设置随机号
     * @param ranNo 随机号
     * @return void
     * @author: hongdong.xie
     * @date: 2025-04-03 10:00:00
     */
    public static void setRanNo(String ranNo) {
        ThreadContext.put(CTX_RANNO, ranNo);
    }

    /**
     * @description: 获取随机号
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2025-04-03 10:00:00
     */
    public static String getRanNo() {
        return ThreadContext.get(CTX_RANNO);
    }

    /**
     * @description: 设置请求ID
     * @param reqId 请求ID
     * @return void
     * @author: hongdong.xie
     * @date: 2025-04-03 10:00:00
     */
    public static void setReqId(String reqId) {
        ThreadContext.put(CTX_UUID, reqId);
    }

    /**
     * @description: 获取请求ID
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2025-04-03 10:00:00
     */
    public static String getReqId() {
        return ThreadContext.get(CTX_UUID);
    }

    /**
     * setConfig:log4j2 ctx 配置属性设置
     *
     * @param uuid
     * @param txCode
     * <AUTHOR>
     * @date 2016-9-15 下午3:31:27
     */
    public static void setConfig(String uuid, String txCode) {
        if (uuid != null) {
            ThreadContext.put(CTX_UUID, uuid);
        } else {
            ThreadContext.put(CTX_UUID, DEFAULT);
        }

        if (txCode != null) {
            ThreadContext.put(CTX_TXCODE, txCode);
        } else {
            ThreadContext.put(CTX_TXCODE, DEFAULT);
        }
    }

    /**
     *
     * setUUID:设置UUID
     * 
     * @return void
     * <AUTHOR>
     * @date 2016年10月26日 上午10:51:05
     */
    public static void setUUID() {
        String uuid = UUID.randomUUID().toString();
        ThreadContext.put(CTX_UUID, uuid);
    }

    /**
     * 如果进入当前线程时，没有UUID，则设置UUID
     */
    public static void setUUIDIfAbsent() {
        String uuid = getUuid();
        if (StringUtils.isBlank(uuid)) {
            setUUID();
        }
    }

    /**
     * getCfgValue:(获取ThreadContext config 配置的值)
     * 
     * @param key
     * @return String
     * <AUTHOR>
     * @date 2016年9月29日 下午6:51:18
     */
    public static String getCfgValue(String key) {
        return ThreadContext.get(key);
    }
    
    /**
     * setCfgValue:(设置ThreadContext值)
     * @param key
     * @return String
     */
    public static void setCfgValue(String key, String value) {
        ThreadContext.put(key, value);
    }

    /**
     * getUuid:(获取uuid)
     * 
     * @return String
     * <AUTHOR>
     * @date 2016年9月29日 下午8:08:49
     */
    public static String getUuid() {
        return ThreadContext.get(CTX_UUID);
    }

    /**
     * clearConfig:清空配置
     * 
     * @return void
     * <AUTHOR>
     * @date 2016年10月9日 下午6:46:24
     */
    public static void clearConfig() {
        ThreadContext.clearAll();
    }

    /**
     * @description: 设置子UUID
     * @param uuid 父线程UUID
     * @return void
     * @author: hongdong.xie
     * @date: 2020/11/3 11:12
     * @since JDK 1.8
     */
    public static void setChildUUID(String uuid) {
        // 如果参数UUID为空则自动生成一个新的UUID
        if(StringUtils.isEmpty(uuid)){
            uuid = UUID.randomUUID().toString();
        }

        //设置子uuid，规则为父UUID+3位随机字符串
        uuid = uuid + "_" + RandomStringUtils.randomAlphanumeric(3).toLowerCase();
        ThreadContext.put(CTX_UUID, uuid);
    }

    /**
     * @description: 获取随机号，规则为7位随机数字
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025-04-02 20:50:47
     * @since JDK 1.8
     */
    public static String createRanNo() {
        //生成7位随机数字
        return RandomStringUtils.randomNumeric(7);
    }
}