package com.howbuy.cs.task.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.cc.message.SendMsgResult;
import com.howbuy.cc.message.SendMsgService;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import com.howbuy.cs.base.Constants;
import crm.howbuy.base.dubbo.response.BaseResponse;
import crm.howbuy.base.utils.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("sendSmsService")
public class SendSmsService {	
	@Autowired
	private SendMsgService sendMessageDubboService;
	@Autowired
	private CmPushMsgService cmPushMsgService;
	
	public String sendSms(String[] phones, String channel, int actionId, Map<String, String> parameterMap) {
		String results = "";
		try {
			SendMsgResult sendMsgResult = null;
			for (int i = 0; i < phones.length; i++) {
				sendMsgResult = sendMessageDubboService.sendTemplateMsgByPhone(String.valueOf(actionId),
						JSON.toJSONString(parameterMap), phones[i], null, null);
				if (sendMsgResult.getCode() != 0) {
					return "-1|请求失败!";
				}
			}
			if (sendMsgResult.getCode() == 0) {
				results = "0000|发送成功！";
			}
		} catch (Exception e) {
			results = "-1|请求失败!";
		}

		return results;
	}
	
	public String sendEmail(String[] emails, int actionId, Map<String, String> parameterMap) {
		String results = "";
		try {
			SendMsgResult sendMsgResult = null;
			for (int i = 0; i < emails.length; i++) {
				sendMsgResult = sendMessageDubboService.sendTemplateMsgByEmail(String.valueOf(actionId),
						JSON.toJSONString(parameterMap), emails[i], null, null, null);
				if (sendMsgResult.getCode() != 0) {
					return "-1|请求失败!";
				}
			}
			if (sendMsgResult.getCode() == 0) {
				results = "0000|发送成功！";
			}

		} catch (Exception e) {
			results = "-1|请求失败!";
		}

		return results;
	}

	/**
	 * 给投顾发送站内信&企业微信消息
	 * @param templateId	模版id
	 * @param consCodeList	投顾编码列表
	 * @param parameterMap	参数
	 * @return	状态
	 */
	public String sendWechat(String templateId, List<String> consCodeList, Map<String, String> parameterMap) {
		if (StringUtil.isEmpty(templateId) || CollectionUtils.isEmpty(consCodeList)) {
            return "-1|请求失败!";
        }

		BaseResponse baseResponse = cmPushMsgService.pushMsgByConsCodeList(Constants.HK_MSG_TEMPLATE_ID_201695, consCodeList, parameterMap);
        if (baseResponse.isSuccessful()) {
            return "0000|发送成功！";
        } else {
            return "-1|请求失败!";
        }
	}
}
