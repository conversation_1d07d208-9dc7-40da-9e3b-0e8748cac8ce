<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.cs.task.dao.SendMsgDao">
	<select id="getNeedSendInfo" resultType="com.howbuy.cs.task.model.SendMsgInfo" useCache="false">
        select a.id,
		       a.advisorid,
		       a.advisorname,
		       b.mobile as advisorMobile,
		       b.email,
		       b.telno as advisorTelno,
		       a.orgcode,
		       a.custno,
		       a.custname,
		       (case
		         when c.gender is not null and c.gender=0 then
		          '女'
		         when c.gender is not null and c.gender=1 then
		          '男'
		         else
		          '未知'
		       end) genderInfo,
		       a.aemailflag,
		       a.asmsflag,
		       a.custsmsflag,
		       a.custsendflag,
			   e.MOBILE_AREA_CODE as mobileAreaCode,
		       a.custmobilecipher as custMobileCipher,
			   e.EMAIL_CIPHER as custEmailCipher,
			   e.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as bookingContent,
			   e.<PERSON><PERSON><PERSON><PERSON> as disCode
		  from cs_autodistribute_log a
		   left join cm_consultant b on a.advisorid = b.conscode
		   left join cm_conscust c on a.custno = c.conscustno
		   left join CS_CALLOUT_WAITDISTRIBUTE d on a.WAITID = d.WAITID
		   left join CM_BOOKINGCUST e on d.TASKID = e.id
		 where a.pubcustflag = 0
		   and a.orgcode in (select orgcode
		                       from hb_organization t
		                      where t.status = '0'
		                      start with t.orgcode = '1'
		                     connect by prior orgcode = parentorgcode
		                     union
		                     select orgcode
		                       from hb_organization t
		                      where t.status = '0'
		                      start with t.orgcode = '10'
		                     connect by prior orgcode = parentorgcode)
		   and a.DEAL_PUSH_MSG_FLAG = '0'
		   and a.advisorid = b.conscode
		   and a.custno = c.conscustno
    </select>

    <select id="getCustContent" parameterType="String" resultType="String" useCache="false">
        select f_cs_usermark_detail(specialmark,
                            commintent,
                            investintent,
                            amountflag) || ' ' || commcontent as content
		  from (select a.*, row_number() over(partition by conscustno order by id desc) rowno from cs_communicate_visit a where a.hisflag='1')
		 where rowno = 1
		   and conscustno = #{conscustNo, jdbcType = VARCHAR}
    </select>
    
    <update id="updateSendSmsFlag" parameterType="Map">
	     update cs_autodistribute_log set ASMSFLAG=1, CUSTSMSFLAG=1 
         where id = #{logId, jdbcType=BIGINT}
    </update>
    
    <update id="updateSendEmailFlag" parameterType="Map">
	     update cs_autodistribute_log set AEMAILFLAG=1 
         where id = #{logId, jdbcType=BIGINT}
    </update>

	<update id="updateDealPushMsgFlag" parameterType="long">
		update cs_autodistribute_log
		set DEAL_PUSH_MSG_FLAG = '1', DEAL_PUSH_MSG_DT = sysdate
		where id = #{id, jdbcType=BIGINT}
	</update>

    <insert id="addSendLog" parameterType="com.howbuy.cs.task.model.CsSendMsgLog">
        insert into cs_send_msg_log
		  (id, receiver, senddate, sendflag, remark, sendtype)
		values
		  (seq_cs_send_msg_log.nextval, #{receiver, jdbcType = VARCHAR}, sysdate, #{sendFlag, jdbcType=INTEGER}, #{remark, jdbcType=VARCHAR}, #{sendType, jdbcType=INTEGER})
    </insert>

</mapper>