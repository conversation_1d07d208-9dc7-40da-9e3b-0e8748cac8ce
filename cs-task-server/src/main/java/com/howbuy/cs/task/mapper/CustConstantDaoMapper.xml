<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.cs.task.dao.CustConstantDao">

	<select id="getConsCodeByCustNo" parameterType="string" resultType="string" useCache="false">
		SELECT A.CONSCODE
        FROM CM_CONSCUST T
        LEFT JOIN CM_CUSTCONSTANT A    ON T.CONSCUSTNO = A.CUSTNO
        <where>
			AND T.CONSCUSTNO = #{conscustNo, jdbcType = VARCHAR}
		</where>

	</select>

	<select id="selectAllConsCode" resultType="java.lang.String">
		SELECT userid FROM cm_consultant_exp order by userid
	</select>
</mapper>