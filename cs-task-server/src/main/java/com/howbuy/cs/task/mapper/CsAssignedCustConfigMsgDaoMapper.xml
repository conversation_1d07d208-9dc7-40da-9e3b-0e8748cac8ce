<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.cs.task.dao.CsAssignedCustConfigMsgDao">

      <select id="listCsAssignedCustConfigMsg" parameterType="Map" resultType="com.howbuy.cs.task.model.CsAssignedCustConfigMsg" useCache="false">
	    SELECT * FROM CS_ASSIGNED_CUST_CONFIG_MSG
		where 1=1
		<if test="id != null"> AND id = #{id} </if>
		<if test="stat != null"> AND stat = #{stat} </if> 
		<if test="stats != null and stats.size() > 0" > 
		    AND stat in (
		      <foreach item="item" index="index" collection="stats" separator=",">
	               #{item}
	          </foreach>
		    )
		</if> 
		order by  createdt asc    
      </select>
      
      
     <select id="selOrgUsed" parameterType="Map" resultType="Map" useCache="false">
	     SELECT orgconfigid as orgconfigid,count(1) as usedcount 
	     FROM CS_ASSIGNED_CUST_CONFIG_LOG  
	     where stat ='2' 
	     group by orgconfigid   
     </select>
      
    <update id="updateInvalidCust" >
         update CS_ASSIGNED_CUST_CONFIG_MSG a
         set a.stat = '3'
         where a.stat in ('0','1')
         and ( exists (select 1 from cm_conscust b where a.conscustno = b.conscustno and b.conscuststatus = '1')  
               or not exists(select 1 from cm_conscust c where a.conscustno = c.conscustno)
               or exists (select 1 from hb_organization d where a.orgcode =d.orgcode  and d.status = '1')
               or not exists(select 1 from hb_organization e where a.orgcode =e.orgcode )
	           or not exists (select 1 from cm_custconstant s  where a.conscustno = s.custno  and s.conscode = 'leadsFPK')
          )
     </update>
     
       
    <update id="updateBatchCsAssignedCustConfigMsg" parameterType="java.util.List">
	      <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
	         update CS_ASSIGNED_CUST_CONFIG_MSG
	         set
		          stat = #{item.stat,jdbcType=VARCHAR},
		          conscode = #{item.conscode,jdbcType=VARCHAR},
		          consname = #{item.consname,jdbcType=VARCHAR},
		          modifydt = #{item.modifydt,jdbcType=TIMESTAMP},
		          modifier = #{item.modifier,jdbcType=VARCHAR},
		          custconstanthis = #{item.custconstanthis,jdbcType=VARCHAR}
	         where id = #{item.id,jdbcType=NUMERIC}
       	  </foreach>
     </update> 
        
        
    <update id="mergeCsAssignedCustConfigMsgList" parameterType="java.util.List">
          <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
	            MERGE INTO CS_ASSIGNED_CUST_CONFIG_MSG t
				using (select #{item.conscustno} as conscustno from dual) t1
				on (t.conscustno = t1.conscustno)
				when not matched then
				INSERT
				<trim prefix="(" suffix=")" suffixOverrides=",">
					id,
					<if test="item.orgconfigid != null"> orgconfigid, </if>
					<if test="item.conscustno != null"> conscustno, </if>
					<if test="item.custname != null"> custname, </if>
					<if test="item.orgcode != null"> orgcode, </if>
					<if test="item.orgname != null"> orgname, </if>
					<if test="item.procode != null"> procode, </if>
					<if test="item.citycode != null"> citycode, </if>
					<if test="item.createdt != null"> createdt, </if>
					<if test="item.creator != null"> creator, </if>
					<if test="item.stat != null"> stat, </if>
					<if test="item.tasktype != null"> tasktype, </if>
					<if test="item.waitid != null"> waitid, </if>
					<if test="item.statistictype != null"> statistictype, </if>
					<if test="item.custsmsflag != null"> custsmsflag, </if>
					<if test="item.invsttype != null"> invsttype, </if>
					<if test="item.idtype != null"> idtype, </if>
					conscode,
					consname,
					<if test="item.idnoMask != null"> idnoMask, </if>
					<if test="item.idnoDigest != null"> idnoDigest, </if>
					<if test="item.idnoCipher != null"> idnoCipher, </if>
					<if test="item.custmobileMask != null"> custmobileMask, </if>
					<if test="item.custmobileDigest != null"> custmobileDigest, </if>
					<if test="item.custmobileCipher != null"> custmobileCipher, </if>
					<if test="item.specialmark != null"> specialmark, </if>
				</trim>
				VALUES
				<trim prefix="(" suffix=")" suffixOverrides=",">
					SEQ_CS_ASSIGNED_CUST_CONFIG.NEXTVAL,
					<if test="item.orgconfigid != null"> #{item.orgconfigid,jdbcType=NUMERIC}, </if>
					<if test="item.conscustno != null"> #{item.conscustno,jdbcType=VARCHAR}, </if>
					<if test="item.custname != null"> #{item.custname,jdbcType=VARCHAR}, </if>
					<if test="item.orgcode != null"> #{item.orgcode,jdbcType=VARCHAR}, </if>
					<if test="item.orgname != null"> #{item.orgname,jdbcType=VARCHAR}, </if>
					<if test="item.procode != null"> #{item.procode,jdbcType=VARCHAR}, </if>
					<if test="item.citycode != null"> #{item.citycode,jdbcType=VARCHAR}, </if>
					<if test="item.createdt != null"> #{item.createdt,jdbcType=TIMESTAMP}, </if>
					<if test="item.creator != null"> #{item.creator,jdbcType=VARCHAR}, </if>
					<if test="item.stat != null"> #{item.stat,jdbcType=VARCHAR}, </if>
					<if test="item.tasktype != null"> #{item.tasktype,jdbcType=VARCHAR}, </if>
					<if test="item.waitid != null"> #{item.waitid,jdbcType=VARCHAR}, </if>
					<if test="item.statistictype != null"> #{item.statistictype,jdbcType=VARCHAR}, </if>
					<if test="item.custsmsflag != null"> #{item.custsmsflag,jdbcType=VARCHAR}, </if>
					<if test="item.invsttype != null"> #{item.invsttype,jdbcType=VARCHAR}, </if>
					<if test="item.idtype != null"> #{item.idtype,jdbcType=VARCHAR}, </if>
					null,
					null,
					<if test="item.idnoMask != null"> #{item.idnoMask,jdbcType=VARCHAR}, </if>
					<if test="item.idnoDigest != null"> #{item.idnoDigest,jdbcType=VARCHAR}, </if>
					<if test="item.idnoCipher != null"> #{item.idnoCipher,jdbcType=VARCHAR}, </if>
					<if test="item.custmobileMask != null"> #{item.custmobileMask,jdbcType=VARCHAR}, </if>
					<if test="item.custmobileDigest != null"> #{item.custmobileDigest,jdbcType=VARCHAR}, </if>
					<if test="item.custmobileCipher != null"> #{item.custmobileCipher,jdbcType=VARCHAR}, </if>
					<if test="item.specialmark != null"> #{item.specialmark,jdbcType=VARCHAR}, </if>
				</trim>
				WHEN matched THEN
				update
				set
				    orgconfigid=#{item.orgconfigid,jdbcType=NUMERIC}, 
				    orgcode=#{item.orgcode,jdbcType=VARCHAR}, 
                    orgname=#{item.orgname,jdbcType=VARCHAR},
					procode=#{item.procode,jdbcType=VARCHAR}, 
					citycode=#{item.citycode,jdbcType=VARCHAR}, 
					modifydt=#{item.modifydt,jdbcType=TIMESTAMP}, 
					modifier=#{item.modifier,jdbcType=VARCHAR}, 
					stat=#{item.stat,jdbcType=VARCHAR}, 
					tasktype=#{item.tasktype,jdbcType=VARCHAR}, 
					waitid=#{item.waitid,jdbcType=VARCHAR}, 
					statistictype=#{item.statistictype,jdbcType=VARCHAR}, 
					custsmsflag=#{item.custsmsflag,jdbcType=VARCHAR}, 
					invsttype=#{item.invsttype,jdbcType=VARCHAR}, 
					idtype=#{item.idtype,jdbcType=VARCHAR}, 
					conscode=null,
					consname=null,
					custconstanthis=null,
					idnoMask=#{item.idnoMask,jdbcType=VARCHAR}, 
					idnoDigest=#{item.idnoDigest,jdbcType=VARCHAR}, 
					idnoCipher=#{item.idnoCipher,jdbcType=VARCHAR}, 
					custmobileMask=#{item.custmobileMask,jdbcType=VARCHAR}, 
					custmobileDigest=#{item.custmobileDigest,jdbcType=VARCHAR}, 
					custmobileCipher=#{item.custmobileCipher,jdbcType=VARCHAR},
					specialmark=#{item.specialmark,jdbcType=VARCHAR}
				where conscustno = #{item.conscustno}
       	  </foreach>
	</update>    
	
	
	
	<delete id="removeCsAssignedCustConfigMsg">
        delete from CS_ASSIGNED_CUST_CONFIG_MSG
        where stat in ('2','3')
    </delete>
    
</mapper>



