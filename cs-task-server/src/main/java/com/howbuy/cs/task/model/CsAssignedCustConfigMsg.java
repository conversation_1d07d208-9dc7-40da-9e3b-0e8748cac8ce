package com.howbuy.cs.task.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @version 1.0
 * @Description: 实体类CsAssignedCustConfigMsg.java
 */
@Data
public class CsAssignedCustConfigMsg implements Serializable {

	private static final long serialVersionUID = 1593949912794918987L;

	private BigDecimal id;
	
	private BigDecimal orgconfigid;

    private String conscustno;

    private String custname;
    
    private String orgcode;
    
    private String orgname;
    
    private String procode;
    
    private String citycode;

    private Date createdt;

    private Date modifydt;

    private String modifier;

    private String creator;
    
    private String stat;
    
    private String conscode;
    
    private String consname;
    
    private String tasktype;
    
    private String waitid;
    
    private int statistictype;
    
    private String custmobile;
    
    private int custsmsflag;
    
    private String invsttype;
    
    private String idno;
    
    private String idtype;
    
    private String custconstanthis;
    
    private String idnoMask;  // 证件掩码
    
	private String idnoDigest;  // 证件摘要
	
	private String idnoCipher;  // 证件密文
	
    private String custmobileMask;  // 手机掩码
    
	private String custmobileDigest; // 手机摘要
	
	private String custmobileCipher; // 手机密文 
	
	private String specialmark; //特殊标签

}
