package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.CmCustconstanthis;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CmCustconstanthisMapper {
    int deleteByPrimaryKey(String custconshisid);

    int insert(CmCustconstanthis record);

    int insertSelective(CmCustconstanthis record);

    CmCustconstanthis selectByPrimaryKey(String custconshisid);

    int updateByPrimaryKeySelective(CmCustconstanthis record);

    int updateByPrimaryKey(CmCustconstanthis record);

    /**
     * @description 查询上月所有划转的转入投顾代码列表
     * @param startDate 开始日期（上月第一天）
     * @param endDate 结束日期（上月最后一天）
     * @return 投顾代码列表
     * <AUTHOR>
     * @date 2024/1/17
     */
    List<String> selectLastMonthTransferConscodes(@Param("startDate") String startDate,
                                                   @Param("endDate") String endDate);

    /**
     * @description 根据投顾代码查询上月该投顾的划转记录
     * @param conscode 投顾代码
     * @param startDate 开始日期（上月第一天）
     * @param endDate 结束日期（上月最后一天）
     * @return 划转记录列表
     * <AUTHOR>
     * @date 2024/1/17
     */
    List<CmCustconstanthis> selectLastMonthTransferRecordsByConscode(
        @Param("conscode") String conscode,
        @Param("startDate") String startDate,
        @Param("endDate") String endDate);
}