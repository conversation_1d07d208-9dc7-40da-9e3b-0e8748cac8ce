<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.cs.task.dao.CmConsultantChangeorgRecMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.cs.task.model.CmConsultantChangeorgRec">
    <!--@mbg.generated-->
    <!--@Table CM_CONSULTANT_CHANGEORG_REC-->
    <id column="RECORD_NO" jdbcType="VARCHAR" property="recordNo" />
    <result column="CONSCODE" jdbcType="VARCHAR" property="conscode" />
    <result column="TEAM_CODE" jdbcType="VARCHAR" property="teamCode" />
    <result column="MANAGEMENT_CODE" jdbcType="VARCHAR" property="managementCode" />
    <result column="MANAGEMENT_LEVEL" jdbcType="VARCHAR" property="managementLevel" />
    <result column="START_DATE" jdbcType="VARCHAR" property="startDate" />
    <result column="END_DATE" jdbcType="VARCHAR" property="endDate" />
    <result column="CREATE_DTM" jdbcType="TIMESTAMP" property="createDtm" />
    <result column="UPDATE_DTM" jdbcType="TIMESTAMP" property="updateDtm" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    RECORD_NO, CONSCODE, TEAM_CODE, MANAGEMENT_CODE, MANAGEMENT_LEVEL, START_DATE, END_DATE, 
    CREATE_DTM, UPDATE_DTM
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_CONSULTANT_CHANGEORG_REC
    where RECORD_NO = #{recordNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CM_CONSULTANT_CHANGEORG_REC
    where RECORD_NO = #{recordNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.cs.task.model.CmConsultantChangeorgRec">
    <!--@mbg.generated-->
    insert into CM_CONSULTANT_CHANGEORG_REC (RECORD_NO, CONSCODE, TEAM_CODE, 
      MANAGEMENT_CODE, MANAGEMENT_LEVEL, START_DATE, 
      END_DATE, CREATE_DTM, UPDATE_DTM
      )
    values (#{recordNo,jdbcType=VARCHAR}, #{conscode,jdbcType=VARCHAR}, #{teamCode,jdbcType=VARCHAR}, 
      #{managementCode,jdbcType=VARCHAR}, #{managementLevel,jdbcType=VARCHAR}, #{startDate,jdbcType=VARCHAR}, 
      #{endDate,jdbcType=VARCHAR}, #{createDtm,jdbcType=TIMESTAMP}, #{updateDtm,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.cs.task.model.CmConsultantChangeorgRec">
    <!--@mbg.generated-->
    insert into CM_CONSULTANT_CHANGEORG_REC
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="recordNo != null">
        RECORD_NO,
      </if>
      <if test="conscode != null">
        CONSCODE,
      </if>
      <if test="teamCode != null">
        TEAM_CODE,
      </if>
      <if test="managementCode != null">
        MANAGEMENT_CODE,
      </if>
      <if test="managementLevel != null">
        MANAGEMENT_LEVEL,
      </if>
      <if test="startDate != null">
        START_DATE,
      </if>
      <if test="endDate != null">
        END_DATE,
      </if>
      <if test="createDtm != null">
        CREATE_DTM,
      </if>
      <if test="updateDtm != null">
        UPDATE_DTM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="recordNo != null">
        #{recordNo,jdbcType=VARCHAR},
      </if>
      <if test="conscode != null">
        #{conscode,jdbcType=VARCHAR},
      </if>
      <if test="teamCode != null">
        #{teamCode,jdbcType=VARCHAR},
      </if>
      <if test="managementCode != null">
        #{managementCode,jdbcType=VARCHAR},
      </if>
      <if test="managementLevel != null">
        #{managementLevel,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="createDtm != null">
        #{createDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDtm != null">
        #{updateDtm,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.cs.task.model.CmConsultantChangeorgRec">
    <!--@mbg.generated-->
    update CM_CONSULTANT_CHANGEORG_REC
    <set>
      <if test="conscode != null">
        CONSCODE = #{conscode,jdbcType=VARCHAR},
      </if>
      <if test="teamCode != null">
        TEAM_CODE = #{teamCode,jdbcType=VARCHAR},
      </if>
      <if test="managementCode != null">
        MANAGEMENT_CODE = #{managementCode,jdbcType=VARCHAR},
      </if>
      <if test="managementLevel != null">
        MANAGEMENT_LEVEL = #{managementLevel,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        START_DATE = #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        END_DATE = #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="createDtm != null">
        CREATE_DTM = #{createDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDtm != null">
        UPDATE_DTM = #{updateDtm,jdbcType=TIMESTAMP},
      </if>
    </set>
    where RECORD_NO = #{recordNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.cs.task.model.CmConsultantChangeorgRec">
    <!--@mbg.generated-->
    update CM_CONSULTANT_CHANGEORG_REC
    set CONSCODE = #{conscode,jdbcType=VARCHAR},
      TEAM_CODE = #{teamCode,jdbcType=VARCHAR},
      MANAGEMENT_CODE = #{managementCode,jdbcType=VARCHAR},
      MANAGEMENT_LEVEL = #{managementLevel,jdbcType=VARCHAR},
      START_DATE = #{startDate,jdbcType=VARCHAR},
      END_DATE = #{endDate,jdbcType=VARCHAR},
      CREATE_DTM = #{createDtm,jdbcType=TIMESTAMP},
      UPDATE_DTM = #{updateDtm,jdbcType=TIMESTAMP}
    where RECORD_NO = #{recordNo,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert">
    <!--@mbg.generated-->
    insert all 
    <foreach collection="list" item="item">
      into CM_CONSULTANT_CHANGEORG_REC
      (RECORD_NO, CONSCODE, TEAM_CODE, MANAGEMENT_CODE, MANAGEMENT_LEVEL, START_DATE, END_DATE, 
        CREATE_DTM, UPDATE_DTM)
      values
      (#{item.recordNo,jdbcType=VARCHAR}, #{item.conscode,jdbcType=VARCHAR}, #{item.teamCode,jdbcType=VARCHAR}, 
        #{item.managementCode,jdbcType=VARCHAR}, #{item.managementLevel,jdbcType=VARCHAR}, 
        #{item.startDate,jdbcType=VARCHAR}, #{item.endDate,jdbcType=VARCHAR}, #{item.createDtm,jdbcType=TIMESTAMP}, 
        #{item.updateDtm,jdbcType=TIMESTAMP})
    </foreach>
    select 1 from dual
  </insert>

  <select id="selectNewstByConsCodeAndLevel" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" />
    FROM (
    SELECT t.*,
    ROW_NUMBER() OVER (PARTITION BY MANAGEMENT_LEVEL ORDER BY START_DATE DESC) AS rn
    FROM CM_CONSULTANT_CHANGEORG_REC t
    where CONSCODE = #{consCode,jdbcType=VARCHAR}
    ) sub
    WHERE rn = 1
  </select>

  <select id="selectLastMonthChangedRecords" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" />
    FROM CM_CONSULTANT_CHANGEORG_REC
    WHERE START_DATE >= #{stratDate,jdbcType=VARCHAR}
    AND START_DATE &lt;= #{endDate,jdbcType=VARCHAR}
    AND MANAGEMENT_CODE IS NOT NULL
  </select>

  <select id="selectPreviousTeamRecord" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" />
    FROM CM_CONSULTANT_CHANGEORG_REC
    WHERE CONSCODE = #{consCode,jdbcType=VARCHAR}
    AND MANAGEMENT_LEVEL = #{managementLevel,jdbcType=VARCHAR}
    AND TEAM_CODE IS NOT NULL
    AND MANAGEMENT_CODE IS NOT NULL
    AND START_DATE &lt; #{startDate,jdbcType=VARCHAR}
    ORDER BY START_DATE DESC
  </select>

  <select id="selectLatestManagementInfo" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" />
    FROM (
        SELECT a.*,
               ROW_NUMBER() OVER (PARTITION BY a.MANAGEMENT_LEVEL ORDER BY a.START_DATE DESC) as rn
        FROM CM_CONSULTANT_CHANGEORG_REC a
        WHERE a.CONSCODE = #{consCode,jdbcType=VARCHAR}
        AND a.TEAM_CODE IS NOT NULL
        AND a.START_DATE &lt;= #{endDate,jdbcType=VARCHAR}
    ) t
    WHERE t.rn = 1
    ORDER BY t.MANAGEMENT_LEVEL
  </select>

  <select id="selectCurrentManagementInfo" resultMap="BaseResultMap"> 
    SELECT <include refid="Base_Column_List" />
    FROM (
        SELECT a.*,
               ROW_NUMBER() OVER (PARTITION BY a.MANAGEMENT_LEVEL ORDER BY a.START_DATE DESC) as rn
        FROM CM_CONSULTANT_CHANGEORG_REC a
        WHERE a.CONSCODE = #{consCode,jdbcType=VARCHAR}
        AND a.START_DATE &lt;= #{endDate,jdbcType=VARCHAR}
        AND (a.END_DATE IS NULL or a.END_DATE &gt;= #{endDate,jdbcType=VARCHAR})
        AND a.TEAM_CODE IS NOT NULL
    ) t
    WHERE t.rn = 1
    ORDER BY t.MANAGEMENT_LEVEL
  </select>

  <!-- 获取投顾之前的团队记录 -->
  <select id="selectPreviousTeamRecords" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" />
    FROM CM_CONSULTANT_CHANGEORG_REC
    WHERE CONSCODE = #{consCode,jdbcType=VARCHAR}
    AND MANAGEMENT_LEVEL = #{managementLevel,jdbcType=VARCHAR}
    AND START_DATE &lt; #{startDate,jdbcType=VARCHAR}
    AND TEAM_CODE IS NOT NULL
    ORDER BY START_DATE DESC
  </select>
</mapper>