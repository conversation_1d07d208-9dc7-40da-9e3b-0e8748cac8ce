package com.howbuy.cs.task.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CommonUtil {
	/**
	 * 验证邮箱
	 */
	public static boolean checkEmail(String email){  
		boolean flag = false;  
		try{  
			String check = "^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";  
			Pattern regex = Pattern.compile(check);  
			Matcher matcher = regex.matcher(email);  
			flag = matcher.matches();  
		}catch(Exception e){  
			flag = false;  
		}  

		return flag;  
	}
	
	/**
	 * 验证手机号
	 */
	public static boolean isMobile(String str) {
		if(str == null){
			return false;
		}else{
			Pattern p = null;
			Matcher m = null;
			boolean b = false; 
			p = Pattern.compile("^[1][0-9][0-9]{9}$"); // 验证手机号
			m = p.matcher(str);
			b = m.matches(); 
			return b;
		}
		
	}
	
	/**	
	 * 检查日期格式
	 */
	public static boolean checkDate(String dateStr){
		
		 if(dateStr == null){
			 return false;
		 }else{
			 String eL= "^((\\d{2}(([02468][048])|([13579][26]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))";        
	         Pattern p = Pattern.compile(eL);         
	         Matcher m = p.matcher(dateStr);         
	         return m.matches();        
		 }
	
	}
	
}
