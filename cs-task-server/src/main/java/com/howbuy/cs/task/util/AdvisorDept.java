package com.howbuy.cs.task.util;

public enum AdvisorDept {
	IC_SALES1("200000999","财富一部"),
	IC_SALES2("200000901","财富二部"),
	IC_SALESBJ("100000999","北京财富管理中心"),
	IC_SALESHZ("310000999","杭州财富管理中心"),
	IC_SALESSZ("518000999","深圳财富管理中心"),
	EC_RS("200000004","RS"),
//	EC_RT("200000902","RT"),
	IC_SALES3("618001999","财富三部");

	private String orgCode;
	private String orgName;
	
	private AdvisorDept(String orgCode,String orgName){
		this.orgCode = orgCode;
		this.orgName = orgName;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public String getOrgName() {
		return orgName;
	}
	
	/**
	 * 根据部门编号获取对应的enum
	 */
	public static AdvisorDept getAdvisorDeptByOrgCode(String orgCode){
		if(orgCode.equals(IC_SALES1.getOrgCode())){
			return IC_SALES1;
		}else if(orgCode.equals(IC_SALES2.getOrgCode())){
			return IC_SALES2;
		}else if(orgCode.equals(IC_SALESBJ.getOrgCode())){
			return IC_SALESBJ;
		}else if(orgCode.equals(IC_SALESHZ.getOrgCode())){
			return IC_SALESHZ;
		}else if(orgCode.equals(IC_SALESSZ.getOrgCode())){
			return IC_SALESSZ;
		}else if(orgCode.equals(EC_RS.getOrgCode())){
			return EC_RS;
		}
//		else if(orgCode.equals(EC_RT.getOrgCode())){
//			return EC_RT;
//		}
		else if(orgCode.equals(IC_SALES3.getOrgCode())){
			return IC_SALES3;
		}else{
			return null;
		}
	}
	
	/**
	 * 根据所在省份获取应该归属的部门信息
	 */
	public static AdvisorDept getAdvisorDeptByProviceID(String proviceID){
		if("20".equals(proviceID)){   //上海 返回 财富一部
			return IC_SALES1;
		}else if("10".equals(proviceID) || "30".equals(proviceID) || "05".equals(proviceID) || "5".equals(proviceID)|| "06".equals(proviceID)|| "6".equals(proviceID)|| "07".equals(proviceID) || "7".equals(proviceID)){  //北京、天津、河北 返回北京部门
			return IC_SALESBJ;
		}else if("31".equals(proviceID) || "32".equals(proviceID)){  //浙江 返回杭州
			return IC_SALESHZ;
		}else if("51".equals(proviceID) || "52".equals(proviceID)){   //广东 返回深圳
			return IC_SALESSZ;
		}else{
			return null;  //其他地区返回null
		}
	}
}
