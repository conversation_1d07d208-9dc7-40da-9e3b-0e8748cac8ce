<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.cs.task.dao.RecyTaskDao">


    <update id="updateDistributeFlag" >
        update cs_callout_waitdistribute a
		   set a.distribute_flag = 0
		 where a.handle_state != 2
		   and exists (select 1
		          from cs_task_assign_day b
		         where a.waitid = b.waitid
		           and b.handle_flag in (0, 1))
		   and not exists (select 1
		          from cs_task_assign_day b
		         where a.waitid = b.waitid
		           and a.task_type = 1100
		           and b.handledate is not null)
    </update>

    <insert id="insertDataTransferToHis" useGeneratedKeys="false">
         insert into cs_task_assign_his
           (select * from cs_task_assign_day where handledate is not null)
    </insert>

    <delete id="deleteCsTaskAssignDay">
        delete from cs_task_assign_day
    </delete>
    
    <delete id="mergeUpCalloutstatus">
        merge into cs_callout_waitdistribute  t1
        using  cs_task_assign_day t2
		on (t1.waitid = t2.waitid)
		when matched then
		update set t1.calloutstatus = t2.calloutstatus
    </delete>
	
	<!-- 弃用 -->
	<select id="qryAdvisorNPDistributeTaskMap" parameterType="map" resultType="map">
	      select t1.mobile as MOBILE,DECODE(t3.wireless_channel, '1', '掌基', '2', '臻财', '3', 'M站', '') as WIFICHANNEL  ,t5.sourcename as CUSTSOURCE,t2.LAST_HANDLE_STATE as LAST_HANDLE_STATE
          from  cm_conscust t1
          left join cs_callout_waitdistribute t2  on t2.conscustno = t1.conscustno
		  left join cm_bookingcust t3 on t3.id = t2.taskid   
		  left join cm_bookingtypesource t4 on t4.bookingtype = t3.bookingtype and t4.activitytype = t3.activitytype
					   and t4.wireless_channel = t3.wireless_channel and t4.qualified_status = t3.qualified_status  and t4.recstat = '0'
		  left join cm_sourceinfo_new t5 on t5.sourceno = t4.newsourceno           
		  where 1=1 
		   <if test="custName != null"> and t1.conscustno = #{conscustno, jdbcType = VARCHAR} </if>
		   <if test="waitid != null">   and t2.waitid = #{waitid, jdbcType = VARCHAR} </if>
     </select>
     

</mapper>