<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.cs.task.dao.CmDailyCmsReportStreamMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.cs.task.model.CmDailyCmsReportStream">
    <!--@mbg.generated-->
    <!--@Table CM_DAILY_CMS_REPORT_STREAM-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="HBONE_NO" jdbcType="VARCHAR" property="hboneNo" />
    <result column="SEND_DATE" jdbcType="VARCHAR" property="sendDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, HBONE_NO, SEND_DATE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.math.BigDecimal" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_DAILY_CMS_REPORT_STREAM
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal">
    <!--@mbg.generated-->
    delete from CM_DAILY_CMS_REPORT_STREAM
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.howbuy.cs.task.model.CmDailyCmsReportStream">
    <!--@mbg.generated-->
    <selectKey keyProperty="id" order="BEFORE" resultType="java.math.BigDecimal">
      select SEQ_DAILY_CMS_REPORT_STREAM.nextval from dual
    </selectKey>
    insert into CM_DAILY_CMS_REPORT_STREAM (ID, HBONE_NO, SEND_DATE
      )
    values (#{id,jdbcType=DECIMAL}, #{hboneNo,jdbcType=VARCHAR}, #{sendDate,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.cs.task.model.CmDailyCmsReportStream">
    <!--@mbg.generated-->
    <selectKey keyProperty="id" order="BEFORE" resultType="java.math.BigDecimal">
      select SEQ_DAILY_CMS_REPORT_STREAM.nextval from dual
    </selectKey>
    insert into CM_DAILY_CMS_REPORT_STREAM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      ID,
      <if test="hboneNo != null">
        HBONE_NO,
      </if>
      <if test="sendDate != null">
        SEND_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=DECIMAL},
      <if test="hboneNo != null">
        #{hboneNo,jdbcType=VARCHAR},
      </if>
      <if test="sendDate != null">
        #{sendDate,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.cs.task.model.CmDailyCmsReportStream">
    <!--@mbg.generated-->
    update CM_DAILY_CMS_REPORT_STREAM
    <set>
      <if test="hboneNo != null">
        HBONE_NO = #{hboneNo,jdbcType=VARCHAR},
      </if>
      <if test="sendDate != null">
        SEND_DATE = #{sendDate,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.cs.task.model.CmDailyCmsReportStream">
    <!--@mbg.generated-->
    update CM_DAILY_CMS_REPORT_STREAM
    set HBONE_NO = #{hboneNo,jdbcType=VARCHAR},
      SEND_DATE = #{sendDate,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="countByHboneNoAndSendDate" parameterType="com.howbuy.cs.task.model.CmDailyCmsReportStream" resultType="int">
    select count(1) from CM_DAILY_CMS_REPORT_STREAM
    where HBONE_NO = #{hboneNo,jdbcType=VARCHAR}
    and SEND_DATE = #{sendDate,jdbcType=VARCHAR}
  </select>
</mapper>