package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.CmConsultantChangeorgRec;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface CmConsultantChangeorgRecMapper {
    int deleteByPrimaryKey(String recordNo);

    int insert(CmConsultantChangeorgRec record);

    int insertSelective(CmConsultantChangeorgRec record);

    CmConsultantChangeorgRec selectByPrimaryKey(String recordNo);

    int updateByPrimaryKeySelective(CmConsultantChangeorgRec record);

    int updateByPrimaryKey(CmConsultantChangeorgRec record);

    int batchInsert(@Param("list") List<CmConsultantChangeorgRec> list);

    /**
     * @description: 根据投顾code查询每个管理层级的最新的变更记录
     * @param consCode	投顾code
     * @return java.util.List<com.howbuy.cs.task.model.CmConsultantChangeorgRec>
     * @author: hongdong.xie
     * @date: 2024/11/25 17:09
     * @since JDK 1.8
     */
    List<CmConsultantChangeorgRec> selectNewstByConsCodeAndLevel(@Param("consCode") String consCode);

    /**
     * @description: 查询指定月份变动的投顾记录
     * @param stratDate	 开始日期
     * @param endDate	 结束日期
     * @return java.util.List<com.howbuy.cs.task.model.CmConsultantChangeorgRec>
     * @author: hongdong.xie
     * @date: 2025/2/11 14:43
     * @since JDK 1.8
     */
    List<CmConsultantChangeorgRec> selectLastMonthChangedRecords(@Param("stratDate") String stratDate,@Param("endDate") String endDate);

    /**
     * @description 获取投顾之前的团队记录列表
     * @param consCode 投顾编号
     * @param managementLevel 管理层级别
     * @param startDate 开始日期
     * @return 投顾变更记录列表
     * <AUTHOR>
     * @date 2024/1/17
     */
    List<CmConsultantChangeorgRec> selectPreviousTeamRecords(@Param("consCode") String consCode,
                                                        @Param("managementLevel") String managementLevel,
                                                        @Param("startDate") String startDate);

    /**
     * @description 查询投顾最新的管理层变更信息
     * @param consCode 投顾编号
     * @param endDate 截止日期
     * @return 投顾管理层变更记录列表
     * <AUTHOR>
     * @date 2024/1/17
     */
    List<CmConsultantChangeorgRec> selectLatestManagementInfo(@Param("consCode") String consCode,
                                                         @Param("endDate") String endDate);

    /**
     * @description 查询投顾当前的管理层变更信息
     * @param consCode 投顾编号
     * @param startDate 开始日期
     * @param endDate 截止日期
     * @return 投顾管理层变更记录列表
     * <AUTHOR>
     * @date 2025/6/18
     */
    List<CmConsultantChangeorgRec> selectCurrentManagementInfo(@Param("consCode") String consCode,
                                                         @Param("startDate") String startDate,
                                                         @Param("endDate") String endDate);
}