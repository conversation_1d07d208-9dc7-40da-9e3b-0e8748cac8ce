package com.howbuy.cs.task.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.howbuy.cs.task.dao.AdvisorTargetDao;
import com.howbuy.cs.task.dao.CsInvestadvisoryQuotaConfigDao;

@Service("advisorResetTaskService")
public class AdvisorResetTaskServiceImpl implements AdvisorResetTaskService {
	private static final Logger log = LoggerFactory.getLogger(AdvisorResetTaskServiceImpl.class);
	
	@Autowired
    private AdvisorTargetDao advisorTargetDao;
	
	@Autowired
    private CsInvestadvisoryQuotaConfigDao csInvestadvisoryQuotaConfigDao;	
	
	/**
	 * 投顾任务表重置方法
	 */
	@Override
	public void advisorTaskReset(String arg) {
		log.info("投顾任务重置方法开始执行。。。");
		boolean updateFlag = advisorTargetDao.updateInitAdvisorTarget() > 0;
		if (updateFlag) {
            log.info("投顾额度表更新成功");
        } else {
            log.info("投顾额度表更新失败");
        }
		
		csInvestadvisoryQuotaConfigDao.initCsInvestadvisoryQuotaConfig();
		log.info("投顾额度配置表更新成功");
		
		
		log.info("投顾任务重置方法执行结束。。。");
	}

	
}