/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.service;

import com.howbuy.cs.outservice.cms.report.CmsReportOutService;
import com.howbuy.cs.task.buss.CustDailyReportBuss;
import com.howbuy.cs.task.buss.CustDailyReportPushBuss;
import com.howbuy.cs.task.model.ReportWithFunds;
import com.howbuy.member.dto.report.ProductReportUpdatePushDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 客户每日报告服务实现类
 * @date 2025-06-16 13:25:20
 * @since JDK 1.8
 */
@Slf4j
@Service("custDailyReportService")
public class CustDailyReportServiceImpl implements CustDailyReportService {

    @Autowired
    private CmsReportOutService cmsReportOutService;

    @Autowired
    private CustDailyReportBuss custDailyReportBuss;

    @Autowired
    private CustDailyReportPushBuss custDailyReportPushBuss;


    /**
     * @param arg 传入参数
     * @description: 每日给客户发送报告
     * @author: hongdong.xie
     * @date: 2025-06-16 13:25:20
     * @since JDK 1.8
     */
    @Override
    public void sendDailyReport(String arg) {
        log.info("客户每日报告任务开始执行...");

        // 计算时间范围：昨天晚上8点到今天晚上8点（左闭右开）
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 20);  // 晚上8点是20点
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date endTime = calendar.getTime();

        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date startTime = calendar.getTime();

        // 转换为String格式：yyyy-MM-dd HH:mm:ss
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTimeStr = sdf.format(startTime);
        String endTimeStr = sdf.format(endTime);


        // 1. 获取每日更新的报告列表
        List<ProductReportUpdatePushDTO> reportList = cmsReportOutService.getUpdatedReports(startTimeStr, endTimeStr, null);
        if (CollectionUtils.isEmpty(reportList)) {
            log.info("今日没有需要推送的报告");
            return;
        }
        log.info("获取到报告数量: {}", reportList.size());

        // 2. 获取最终要推送的客户和客户关联的报告列表
        Map<String, List<ReportWithFunds>> hboneNoAndReportMap = custDailyReportBuss.buildCustomerReportMap(reportList);
        log.info("需要推送的客户数量: {}", hboneNoAndReportMap.size());

        // 3. 遍历客户报告映射，进行幂等性检查和推送处理
        custDailyReportPushBuss.pushMessage(hboneNoAndReportMap);
        log.info("客户每日报告任务结束");
    }

} 