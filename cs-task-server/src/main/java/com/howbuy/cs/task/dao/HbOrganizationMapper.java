package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.HbOrganization;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * @description: 组织架构Mapper接口
 * @author: hongdong.xie
 * @date: 2024/1/17
 */
public interface HbOrganizationMapper {
    
    /**
     * 根据部门编码查询从当前部门到顶层的所有部门列表
     * @param orgCode 部门编码
     * @return 部门列表，按层级倒序排列(从顶层到当前部门)
     */
    List<HbOrganization> selectOrgPathByOrgCode(@Param("orgCode") String orgCode);

    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(String orgcode);

    /**
     * 插入记录
     */
    int insert(HbOrganization record);

    /**
     * 选择性插入记录
     */
    int insertSelective(HbOrganization record);

    /**
     * 根据主键查询
     */
    HbOrganization selectByPrimaryKey(String orgcode);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(HbOrganization record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(HbOrganization record);

    /**
     * 查询中心领导用户ID
     *
     * @param outletCode 营业部代码
     * @return 中心领导用户ID
     */
    String selectCenterLeaderUserId(String outletCode);
}