package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.TransBookCustModel;
import java.util.List;
import com.howbuy.cs.task.model.TransMobileCustModel;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface TransBookCustDao {

	/**
	 * 获取需要划转的预约客户 [只处理 T+14 数据]
	 */
	List<TransBookCustModel> getTransBookCust();


	/**
	 * 根据taskId --> 获取需要划转的预约客户[只处理 T+7 数据]
	 * @return
	 */
	TransBookCustModel getTransBookCustByTaskId(@Param("taskId") Integer taskId);

	/**
	 * 获取需要划转的手机号异常客户
	 */
	List<TransMobileCustModel> getTransMobileCust();

    /**
     * 预约客户任务流转处理类
     */
    Integer updateTransBookFlag(final TransBookCustModel transBookCustModel);


	/**
	 * 更新 任务状态-->已处理 .
	 * 更新  拨出状态
	 * @param taskId
	 * @param calloutStatus
	 * @return
	 */
	Integer updateDealedTaskAssignDay(@Param("taskId") Integer taskId, @Param("calloutStatus") String calloutStatus);
    
}
