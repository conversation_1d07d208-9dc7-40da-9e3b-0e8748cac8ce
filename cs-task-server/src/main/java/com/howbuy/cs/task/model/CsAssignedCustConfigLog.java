package com.howbuy.cs.task.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @version 1.0
 * @Description: 实体类CsAssignedCustConfigLog.java
 */
@Data
public class CsAssignedCustConfigLog implements Serializable {

	private static final long serialVersionUID = 3911999922309125224L;

	private BigDecimal id;
	
	private BigDecimal assignedcustid;
	
	private String custconstanthis;
	
	private String waitid;

    private String conscustno;

    private String custname;
    
    private String orgcode;
    
    private String orgname;
    
    private String procode;
    
    private String citycode;

    private Date createdt;

    private Date modifydt;

    private String modifier;

    private String creator;
    
    private String stat;
    
    private String conscode;
    
    private String consname;
    
    private String tasktype;
    
    private int statistictype;
    
    private String custmobile;
    
    private int custsmsflag;
    
    private String invsttype;
    
    private String idno;
    
    private String idtype;

}
