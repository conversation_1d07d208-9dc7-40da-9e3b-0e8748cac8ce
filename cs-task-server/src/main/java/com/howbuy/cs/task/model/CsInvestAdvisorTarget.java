package com.howbuy.cs.task.model;

import lombok.Data;

@Data
public class CsInvestAdvisorTarget {
    private long id;
    private String advisorId;
    private String advisorName;
    private int targetNum;
    private String workFlag;
    private int topFlag;
    private int levelNum;
    private int validateFlag;
    private String orgCode;
    private String orgName;
    private String createDt;
    private String modifyDt;
    private String modifierId;
    private String createrId;
    private int taskSourceType;
    private int multiRate;
    private int currCustNum;
    private int currTargetNum;
    private int currLevelNum;  //最新的排队序号
    private String teamCode;  //团队编号

    /**
     * <p>功能描述：获取该用户的总共额度</p>
     * <p>创建日期：2014年10月29日</p>
     *
     * @return int
     * <AUTHOR>
     * @update [更改日期 yyyy-MM-dd] [更改人姓名]
     */
    public int getCurrTargetTotalNum() {
        return this.currTargetNum * this.multiRate;
    }

    /**
     * <p>功能描述：获取该投顾的初始总共额度</p>
     * <p>创建日期：2014年11月6日</p>
     *
     * @return int
     * <AUTHOR>
     * @update [更改日期 yyyy-MM-dd] [更改人姓名]
     */
    public int getInitTargetTotalNum() {
        return this.targetNum * this.multiRate;
    }
}
