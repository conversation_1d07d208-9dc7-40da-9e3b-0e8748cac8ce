package com.howbuy.cs.task.dao;

import org.apache.ibatis.annotations.Param;

import com.howbuy.cs.task.model.Custconstant;

import java.util.List;

/**
 * @description: 客户投顾关系表Mapper
 * @author: hongdong.xie
 * @date: 2024/1/17
 */
public interface CustConstantMapper {
    
    /**
     * @description: 根据投顾编码和日期查询客户号列表
     * @param consCode 投顾编码
     * @param queryDate 查询日期
     * @return 客户号列表
     * @author: hongdong.xie
     * @date: 2024/1/17
     */
    List<String> selectCustNosByConsCode(@Param("consCode") String consCode, @Param("queryDate") String queryDate);

    /**
     * @description: 查询投顾下需要新增存量分成配置的有效客户列表
     * @param consCode 投顾编号
     * @param managementCode 管理层编号
     * @param managementLevel 管理层级别
     * @param lastMonthLastDay 上月最后一天
     * @return 需要新增配置的客户号列表
     */
    List<String> selectValidCustNosByConsCode(@Param("consCode") String consCode,
                                             @Param("managementCode") String managementCode,
                                             @Param("managementLevel") String managementLevel,
                                             @Param("lastMonthLastDay") String lastMonthLastDay );

    /**
     * @description 根据客户号查询最新的投顾关系信息
     * @param custNo 客户号
     * @return 客户投顾关系信息
     * <AUTHOR>
     * @date 2024/1/17
     */
    Custconstant selectByCustNo(@Param("custNo") String custNo);

    /**
     * @description 根据客户号查询客户名称
     * @param custNo 客户号
     * @return 客户名称
     * <AUTHOR>
     * @date 2024-06-29 17:30:00
     */
    String selectCustNameByCustNo(String custNo);
} 