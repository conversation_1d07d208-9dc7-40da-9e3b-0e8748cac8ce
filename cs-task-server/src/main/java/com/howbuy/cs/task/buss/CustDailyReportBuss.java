/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.buss;

import com.howbuy.cs.outservice.ordercenter.QueryCustBalanceOuterService;
import com.howbuy.cs.outservice.td.CrmHighDealOrderOuterService;
import com.howbuy.cs.task.model.ReportWithFunds;
import com.howbuy.member.dto.report.ProductReportUpdatePushDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/7/18 15:11
 * @since JDK 1.8
 */

@Slf4j
@Service("custDailyReportBuss")
public class CustDailyReportBuss {

    @Autowired
    private QueryCustBalanceOuterService queryCustBalanceOuterService;

    @Autowired
    private CrmHighDealOrderOuterService crmHighDealOrderOuterService;


    /**
     * @param reportList 报告列表
     * @return Map<String, List < ReportWithFunds>> 客户号->报告和该客户持有的基金列表
     * @description: 构建客户报告映射关系
     * @author: hongdong.xie
     * @date: 2025-06-16 13:25:20
     * @since JDK 1.8
     */
    public Map<String, List<ReportWithFunds>> buildCustomerReportMap(List<ProductReportUpdatePushDTO> reportList) {
        log.info("开始构建客户报告映射关系");
        Map<String, List<ReportWithFunds>> customerReportMap = new HashMap<>();

        // 2.1 遍历报告列表，获取每个报告要推送的客户列表
        for (ProductReportUpdatePushDTO report : reportList) {
            try {
                Map<String, List<String>> hboneNoAndFundsMap = getReportCustomers(report);

                // 2.2 将每个报告要推送的客户列表，遍历装进Map中
                for (Map.Entry<String, List<String>> entry : hboneNoAndFundsMap.entrySet()) {
                    String hboneNo = entry.getKey();
                    List<String> fundCodes = entry.getValue();

                    if (StringUtils.isBlank(hboneNo)) {
                        continue;
                    }

                    customerReportMap.computeIfAbsent(hboneNo, k -> new ArrayList<>())
                            .add(new ReportWithFunds(report, fundCodes));
                }
            } catch (Exception e) {
                log.error("处理报告时发生异常: {}", report, e);
                // 继续处理下一个报告
            }
        }

        log.info("完成客户报告映射关系构建");
        return customerReportMap;
    }


    /**
     * @param report 报告对象
     * @return Map<String, List < String>> 客户一账通号->该客户持有的基金代码列表
     * @description: 获取报告要推送的客户列表和对应的基金产品关联关系
     * @author: hongdong.xie
     * @date: 2025-06-16 13:25:20
     * @since JDK 1.8
     */
    private Map<String, List<String>> getReportCustomers(ProductReportUpdatePushDTO report) throws ParseException {
        Map<String, List<String>> customerFundMap = new HashMap<>();

        // 2.1.1 遍历每个报告关联的基金代码
        List<String> fundCodes = getReportFundCodes(report);
        Date reportDeadline = getReportDeadline(report);

        for (String fundCode : fundCodes) {
            try {
                // 根据基金代码查询持仓客户列表（最早交易上报日 需在报告日期之前）
                List<String> balanceHboneNoList = getFundCustomers(fundCode, reportDeadline);

                // 为每个客户添加对应的基金代码
                for (String hboneNo : balanceHboneNoList) {
                    if (StringUtils.isNotBlank(hboneNo)) {
                        customerFundMap.computeIfAbsent(hboneNo, k -> new ArrayList<>())
                                .add(fundCode);
                    }
                }
            } catch (Exception e) {
                log.error("查询基金{}的持仓客户失败", fundCode, e);
            }
        }

        return customerFundMap;
    }


    /**
     * @param fundCode       基金代码
     * @param reportDeadline 报告调研截止日期
     * @return List<String> 符合条件的客户一账通号列表
     * @description: 根据基金代码查询持仓客户列表
     * @author: hongdong.xie
     * @date: 2025-06-16 13:25:20
     * @since JDK 1.8
     */
    private List<String> getFundCustomers(String fundCode, Date reportDeadline) {
        log.debug("查询基金{}的持仓客户", fundCode);

        try {
            // 调接口查询持仓客户
            List<String> balanceHboneNoList = queryCustBalanceOuterService.getBalanceHboneListByFundCode(fundCode);
            if (CollectionUtils.isEmpty(balanceHboneNoList)) {
                log.debug("查询基金{}的持仓客户为空", fundCode);
                return new ArrayList<>();
            }
            log.debug("查询基金{}的持仓客户成功，结果数量: {}", fundCode, balanceHboneNoList.size());

            // 根据首次交易上报日过滤客户
            return filterCustomersByFirstTradeDate(balanceHboneNoList, fundCode, reportDeadline);
        } catch (Exception e) {
            log.error("查询基金{}持仓客户失败", fundCode, e);
        }

        return new ArrayList<>();
    }


    /**
     * @param customerList   客户列表
     * @param fundCode       基金代码
     * @param reportDeadline 报告调研截止日期
     * @return List<String> 符合条件的客户列表
     * @description: 根据首个交易上报日过滤客户
     * @author: hongdong.xie
     * @date: 2025-06-16 13:25:20
     * @since JDK 1.8
     */
    private List<String> filterCustomersByFirstTradeDate(List<String> customerList, String fundCode,
                                                         Date reportDeadline) {
        List<String> validHboneNoList = new ArrayList<>();

        for (String hboneNo : customerList) {
            try {
                // ******* 查询客户的交易记录，计算首个交易上报日
                Date firstTradeDate = crmHighDealOrderOuterService.getFirstTradeDate(hboneNo, fundCode);

                // 如果首个交易上报日在报告调研截止日期之前，则加入推送列表
                if (firstTradeDate != null && firstTradeDate.compareTo(reportDeadline) <= 0) {
                    validHboneNoList.add(hboneNo);
                    log.debug("客户{}符合条件，首个交易日期：{}，报告截止日期：{}", hboneNo, firstTradeDate, reportDeadline);
                } else {
                    log.debug("客户{}不符合条件，首个交易日期：{}，报告截止日期：{}", hboneNo, firstTradeDate, reportDeadline);
                }
            } catch (Exception e) {
                log.error("查询客户{}的交易记录失败", hboneNo, e);
            }
        }

        return validHboneNoList;
    }


    /**
     * @param report 报告对象
     * @return List<String> 基金代码列表
     * @description: 从报告对象中获取关联的基金代码列表
     * @author: hongdong.xie
     * @date: 2025-06-16 13:25:20
     * @since JDK 1.8
     */
    private List<String> getReportFundCodes(ProductReportUpdatePushDTO report) {
        // 从ProductReportUpdatePushDTO获取基金代码列表
        if (report != null && CollectionUtils.isNotEmpty(report.getFundCodeList())) {
            return report.getFundCodeList();
        }
        return new ArrayList<>();
    }

    /**
     * @param report 报告对象
     * @return Date 调研截止日期
     * @description: 从报告对象中获取调研截止日期
     * @author: hongdong.xie
     * @date: 2025-06-16 13:25:20
     * @since JDK 1.8
     */
    private Date getReportDeadline(ProductReportUpdatePushDTO report) throws ParseException {
        // 从ProductReportUpdatePushDTO获取报告日期作为截止日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.parse(report.getReportDate());
    }


}