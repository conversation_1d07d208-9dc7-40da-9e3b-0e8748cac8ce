<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.cs.task.dao.FundFileProcessDtlRecMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.cs.task.model.FundFileProcessDtlRec">
    <!--@mbg.generated-->
    <!--@Table FUND_FILE_PROCESS_DTL_REC-->
    <id column="RECORD_NO" jdbcType="VARCHAR" property="recordNo" />
    <result column="TA_CODE" jdbcType="VARCHAR" property="taCode" />
    <result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode" />
    <result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt" />
    <result column="FILE_TYPE" jdbcType="VARCHAR" property="fileType" />
    <result column="FILE_NAME" jdbcType="VARCHAR" property="fileName" />
    <result column="FILE_OPTION" jdbcType="CHAR" property="fileOption" />
    <result column="FILE_OP_STATUS" jdbcType="CHAR" property="fileOpStatus" />
    <result column="CREATE_DTM" jdbcType="TIMESTAMP" property="createDtm" />
    <result column="UPDATE_DTM" jdbcType="TIMESTAMP" property="updateDtm" />
    <result column="OPERATOR" jdbcType="VARCHAR" property="operator" />
    <result column="MEMO" jdbcType="VARCHAR" property="memo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    RECORD_NO, TA_CODE, FUND_CODE, TA_TRADE_DT, FILE_TYPE, FILE_NAME, FILE_OPTION, FILE_OP_STATUS, 
    CREATE_DTM, UPDATE_DTM, "OPERATOR", MEMO
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from FUND_FILE_PROCESS_DTL_REC
    where RECORD_NO = #{recordNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from FUND_FILE_PROCESS_DTL_REC
    where RECORD_NO = #{recordNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.cs.task.model.FundFileProcessDtlRec">
    <!--@mbg.generated-->
    insert into FUND_FILE_PROCESS_DTL_REC (RECORD_NO, TA_CODE, FUND_CODE, 
      TA_TRADE_DT, FILE_TYPE, FILE_NAME, 
      FILE_OPTION, FILE_OP_STATUS, CREATE_DTM, 
      UPDATE_DTM, "OPERATOR", MEMO
      )
    values (#{recordNo,jdbcType=VARCHAR}, #{taCode,jdbcType=VARCHAR}, #{fundCode,jdbcType=VARCHAR}, 
      #{taTradeDt,jdbcType=VARCHAR}, #{fileType,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, 
      #{fileOption,jdbcType=CHAR}, #{fileOpStatus,jdbcType=CHAR}, #{createDtm,jdbcType=TIMESTAMP}, 
      #{updateDtm,jdbcType=TIMESTAMP}, #{operator,jdbcType=VARCHAR}, #{memo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.cs.task.model.FundFileProcessDtlRec">
    <!--@mbg.generated-->
    insert into FUND_FILE_PROCESS_DTL_REC
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="recordNo != null">
        RECORD_NO,
      </if>
      <if test="taCode != null">
        TA_CODE,
      </if>
      <if test="fundCode != null">
        FUND_CODE,
      </if>
      <if test="taTradeDt != null">
        TA_TRADE_DT,
      </if>
      <if test="fileType != null">
        FILE_TYPE,
      </if>
      <if test="fileName != null">
        FILE_NAME,
      </if>
      <if test="fileOption != null">
        FILE_OPTION,
      </if>
      <if test="fileOpStatus != null">
        FILE_OP_STATUS,
      </if>
      <if test="createDtm != null">
        CREATE_DTM,
      </if>
      <if test="updateDtm != null">
        UPDATE_DTM,
      </if>
      <if test="operator != null">
        "OPERATOR",
      </if>
      <if test="memo != null">
        MEMO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="recordNo != null">
        #{recordNo,jdbcType=VARCHAR},
      </if>
      <if test="taCode != null">
        #{taCode,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="taTradeDt != null">
        #{taTradeDt,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileOption != null">
        #{fileOption,jdbcType=CHAR},
      </if>
      <if test="fileOpStatus != null">
        #{fileOpStatus,jdbcType=CHAR},
      </if>
      <if test="createDtm != null">
        #{createDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDtm != null">
        #{updateDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.cs.task.model.FundFileProcessDtlRec">
    <!--@mbg.generated-->
    update FUND_FILE_PROCESS_DTL_REC
    <set>
      <if test="taCode != null">
        TA_CODE = #{taCode,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        FUND_CODE = #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="taTradeDt != null">
        TA_TRADE_DT = #{taTradeDt,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        FILE_TYPE = #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        FILE_NAME = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileOption != null">
        FILE_OPTION = #{fileOption,jdbcType=CHAR},
      </if>
      <if test="fileOpStatus != null">
        FILE_OP_STATUS = #{fileOpStatus,jdbcType=CHAR},
      </if>
      <if test="createDtm != null">
        CREATE_DTM = #{createDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDtm != null">
        UPDATE_DTM = #{updateDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        "OPERATOR" = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        MEMO = #{memo,jdbcType=VARCHAR},
      </if>
    </set>
    where RECORD_NO = #{recordNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.cs.task.model.FundFileProcessDtlRec">
    <!--@mbg.generated-->
    update FUND_FILE_PROCESS_DTL_REC
    set TA_CODE = #{taCode,jdbcType=VARCHAR},
      FUND_CODE = #{fundCode,jdbcType=VARCHAR},
      TA_TRADE_DT = #{taTradeDt,jdbcType=VARCHAR},
      FILE_TYPE = #{fileType,jdbcType=VARCHAR},
      FILE_NAME = #{fileName,jdbcType=VARCHAR},
      FILE_OPTION = #{fileOption,jdbcType=CHAR},
      FILE_OP_STATUS = #{fileOpStatus,jdbcType=CHAR},
      CREATE_DTM = #{createDtm,jdbcType=TIMESTAMP},
      UPDATE_DTM = #{updateDtm,jdbcType=TIMESTAMP},
      "OPERATOR" = #{operator,jdbcType=VARCHAR},
      MEMO = #{memo,jdbcType=VARCHAR}
    where RECORD_NO = #{recordNo,jdbcType=VARCHAR}
  </update>

  <select id="selectUnProcess" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from FUND_FILE_PROCESS_DTL_REC
    where TA_TRADE_DT = #{taTradeDt,jdbcType=VARCHAR}
    and FILE_TYPE = #{fileType,jdbcType=VARCHAR}
    and FILE_OP_STATUS in('0','6')

  </select>

  <update id="updateOpStatusByRecordNo">
    update FUND_FILE_PROCESS_DTL_REC
    set FILE_OP_STATUS = #{newOpStatus,jdbcType=CHAR},UPDATE_DTM = #{updateTime,jdbcType=TIMESTAMP}
    where RECORD_NO = #{recordNo,jdbcType=VARCHAR}
    <if test="oldOpStatus != null">
      and FILE_OP_STATUS = #{oldOpStatus,jdbcType=CHAR}
    </if>
  </update>
</mapper>