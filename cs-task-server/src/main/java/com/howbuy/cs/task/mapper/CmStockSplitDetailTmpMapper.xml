<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.cs.task.dao.CmStockSplitDetailTmpMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.cs.task.model.CmStockSplitDetailTmp">
    <!--@mbg.generated-->
    <!--@Table CM_STOCK_SPLIT_DETAIL_TMP-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CONFIG_ID" jdbcType="VARCHAR" property="configId" />
    <result column="CUST_NO" jdbcType="VARCHAR" property="custNo" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIMESTAMP" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIMESTAMP" jdbcType="TIMESTAMP" property="modifyTimestamp" />
    <result column="REC_STAT" jdbcType="VARCHAR" property="recStat" />
    <result column="UPPER_LIMIT" jdbcType="DECIMAL" property="upperLimit" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CONFIG_ID, CUST_NO, CREATOR, CREATE_TIMESTAMP, MODIFIER, MODIFY_TIMESTAMP, REC_STAT, 
    UPPER_LIMIT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_STOCK_SPLIT_DETAIL_TMP
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CM_STOCK_SPLIT_DETAIL_TMP
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.cs.task.model.CmStockSplitDetailTmp">
    <!--@mbg.generated-->
    insert into CM_STOCK_SPLIT_DETAIL_TMP (ID, CONFIG_ID, CUST_NO, 
      CREATOR, CREATE_TIMESTAMP, MODIFIER, 
      MODIFY_TIMESTAMP, REC_STAT, UPPER_LIMIT
      )
    values (#{id,jdbcType=VARCHAR}, #{configId,jdbcType=VARCHAR}, #{custNo,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{createTimestamp,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, 
      #{modifyTimestamp,jdbcType=TIMESTAMP}, #{recStat,jdbcType=VARCHAR}, #{upperLimit,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.cs.task.model.CmStockSplitDetailTmp">
    <!--@mbg.generated-->
    insert into CM_STOCK_SPLIT_DETAIL_TMP
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="configId != null">
        CONFIG_ID,
      </if>
      <if test="custNo != null">
        CUST_NO,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="createTimestamp != null">
        CREATE_TIMESTAMP,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="modifyTimestamp != null">
        MODIFY_TIMESTAMP,
      </if>
      <if test="recStat != null">
        REC_STAT,
      </if>
      <if test="upperLimit != null">
        UPPER_LIMIT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="configId != null">
        #{configId,jdbcType=VARCHAR},
      </if>
      <if test="custNo != null">
        #{custNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTimestamp != null">
        #{modifyTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="upperLimit != null">
        #{upperLimit,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.cs.task.model.CmStockSplitDetailTmp">
    <!--@mbg.generated-->
    update CM_STOCK_SPLIT_DETAIL_TMP
    <set>
      <if test="configId != null">
        CONFIG_ID = #{configId,jdbcType=VARCHAR},
      </if>
      <if test="custNo != null">
        CUST_NO = #{custNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTimestamp != null">
        MODIFY_TIMESTAMP = #{modifyTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="recStat != null">
        REC_STAT = #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="upperLimit != null">
        UPPER_LIMIT = #{upperLimit,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.cs.task.model.CmStockSplitDetailTmp">
    <!--@mbg.generated-->
    update CM_STOCK_SPLIT_DETAIL_TMP
    set CONFIG_ID = #{configId,jdbcType=VARCHAR},
      CUST_NO = #{custNo,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODIFY_TIMESTAMP = #{modifyTimestamp,jdbcType=TIMESTAMP},
      REC_STAT = #{recStat,jdbcType=VARCHAR},
      UPPER_LIMIT = #{upperLimit,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  
  <!-- 初始化临时表 -->
  <update id="initTempTable">
    BEGIN
      -- 删除临时表数据
      EXECUTE IMMEDIATE 'TRUNCATE TABLE CM_STOCK_SPLIT_DETAIL_TMP';
      
      -- 从正式表复制数据到临时表
      INSERT INTO CM_STOCK_SPLIT_DETAIL_TMP(
        ID, CONFIG_ID, CUST_NO, CREATOR, CREATE_TIMESTAMP, MODIFIER, MODIFY_TIMESTAMP, REC_STAT,
        UPPER_LIMIT)
      SELECT ID, CONFIG_ID, CUST_NO, CREATOR, CREATE_TIMESTAMP, MODIFIER, MODIFY_TIMESTAMP, REC_STAT,
             UPPER_LIMIT
      FROM CM_STOCK_SPLIT_DETAIL;
    END;
  </update>

  <insert id="batchInsertToTemp" parameterType="java.util.List" useGeneratedKeys="false">
    insert all
    <foreach collection="list" item="item">
      into CM_STOCK_SPLIT_DETAIL_TMP
      (ID,CONFIG_ID,CUST_NO,CREATOR,CREATE_TIMESTAMP,REC_STAT)
      values
      (#{item.id,jdbcType=VARCHAR},#{item.configId,jdbcType=VARCHAR} ,
      #{item.custNo,jdbcType=VARCHAR},
      #{item.creator,jdbcType=VARCHAR},
      #{item.createTimestamp,jdbcType=TIMESTAMP},
      #{item.recStat,jdbcType=VARCHAR})
    </foreach>
    select 1 from dual
  </insert>

  <select id="selectByConfigIdFromTemp" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from CM_STOCK_SPLIT_DETAIL_TMP
    where CONFIG_ID = #{configId,jdbcType=VARCHAR}
  </select>
  
  <!-- 查询临时表中的重复客户数据 -->
  <select id="findDuplicateCustomersInTemp" resultType="java.util.Map">
    SELECT 
      d.CUST_NO, 
      c.CONFIG_LEVEL, 
      c.NEWLY_CONS_CODE
    FROM 
      CM_STOCK_SPLIT_DETAIL_TMP d
    JOIN 
      CM_STOCK_SPLIT_CONFIG_TMP c ON d.CONFIG_ID = c.ID
    WHERE 
      d.REC_STAT = '1'
      AND c.REC_STAT = '1'
      AND c.CONFIG_TYPE = '10'
      AND c.CAL_START_DT BETWEEN #{startDate,jdbcType=VARCHAR} AND #{endDate,jdbcType=VARCHAR}
    GROUP BY 
      d.CUST_NO, c.CONFIG_LEVEL, c.NEWLY_CONS_CODE
    HAVING 
      COUNT(*) > 1
  </select>
  
  <!-- 根据配置ID更新临时表中的记录状态 -->
  <update id="updateRecStatByConfigIdInTemp">
    UPDATE CM_STOCK_SPLIT_DETAIL_TMP
    SET 
      REC_STAT = #{recStat,jdbcType=VARCHAR},
      MODIFIER = 'system',
      MODIFY_TIMESTAMP = SYSDATE
    WHERE 
      CONFIG_ID = #{configId,jdbcType=VARCHAR}
  </update>
  
  <!-- 根据配置ID和客户号更新临时表中的记录状态 -->
  <update id="updateRecStatByConfigIdAndCustNoInTemp">
    UPDATE CM_STOCK_SPLIT_DETAIL_TMP
    SET 
      REC_STAT = #{recStat,jdbcType=VARCHAR},
      MODIFIER = 'system',
      MODIFY_TIMESTAMP = SYSDATE
    WHERE 
      CONFIG_ID = #{configId,jdbcType=VARCHAR}
      AND CUST_NO = #{custNo,jdbcType=VARCHAR}
  </update>
</mapper>