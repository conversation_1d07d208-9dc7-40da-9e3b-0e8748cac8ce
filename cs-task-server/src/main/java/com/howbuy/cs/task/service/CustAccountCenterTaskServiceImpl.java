package com.howbuy.cs.task.service;

import java.util.List;

import com.alibaba.fastjson.JSON;
import com.howbuy.cs.task.dao.TenpayCustDao;
import crm.howbuy.base.constants.StaticVar;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.howbuy.crm.conscust.dto.CmSourceInfoDomain;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.QueryCmSourceInfoRequest;
import com.howbuy.crm.conscust.request.UpdateConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryCmSourceInfoResponse;
import com.howbuy.crm.conscust.response.UpdateConscustInfoResponse;
import com.howbuy.crm.conscust.service.QueryCmSourceInfoService;
import com.howbuy.crm.conscust.service.UpdateConscustInfoService;
import com.howbuy.cs.task.dao.HboneOpenSourceDao;
import com.howbuy.cs.task.model.HboneOpenSource;

@Service("custAccountCenterTaskService")
public class CustAccountCenterTaskServiceImpl implements CustAccountCenterTaskService {
	private static final Logger log = LoggerFactory.getLogger(CustAccountCenterTaskServiceImpl.class);

	@Autowired
	private HboneOpenSourceDao hboneOpenSourceDao;

	@Autowired
	private QueryCmSourceInfoService queryCmSourceInfoService;

	@Autowired
	private UpdateConscustInfoService updateConscustInfoService;

	@Autowired
	private TenpayCustDao tenpayCustDao;

	@Override
	public void mqAbnormalCustHandle(String arg) {
		log.info("账户中心数据匹配方法开始执行。。。");
		List<HboneOpenSource> listHboneCust = hboneOpenSourceDao.getHboneOpenSource();
		if (listHboneCust.size() == 0) {
			log.info("没有获取到账户中心客户数据！");
		} else {
			log.info("账户中心数据匹配-获取到待处理的一账通数据：{}", JSON.toJSONString(listHboneCust));
			for (HboneOpenSource hboneOpenSource : listHboneCust) {
				String regOutletCode = hboneOpenSource.getRegOutletCode();
				String invstType = hboneOpenSource.getInvstType();
				String provCode = hboneOpenSource.getProvCode();
				String cityCode = hboneOpenSource.getCityCode();
				String conscustNo = hboneOpenSource.getConscustNo();
				if (StringUtils.isNotBlank(regOutletCode) && StringUtils.isNotBlank(invstType)) {
					QueryCmSourceInfoRequest request = new QueryCmSourceInfoRequest();
					request.setSourceNo(regOutletCode);
					try {
						QueryCmSourceInfoResponse response = queryCmSourceInfoService.queryCmSourceInfo(request);
						if (response != null && "0000".equals(response.getReturnCode())) {
							CmSourceInfoDomain cmSourceInfoDomain = response.getCmSourceInfo();
							if (cmSourceInfoDomain != null) {
								String firstLevelCode = cmSourceInfoDomain.getFirstLevelCode();
								String consCode = "";

								// 获取客户所属投顾
								if ("0".equals(invstType)  || "2".equals(invstType)) {
									consCode = "JGKH_ORG";
								} else if ("C".equals(firstLevelCode)) {
									consCode = "CXGN_CSLS";
								} else if ("K".equals(firstLevelCode)) {
									consCode = "ZSJJN_CSLS";
								} else if ("L".equals(firstLevelCode) || "H".equals(firstLevelCode)
										|| "F".equals(firstLevelCode)) {
									consCode = "HZN_CSLS";
								} else {
									consCode = "WZQTN_CSLS";
								}

								ConscustInfoDomain conscustinfomodel = new ConscustInfoDomain();
								conscustinfomodel.setConscustno(conscustNo);

								// 设置客户来源
								conscustinfomodel.setNewsourceno(cmSourceInfoDomain.getSourceNo());

								// 设置省市编码
								if (StringUtils.isNotBlank(provCode)) {
									conscustinfomodel.setProvcode(provCode);
								}
								if (StringUtils.isNotBlank(cityCode)) {
									conscustinfomodel.setCitycode(cityCode);
								}

								// 修改客户信息
								UpdateConscustInfoRequest updateConscustInfoRequest = new UpdateConscustInfoRequest();
								updateConscustInfoRequest.setConscustinfo(conscustinfomodel);
								UpdateConscustInfoResponse updateConscustInfoResponse = updateConscustInfoService.updateConsCustInfo(updateConscustInfoRequest);
								if (updateConscustInfoResponse != null && "0000".equals(updateConscustInfoResponse.getReturnCode())) {
									hboneOpenSource.setConsCode(consCode);
									hboneOpenSource.setConscustNo(conscustNo);

									// 获取原投顾
									String oldConscode = hboneOpenSourceDao.getOldConscode(conscustNo);
									// 修改投顾信息
									log.info("账户中心数据匹配-执行划转客户操作 conscustNo {}，oldConscode {}，conscode {}",
											conscustNo, oldConscode, consCode);

									String userid = "acct-match-sys-task";
									String custconshisid = tenpayCustDao.getSeqValue("SEQ_CUSTREC").toString();
									// 修改客户投顾关联历史表
									hboneOpenSourceDao.insertCustconstantHis(custconshisid, userid, consCode, StaticVar.TRANSF_REASON_OTHER, conscustNo);
									// 修改客户投顾关联
									hboneOpenSourceDao.updateCustconstant(consCode, userid, custconshisid, conscustNo);
								} else {
									log.info("updateConscustInfoResponse方法修改客户异常！");
								}

							}
						}
					} catch (Exception e) {
						log.info("来源和投顾编码修改异常：" + e.getMessage());
					}
				}
			}
		}

		log.info("账户中心数据匹配方法执行结束。。。");
	}

}