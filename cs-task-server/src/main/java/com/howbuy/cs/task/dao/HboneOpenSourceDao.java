package com.howbuy.cs.task.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;
import com.howbuy.cs.task.model.HboneOpenSource;

/**
 * 一账通分销客户处理类
 */
@MapperScan
public interface HboneOpenSourceDao {

	/**
	 * 获取分销客户数据
	 */
	List<HboneOpenSource> getHboneOpenSource();

	/**
	 * 修改客户的所属投顾
	 * @param consCode 新的所属投顾
	 * @param modifier 修改人
	 * @param beforehisid 上一次修改历史的id
	 * @param conscustNo 投顾客户号
	 * @return
	 */
	int updateCustconstant(@Param("consCode") String consCode,
						   @Param("modifier") String modifier,
						   @Param("beforehisid") String beforehisid,
						   @Param("conscustNo") String conscustNo);

	/**
	 * 插入高端历史分配记录
	 * @param custconshisid 分配记录id
	 * @param creator 创建人
	 * @param nextcons 要分配的新投顾
	 * @param reason 分配原因
	 * @param custno 投顾客户号
	 * @return
	 */
	int insertCustconstantHis(@Param("custconshisid") String custconshisid,
							  @Param("creator") String creator,
							  @Param("nextcons") String nextcons,
							  @Param("reason") String reason,
							  @Param("custno") String custno);

	/**
	 * 获取客户的原始投顾
	 * @param custno
	 * @return
	 */
	String getOldConscode(String custno);

	/**
	 * 更新任务状态
	 */
	Integer updateHboneOpenSource(HboneOpenSource hboneOpenSource);

}
