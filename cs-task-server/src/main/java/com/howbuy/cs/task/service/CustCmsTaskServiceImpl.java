package com.howbuy.cs.task.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.howbuy.cc.message.SendMsgResult;
import com.howbuy.cc.message.SendMsgService;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.UpdateConscustInfoRequest;
import com.howbuy.crm.conscust.response.UpdateConscustInfoResponse;
import com.howbuy.crm.conscust.service.UpdateConscustInfoService;
import com.howbuy.cs.bookcust.buss.BookingCustInfoBuss;
import com.howbuy.cs.bookcust.model.CustCourceAbnormalInfo;

@Service("custCmsTaskService")
public class CustCmsTaskServiceImpl implements CustCmsTaskService {
	private static final Logger log = LoggerFactory.getLogger(CustCmsTaskServiceImpl.class);

	@Autowired
	private UpdateConscustInfoService updateConscustInfoService;
	
	@Autowired
	private BookingCustInfoBuss bookingCustInfoBuss;
	
	@Autowired
	private SendMsgService sendMessageDubboService;
	
	// 邮件接收人
	@Value("${emailReceiver}")
	private String emailReceiver;
	
	@Override
	public void sourceAbnormalCustHandle(String arg) {
		log.info("cms来源异常客户方法开始执行。。。");
		
		// 查找需要修复来源的客户
		Map<String, String> param = new HashMap<String, String>();
		param.put("optFlag", "repairSource");
		List<CustCourceAbnormalInfo> listRepairSourceCust = bookingCustInfoBuss.queryListCustCourceAbnormal(param);
		if (listRepairSourceCust.size() == 0) {
			log.info("来源修复：未找到cms来源异常客户或相关配置信息！");
		} else {
			repairSource(listRepairSourceCust);
		}
		
		// 对来源异常客户进行邮件通知
		param.clear();
		param.put("optFlag", "sendEmail");
		List<CustCourceAbnormalInfo> listSendEmailCust = bookingCustInfoBuss.queryListCustCourceAbnormal(param);
		if (listSendEmailCust.size() == 0) {
			log.info("发送邮件：未找到cms来源异常客户！");
		} else {
			sendEmail(listSendEmailCust);
		}
		
		log.info("cms来源异常客户方法执行结束。。。");
	}
	
	/**
	 * 对客户来源进行修复
	 */
	public void repairSource(List<CustCourceAbnormalInfo> listRepairSourceCust) {
		int totalCount = listRepairSourceCust.size();
		if (totalCount > 0) {
			for (CustCourceAbnormalInfo custCourceAbnormalInfo : listRepairSourceCust) {
				UpdateConscustInfoRequest updateConscustInfoRequest = new UpdateConscustInfoRequest();
				ConscustInfoDomain conscustinfo = new ConscustInfoDomain();
				conscustinfo.setConscustno(custCourceAbnormalInfo.getConsCustNo());
				conscustinfo.setNewsourceno(custCourceAbnormalInfo.getNewSourceNo());
				updateConscustInfoRequest.setConscustinfo(conscustinfo);
				
				try {
					UpdateConscustInfoResponse updateConscustInfoResponse = updateConscustInfoService.updateConsCustInfo(updateConscustInfoRequest);
					if(updateConscustInfoResponse != null && "0000".equals(updateConscustInfoResponse.getReturnCode())){
						log.info("客户号为："+custCourceAbnormalInfo.getConsCustNo()+"，来源修复成功！");
					}else {
						log.info("来源修复失败，返回resp：{}", JSONObject.toJSONString(updateConscustInfoResponse));
					}
				} catch (Exception e) {
					log.error("来源修复出现异常！");
				}
				
			}
			
		}

	}
	
	/**
	 * 来源异常客户短信通知
	 */
	public void sendEmail(List<CustCourceAbnormalInfo> listSendEmailCust) {
		int totalCount = listSendEmailCust.size();
		if (totalCount > 0) {
			try {
				if(StringUtils.isNotBlank(emailReceiver)) {
					Map<String, Object> emailMap = new HashMap<String, Object>();
					emailMap.put("totalCount", String.valueOf(totalCount));
					emailMap.put("custInfoList", listSendEmailCust);
					
					String[] emailList = emailReceiver.split(",");
					SendMsgResult sendMsgResult = null;
					for(int i = 0; i < emailList.length; i++) {
						sendMsgResult =	sendMessageDubboService.sendTemplateMsgByEmail("60079", JSON.toJSONString(emailMap), emailList[i], null, null, null);
						if(sendMsgResult.getCode()!=0){
							log.info("邮件发送失败，返回码为："+sendMsgResult.getCode());
						}
					}
					if(sendMsgResult != null && sendMsgResult.getCode() == 0){
						log.info("邮件发送成功！");
					}
				}
			} catch (Exception e) {
				log.error("邮件发送请求异常！");
			}
		}

	}
	
}