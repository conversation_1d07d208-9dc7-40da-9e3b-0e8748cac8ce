package com.howbuy.cs.task.util;

import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 日期处理工具类
 */

public class DateTimeUtil {
	public static final String YYYYMMDD = "yyyyMMdd";

	/**
	 * 获取系统当前日期和时间：格式为：2010-01-01 01:01:01
	 * @return String
	 */
	public static String getCurrentDayTime() {
		Date day = new Date();
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return df.format(day);
	}
	
	/**
	 * 获取系统当前日期：格式为yyyyMMddHHmmss
	 * @return String
	 */
	public static String getCurDateTime() {
		Date curDate = new Date();
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
		return format.format(curDate);
	}
	
	public static String getCurrYMD() {
		Date day = new Date();
		SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
		return df.format(day);
	}
	
	/**
	 * 获取系统当前时间：格式为HH:mm:ss:SSS
	 * @return String
	 */
	public static String getCurTimeSS() {
		Date curDate = new Date();
		SimpleDateFormat format = new SimpleDateFormat("HH:mm:ss:SSS");
		return format.format(curDate);
	}
	
	public static String PatternDate(Date date,String pattern){
		SimpleDateFormat format = new SimpleDateFormat(pattern);
		return format.format(date);
	}
	
	/**
	 * 获取系统当前日期：格式为yyyyMMdd
	 * @return String
	 */
	public static String getCurDate() {
		Date curDate = new Date();
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
		return format.format(curDate);
	}
	
	/**
	 * 获取系统当前时间：格式为HHmmss
	 * @return String
	 */
	public static String getCurTime() {
		Date curTime = new Date();
		SimpleDateFormat format = new SimpleDateFormat("HHmmss");
		return format.format(curTime);
	}
	
	/**
	 * 获取两个日期之间相隔天数
	 * @param beginDate
	 * @param endDate
	 * @return int
	 * @throws ParseException
	 */
	public static int getDaysBetween(String beginDate, String endDate)
			throws ParseException {
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
		Date bDate = format.parse(beginDate);
		Date eDate = format.parse(endDate);
		Calendar d1 = new GregorianCalendar();
		d1.setTime(bDate);
		Calendar d2 = new GregorianCalendar();
		d2.setTime(eDate);
		int days = d2.get(Calendar.DAY_OF_YEAR) - d1.get(Calendar.DAY_OF_YEAR);
		int y2 = d2.get(Calendar.YEAR);
		if (d1.get(Calendar.YEAR) != y2) {
			d1 = (Calendar) d1.clone();
			do {
				days += d1.getActualMaximum(Calendar.DAY_OF_YEAR);// 得到当年的实际天数
				d1.add(Calendar.YEAR, 1);
			} while (d1.get(Calendar.YEAR) != y2);
		}
		return days;
	}
	
	// 获得当前时间前面九十天的时间
	public static String getNinetyBeforeDate() {
		Date date = new Date();
		Date dBefore = null;
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DAY_OF_MONTH, -90);
		dBefore = calendar.getTime();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd"); // 设置时间格式
		String defaultStartDate = sdf.format(dBefore); // 格式化前一天
		return defaultStartDate;
	}
			
	/**
	 * 指定日期格式化
	 * @return String
	 */
	public static String getDateFormat(Date date, String formatStr) {
		try {
			if (formatStr == null || formatStr.equals("")) {
				formatStr = "yyyy-MM-dd HH:mm:ss";
			}
			if(date == null){
				date =new Date();
			}
			
			SimpleDateFormat sd = new SimpleDateFormat(formatStr);
			return sd.format(date);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "";
	}
	
	/**
	 * 从当前时间增加多少天
	 * @param days
	 */
	public static Date addDaysFromNow(int days) {
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DAY_OF_MONTH, days);
		return cal.getTime();
	}

	//把秒转化成hh:mm:ss
    public static String format4Time(long times) {
		StringBuffer sb = new StringBuffer();
		
		long h = times/3600;
		times -= h*3600;
		long m = times/60;
		times -= m*60;
		sb.append(h).append(":").append(m<10?"0"+m:m).append(":").append(times<10?"0"+times:times);
		return sb.toString();
	}
    
    /**
	 * 将字符串格式转换为日期
	 * @param strDate
	 * @return Date
	 */
	public static Date strToDate(String strDate) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
		ParsePosition pos = new ParsePosition(0);
		Date strtodate = formatter.parse(strDate, pos);
		return strtodate;
	}
	
	@SuppressWarnings("finally")
	public static Date strToDate(String strDate,String partten){
		SimpleDateFormat formatter = new SimpleDateFormat(partten);
		Date date =null;
		try {
			date = formatter.parse(strDate);
		} catch (ParseException e) {
			e.printStackTrace();
		}finally{
			return date;
		}
		
	}
	
	/**
	 * 格式化日期
	 * @param date 被格式化的日期
	 * @param style 显示的样式，如yyyyMMdd
	 */
	public static String fmtDate(Date date, String style) {
		SimpleDateFormat dateFormat = new SimpleDateFormat(style);
		return dateFormat.format(date);
	}
	
	/**
     * 获取系统今天和昨天日期
     * @return Map<String, String>
	 * @throws 
     */
    @SuppressWarnings("static-access")
	public static Map<String, String> getDayStartAndEnd() {    	
    	SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
    	
    	// 获取系统当天时间
    	Date curDate = new Date();
    	String endTime = df.format(curDate);
    	StringBuffer endStr = new StringBuffer().append(endTime);
    	endTime = endStr.toString();
    	
    	// 获取系统昨天时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(curDate);        
        calendar.add(calendar.DATE, -1);	// 把日期往前增加一天：整数往后推,负数往前推
        Date yestoday= calendar.getTime(); 	// 这个日期就是被调整过的日期
        String startTime = df.format(yestoday);
        StringBuffer startStr = new StringBuffer().append(startTime);
        startTime = startStr.toString();
        
        Map<String, String> map = new LinkedHashMap<String, String>();
        map.put("beginDate", startTime);
        map.put("endDate", endTime);
        return map;
    }
    
}