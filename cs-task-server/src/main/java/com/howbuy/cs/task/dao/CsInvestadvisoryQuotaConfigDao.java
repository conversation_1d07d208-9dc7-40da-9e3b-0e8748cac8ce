package com.howbuy.cs.task.dao;

import java.util.List;
import java.util.Map;

import org.mybatis.spring.annotation.MapperScan;
import com.howbuy.cs.task.model.CsInvestadvisoryQuotaConfig;


@MapperScan
public interface CsInvestadvisoryQuotaConfigDao {

	/**
	 * 
	 * <p>功能描述：根据部门编号获取所有投顾信息</p>
	 */
    List<CsInvestadvisoryQuotaConfig> listCsInvestadvisoryQuotaConfig(Map<String, Object> param);
	
    
    /**
	 * 批量更新投顾额度表中的信息
	 */
	public Integer updateBatchInvestadvisoryQuotaConfig(final List<CsInvestadvisoryQuotaConfig> csInvestadvisoryQuotaConfigList);
	
	
	/**
	 * 获取当天工作的投顾配额信息
	 */
	public List<CsInvestadvisoryQuotaConfig> getTodayCsInvestadvisoryQuotaConfig();
	
	
	/**
	 * 初始化投顾额度表
	 */
	public Integer initCsInvestadvisoryQuotaConfig();

}
