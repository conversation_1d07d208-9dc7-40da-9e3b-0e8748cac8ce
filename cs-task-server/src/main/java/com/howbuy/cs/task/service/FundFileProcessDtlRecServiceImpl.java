/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.service;

import com.howbuy.cs.task.dao.FundFileProcessDtlRecMapper;
import com.howbuy.cs.task.model.FundFileProcessDtlRec;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @description:  产品文件处理明细记录表服务实现类
 * <AUTHOR>
 * @date 2024/11/21 14:42
 * @since JDK 1.8
 */
@Service("fundFileProcessDtlRecServiceImpl")
public class FundFileProcessDtlRecServiceImpl {
    @Autowired
    private FundFileProcessDtlRecMapper fundFileProcessDtlRecMapper;

    /**
     * @description: 根据文件类型查询未处理的记录
     * @param tradeDt 交易日期
     * @param fileType 文件类型
     * @return java.util.List<com.howbuy.cs.task.model.FundFileProcessDtlRec>
     * @author: hongdong.xie
     * @date: 2024/11/21 14:51
     * @since JDK 1.8
     */
    public List<FundFileProcessDtlRec> getUnProcess(String tradeDt, String fileType) {
        return fundFileProcessDtlRecMapper.selectUnProcess(tradeDt,fileType);
    }

    /**
     * @description: 更新文件处理状态
     * @param recordNo 文件记录号
     * @param oldOpStatus 旧状态
     * @param newOpStatus 新状态
     * @param updateTime 更新时间
     * @return int
     * @author: hongdong.xie
     * @date: 2024/11/21 13:57
     * @since JDK 1.8
     */
    public int updateOpStatusByRecordNo(String recordNo, String oldOpStatus, String newOpStatus, Date updateTime){
        return fundFileProcessDtlRecMapper.updateOpStatusByRecordNo(recordNo,oldOpStatus,newOpStatus,updateTime);
    }

}