package com.howbuy.cs.task.service;

import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.howbuy.cs.task.dao.AssignRuleDao;
import com.howbuy.cs.task.model.CsCalloutWaitDistribute;
import com.howbuy.cs.task.model.CsTaskAssignModel;
import com.howbuy.cs.task.util.StaticVar;

@Service("agentResetTaskService")
public class AgentResetTaskServiceImpl implements AgentResetTaskService {
	private static final Logger log = LoggerFactory.getLogger(AgentResetTaskServiceImpl.class);
	
	@Autowired
	private AssignRuleDao assignRuleDao;
	
	public Map<String, List<CsCalloutWaitDistribute>> taskMap = null;
	
	/**
	 * 坐席任务规则重置方法
	 */
	@Override
	public void agentTaskReset(String arg) {
		log.info("坐席任务规则重置方法开始执行。。。");
        CsTaskAssignModel csTaskAssignModel = new CsTaskAssignModel();
        csTaskAssignModel.setModifier("admin");
        csTaskAssignModel.setModeDetail(StaticVar.DEFAULT_ASSIGN_RULE);
        csTaskAssignModel.setModetype("0");
        boolean addAssignFlag = assignRuleDao.updateAssginRule(csTaskAssignModel)>0;;
        
        CsTaskAssignModel csTaskVisitModel = new CsTaskAssignModel();
        csTaskVisitModel.setModifier("admin");
        csTaskVisitModel.setModeDetail(StaticVar.DEFAULT_VISIT_RULE);
        csTaskVisitModel.setModetype("1");
        boolean addVisitFlag = assignRuleDao.updateAssginRule(csTaskAssignModel)>0;;

        if (addAssignFlag) {
            log.info("任务分配规则表分配顺序已经更新好");
        } else {
            log.info("任务分配规则表分配顺序更新失败");
        }
        
        if (addVisitFlag) {
            log.info("任务分配规则表访问配置已更新好");
        } else {
            log.info("任务分配规则表访问配置更新失败");
        }
		log.info("坐席任务规则重置方法执行结束。。。");
	}
	
}