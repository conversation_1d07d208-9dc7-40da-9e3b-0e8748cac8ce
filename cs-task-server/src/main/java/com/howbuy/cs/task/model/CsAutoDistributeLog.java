package com.howbuy.cs.task.model;

import java.io.Serializable;

import lombok.Data;

@Data
public class CsAutoDistributeLog implements Serializable {
	private static final long serialVersionUID = 1L;
	private long id; // 序号ID
	private String advisorId; // 投顾编号
	private String advisorName; // 投顾名称
	private String custNo; // 客户编号
	private String custName; // 客户名称
	private String orgCode; // 所属部门编号
	private int custType; // "0" 表示非howbuy，“1”表示howbuy
	private int pubCustFlag; // “0” 表示非公募，“1”表示公募
	private String procId; // 客户所在省份
	private int autoType; // “1”表示非公募投顾分配 “2”表示公募客户投顾分配
	private String distributeDate; // 分配时间
	private String teamCode; // 团队编号
	private String custMobile; // 客户手机号
	private int custSmsFlag; // 客户是否发送短信标识符
	private long waitId;
	private int statisticType;
	private String custMobileMask;      // 手机掩码
	private String custMobileDigest;    // 手机摘要
	private String custMobileCipher;    // 手机密文 

}
