package com.howbuy.cs.task.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.acccenter.facade.query.querytxacctinfofacade.QueryTxAcctInfoFacade;
import com.howbuy.acccenter.facade.query.querytxacctinfofacade.QueryTxAcctInfoRequest;
import com.howbuy.acccenter.facade.query.querytxacctinfofacade.QueryTxAcctInfoResponse;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.auth.facade.response.CodecSingleResponse;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.crm.base.model.CenterOrgEnum;
import com.howbuy.crm.conscust.dto.CustconstantInfoDomain;
import com.howbuy.crm.conscust.request.UpdateCustconstantInfoRequest;
import com.howbuy.crm.conscust.response.UpdateCustconstantInfoResponse;
import com.howbuy.crm.conscust.service.UpdateCustconstantInfoService;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import com.howbuy.crm.orguser.service.HbOrgUserService;
import com.howbuy.cs.base.Constants;
import com.howbuy.cs.bookcust.enums.CmsDisCodeEnum;
import com.howbuy.cs.cacheService.CacheKeyPrefix;
import com.howbuy.cs.task.dao.*;
import com.howbuy.cs.task.model.*;
import com.howbuy.cs.task.util.*;
import crm.howbuy.base.utils.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;

@Service("advisorNPDistributeTaskNewService")
public class AdvisorNPDistributeTaskNewServiceImpl implements AdvisorNPDistributeTaskNewService {
	private static final Logger log = LoggerFactory.getLogger(AdvisorNPDistributeTaskNewServiceImpl.class);
	
	// 短信接收人
	@Value("${CS.CONS.SMS.ACTIONID}")
	private String consSmsActionId;

	// 短信接收人
	@Value("${CS.CUST.SMS.ACTIONID}")
	private String custSmsActionId;
	
	@Autowired
    private DecryptSingleFacade decryptSingleFacade;
		
	@Autowired
	private AdvisorTargetDao advisorTargetDao;

	@Autowired
	private CsAutoTaskLogDao csAutoTaskLogDao;

	@Autowired
	private SendMsgDao sendMsgDao;
	@Autowired
	private CustConstantDao custConstantDao;

	@Autowired
	private SendSmsService sendSmsService;
	
	@Autowired
    private CsDeptAssignRuleConfigDao csDeptAssignRuleConfigDao;
	
	@Autowired
    private CsDeptAssignRuleOrgConfigDao csDeptAssignRuleOrgConfigDao;
	
	@Autowired
    private CsInvestadvisoryQuotaConfigDao csInvestadvisoryQuotaConfigDao;
	
	@Autowired
    private CsAssignedCustConfigMsgDao csAssignedCustConfigMsgDao;
	
	@Autowired
    private CsAssignedCustConfigLogDao csAssignedCustConfigLogDao;
	
	@Autowired
    private HbUserRoleDao hbUserRoleDao;
	
	@Autowired
	private TenpayCustDao tenpayCustDao;
	
	@Autowired
    private UpdateCustconstantInfoService updateCustconstantInfoService;

	@Autowired
	private CmPushMsgService cmPushMsgService;

	/**
	 * 当前投顾分配信息对象
	 */
	private CurrAssignRuleInfoVO currAssignRuleInfoVO;

	/**
	 * 用于记录自动分配日志信息
	 */
	private List<CsAutoDistributeLog> csAutoDistributeLogList = null;

	/**
	 * 用于更新自动分配配置表汇总的状态
	 */
	private List<CsAssignedCustConfigMsg> upCsAssignedCustConfigMsgList = null;

	/**
	 * 用于更新客户信息
	 */
	private List<CustconstantInfoDomain> updateCustMsgList = null;

	/**
	 * merge自动分配信息
	 */
	private List<CsAssignedCustConfigMsg> mergeCsAssignedCustConfigMsgList = null;

	/**
	 * 正常的省市对应的部门LinkedList
	 */
	private LinkedList<HashMap<String,LinkedList<CsDeptAssignRuleOrgConfig>>> normalProOrCityList = null;

	/**
	 * 正常的标签对应的部门LinkedList
	 */
	private LinkedList<HashMap<String,LinkedList<CsDeptAssignRuleOrgConfig>>> normalLabelList = null;

	/**
	 * 其它的省市对应的部门Map
	 */
	private HashMap<String,LinkedList<CsDeptAssignRuleOrgConfig>> qtProOrCityMap = null;

	/**
	 * 正常投顾Map
	 */
	private Map<String,LinkedList<CsInvestadvisoryQuotaConfig>> normalQuotaConfigMap = null;

	/**
	 * 部门已经分配
	 */
	private HashMap<BigDecimal,BigDecimal> orgUsedMap;

	/**
	 * 用于存放额度已满的投顾信息
	 */
	private HashMap<String,LinkedList<CsInvestadvisoryQuotaConfig>> overQuotaConfigMap = null;

	/**
	 * 用于存放对应部门比例的Map
	 */
	private HashMap<BigDecimal,String > orgRadioMap = null;

	/**
	 * 用于存放对应部门比例的使用Map
	 */
	private HashMap<BigDecimal,LinkedList<CsDeptAssignRuleOrgConfig> > orgRadioUseMap = null;

	/**
	 * 用于存放需要发送短信的Map
	 */
	private HashMap<String,String > sendMsgMap = null;

	/**
	 * 用于存放投顾使用List
	 */
	private List<String> usedCodeList = new ArrayList<>();
	
	private String[] mobilesRolePsLeader = null;
	
	@Autowired
    private QueryTxAcctInfoFacade queryTxAcctInfoFacade;
	
	@Autowired
	private EncryptSingleFacade encryptSingleFacade;

	@Autowired
	private HbOrgUserService hbOrgUserService;

	/**
	 * 消息模版id：leads分配推送消息提醒
	 */
	private static final String LEADS_TRANSFER_MSG_TEMPLATE_ID = "201984";



	@Override
	public void advisorNPTaskDistribute(String arg) {
		log.info("非公募客户投顾分配方法开始执行。。。");
		// 初始化当前投顾分配信息对象
		initUseDataFromCache();

		initRoleMobile();
		
		// 分配部门
		assignOrgCode();
		
		// 分配投顾
		assignConscode(true);

		// 数据保存到缓存
		saveUseDataToCache();

		// 给投顾发邮件短信，给客户发短信
		do_sendMsg();
		log.info("非公募客户投顾分配方法执行结束。。。");
	}

	private void initRoleMobile() {
		Map<String, Object> param = new HashMap<>();
		List<String> rolelist = new ArrayList<>();
		rolelist.add("ROLE_PS_LEADER");
		rolelist.add("ROLE_PS");
		param.put("rolelist", rolelist);
		List<HbUserRole> list = hbUserRoleDao.listHbRoleUser(param);
		if (CollectionUtils.isNotEmpty(list)) {
			mobilesRolePsLeader = new String[list.size()];
			for (int i = 0; i < list.size(); i++) {
				HbUserRole role = list.get(i);
				mobilesRolePsLeader[i] = role.getMobile();
			}
		}
	}
	
	/**
	 * 分配投顾
	 */
	private void assignConscode(boolean isSendSms) {
		// 1.处理不需要分配的数据
		opNotNeedAssign();
		
		log.info("分配投顾方法开始执行。。。");
		
		// 2.查询需要待分配的配置信息
		Map<String, Object> param = new HashMap<>();
		List<String> listStat = new ArrayList<>();
		listStat.add("0");
		listStat.add("1");
		param.put("stats", listStat);
		List<CsAssignedCustConfigMsg> listCustConfigMsg = csAssignedCustConfigMsgDao.listCsAssignedCustConfigMsg(param);
		if (CollectionUtils.isEmpty(listCustConfigMsg)) {
			log.info("此次执行没有待分配客户配置信息。。。");
			return;
		}
		log.info("待分配投顾列表：" + JSON.toJSONString(listCustConfigMsg));

		sendMsgMap = new HashMap<>();
		upCsAssignedCustConfigMsgList = new LinkedList<>();
		overQuotaConfigMap = new HashMap<>();
		usedCodeList = new ArrayList<>();
		csAutoDistributeLogList = new LinkedList<>();
		updateCustMsgList = new LinkedList<>();
        
		// 3：获取当天工作的投顾配额信息
		normalQuotaConfigMap = new HashMap<>();
		List<CsInvestadvisoryQuotaConfig> listCsInvestadvisoryQuotaConfig = csInvestadvisoryQuotaConfigDao.getTodayCsInvestadvisoryQuotaConfig();
		if (CollectionUtils.isNotEmpty(listCsInvestadvisoryQuotaConfig)) {
			LinkedList<CsInvestadvisoryQuotaConfig> tempQuotaConfigList;
			for (CsInvestadvisoryQuotaConfig csInvestadvisoryQuotaConfig : listCsInvestadvisoryQuotaConfig) {
				if (normalQuotaConfigMap.containsKey(csInvestadvisoryQuotaConfig.getOrgCode())) {
					tempQuotaConfigList = normalQuotaConfigMap.get(csInvestadvisoryQuotaConfig.getOrgCode());
				} else {
					tempQuotaConfigList = new LinkedList<>();
				}
				tempQuotaConfigList.add(csInvestadvisoryQuotaConfig);
				normalQuotaConfigMap.put(csInvestadvisoryQuotaConfig.getOrgCode(), tempQuotaConfigList);
			}
		}
        log.info("当天工作的投顾配额信息列表："+JSON.toJSONString(normalQuotaConfigMap));

		// 4：分配投顾
		for (CsAssignedCustConfigMsg csAssignedCustConfigMsg : listCustConfigMsg) {
			try {
				LinkedList<CsInvestadvisoryQuotaConfig> listQuotaConfig = normalQuotaConfigMap.get(csAssignedCustConfigMsg.getOrgcode());
				CsInvestadvisoryQuotaConfig quotaConfig = null;
				if (CollectionUtils.isNotEmpty(listQuotaConfig)) {
					int countSum = listQuotaConfig.size();
					int count = 0;
					while (listQuotaConfig.size() > 0) {
						count++;
						if (count > countSum) {
							break;
						}
						CsInvestadvisoryQuotaConfig csInvestadvisoryQuotaConfig = listQuotaConfig.pop();
						BigDecimal maxCustLimit = csInvestadvisoryQuotaConfig.getMaxCustLimit() == null ? BigDecimal.ZERO : csInvestadvisoryQuotaConfig.getMaxCustLimit();
						BigDecimal usedAdvisoryQuota = csInvestadvisoryQuotaConfig.getUsedAdvisoryQuota() == null ? BigDecimal.ZERO : csInvestadvisoryQuotaConfig.getUsedAdvisoryQuota();
						if (maxCustLimit.compareTo(usedAdvisoryQuota) > 0) {
							BigDecimal targetNum = csInvestadvisoryQuotaConfig.getTargetNum().multiply(csInvestadvisoryQuotaConfig.getMultiRate());
							if (csInvestadvisoryQuotaConfig.getCurrCustNum() == null || targetNum.compareTo(csInvestadvisoryQuotaConfig.getCurrCustNum()) > 0) {
								BigDecimal currcustNum = csInvestadvisoryQuotaConfig.getCurrCustNum() == null ? BigDecimal.ONE : csInvestadvisoryQuotaConfig.getCurrCustNum().add(BigDecimal.ONE);
								csInvestadvisoryQuotaConfig.setCurrCustNum(currcustNum);
								usedAdvisoryQuota = usedAdvisoryQuota.add(BigDecimal.ONE);
								csInvestadvisoryQuotaConfig.setUsedAdvisoryQuota(usedAdvisoryQuota);
								quotaConfig = csInvestadvisoryQuotaConfig;
								joinUsedCodeList(csInvestadvisoryQuotaConfig.getOrgCode());
								if (currcustNum.compareTo(targetNum) < 0 && maxCustLimit.compareTo(usedAdvisoryQuota) > 0) {
									listQuotaConfig.add(csInvestadvisoryQuotaConfig);
								} else {
									addOverQuotaConfigMap(csInvestadvisoryQuotaConfig);
								}
								break;
							} else {
								joinUsedCodeList(csInvestadvisoryQuotaConfig.getOrgCode());
								addOverQuotaConfigMap(csInvestadvisoryQuotaConfig);
							}
						} else {
							joinUsedCodeList(csInvestadvisoryQuotaConfig.getOrgCode());
							addOverQuotaConfigMap(csInvestadvisoryQuotaConfig);
						}
					}

					if (quotaConfig == null && isSendSms) {
						// 发送短信   ${deptName}配额已满，请至CRM-管理中心-投顾分配管理中配置！
						sendMsgMap.put(csAssignedCustConfigMsg.getOrgname(), "340");
					}
				} else {
					if (isSendSms) {
						// 发送短信   部门：${deptName} 未配置分配投顾，请至CRM-管理中心-投顾分配管理中配置！
						sendMsgMap.put(csAssignedCustConfigMsg.getOrgname(), "60221");
					}
				}
				log.info("选出投顾信息后usedCodeList：" + JSON.toJSONString(usedCodeList));
				log.info("选出投顾信息后overQuotaConfigMap：" + JSON.toJSONString(overQuotaConfigMap));
				log.info("选择出的投顾信息为：" + JSON.toJSONString(quotaConfig));
				log.info("选择的分配信息为：" + JSON.toJSONString(csAssignedCustConfigMsg));
				packagingInformation(csAssignedCustConfigMsg, quotaConfig);
			} catch (Exception e) {
				log.error("分配投顾失败,失败的配置信息为：" + JSON.toJSONString(csAssignedCustConfigMsg));
				log.error("分配投顾失败,失败原因为：" + e.getMessage(), e);
			}
		}


		// 5：更新投顾额度配置中的已使用和级别
		updateCurrcustNumAndLevelnum();

		// 6.更新 客户信息、日志信息、更新 待分配任务状态
		updateAssignedData();

		// 7.发送短信
		if (isSendSms) {
			handSendMsg();
		}
		log.info("分配投顾方法执行结束。。。");
	}
	
	/**
	 * 处理需要发送短信的数据
	 */
	private void handSendMsg() {
		log.info("处理发送短信方法开始执行。。。");
		Iterator<Entry<String, String>> normal_It = sendMsgMap.entrySet().iterator();
		String key = null;
		String value = null;
		while (normal_It.hasNext()) {
			try {
				Entry<String, String> entry = normal_It.next();
				key = entry.getKey();
				value = entry.getValue();
				sendSmsInfoToRolePsLeader(key, value);
				log.info("发送短信，部门：" + key + ",短信类型：" + value);
			} catch (Exception e) {
				log.error("发送短信失败，短信的部门名称为：" + key + ",短信类型为：" + value);
				log.error("发送短信失败，失败原因为：" + e.getMessage(), e);
			}
		}
		log.info("处理发送短信方法执行结束。。。");
	}
	
	
	/**
	 * 处理不需要分配的数据
	 */
	private void opNotNeedAssign() {
		log.info("处理不需要分配分配的数据方法开始执行。。。");
		//待处理分配任务中的客户已手动分配 或 客户号不存在 或 待处理分配任务中的部门不存在 ，则将分配状态置为 归档；
		csAssignedCustConfigMsgDao.updateInvalidCust();
		log.info("处理不需要分配分配的数据方法执行结束。。。");
	}

	private void joinUsedCodeList(String orgCode) {
		boolean joinstat = true;
		for (String str : usedCodeList) {
			if (str.equals(orgCode)) {
				joinstat = false;
			}
		}
		if (joinstat) {
			usedCodeList.add(orgCode);
		}
	}

	private void addOverQuotaConfigMap(CsInvestadvisoryQuotaConfig csInvestadvisoryQuotaConfig) {
		LinkedList<CsInvestadvisoryQuotaConfig> tempList;
		if (overQuotaConfigMap.containsKey(csInvestadvisoryQuotaConfig.getOrgCode())) {
			tempList = overQuotaConfigMap.get(csInvestadvisoryQuotaConfig.getOrgCode());
		} else {
			tempList = new LinkedList<>();
		}
		tempList.add(csInvestadvisoryQuotaConfig);
		overQuotaConfigMap.put(csInvestadvisoryQuotaConfig.getOrgCode(), tempList);
	}
	
	/**
	 * 更新投顾额度配置中的已使用和级别
	 */
	private void updateCurrcustNumAndLevelnum() {
		if (CollectionUtils.isNotEmpty(usedCodeList)) {
			for (String orgCode : usedCodeList) {
				LinkedList<CsInvestadvisoryQuotaConfig> updateQuotaConfigList = new LinkedList<>();

				// 当前最新的排序号
				int currLevelNum = 0;
				LinkedList<CsInvestadvisoryQuotaConfig> linkedList = normalQuotaConfigMap.get(orgCode);
				CsInvestadvisoryQuotaConfig config = null;
				if (CollectionUtils.isNotEmpty(linkedList)) {
					while (linkedList.size() > 0) {
						config = linkedList.pop();
						++currLevelNum;
						config.setLevelNum(new BigDecimal(currLevelNum));
						updateQuotaConfigList.add(config);
					}
				}

				linkedList = overQuotaConfigMap.get(orgCode);
				if (CollectionUtils.isNotEmpty(linkedList)) {
					while (linkedList.size() > 0) {
						config = linkedList.pop();
						++currLevelNum;
						config.setLevelNum(new BigDecimal(currLevelNum));
						updateQuotaConfigList.add(config);
					}
				}

				if (CollectionUtils.isNotEmpty(updateQuotaConfigList)) {
					log.info("更新投顾信息updateQuotaConfigList:" + JSON.toJSONString(updateQuotaConfigList));
					boolean updateOne = csInvestadvisoryQuotaConfigDao.updateBatchInvestadvisoryQuotaConfig(updateQuotaConfigList) > 0;
					if (updateOne) {
						log.info("批量更新指定部门的投顾额度表中的信息,orgCode:" + orgCode + ",更新条数：" + updateOne);
					}
				}
			}
		}
	}
	
	/**
	 * 更新分配相关数据：更新 客户信息、日志信息、更新 待分配任务状态
	 */
	private void updateAssignedData() {
		if (CollectionUtils.isNotEmpty(updateCustMsgList)) {
			// 调用core中接口进行客户投顾划转
			UpdateCustconstantInfoRequest reqParam = new UpdateCustconstantInfoRequest();
			reqParam.setListCustconstantInfoDomain(updateCustMsgList);
			reqParam.setHasHisId("1");
			UpdateCustconstantInfoResponse updateCustconstantInfoResponse = updateCustconstantInfoService.batchUpdateCustConstant(reqParam);
			if ("0000".equals(updateCustconstantInfoResponse.getReturnCode())) {
				// 批量向自动分配日志表中插入数据
				csAutoTaskLogDao.insertBatchAddAutoDistributeLog(csAutoDistributeLogList);
				log.info("批量向自动分配日志表中插入数据");
			} else {
				log.info("批量划转投顾失败！");
			}
		}

		if (CollectionUtils.isNotEmpty(upCsAssignedCustConfigMsgList)) {
			boolean updateThr = csAssignedCustConfigMsgDao.updateBatchCsAssignedCustConfigMsg(upCsAssignedCustConfigMsgList) > 0;
			if (updateThr) {
				log.info("批量更新分配表中的投顾信息");
			}
		}

		//把待分配表中 归档和分配成功的数据搬到日志表中
		int count = csAssignedCustConfigLogDao.insertCsAssignedCustConfigLog();
		log.info("待分配表中归档和分配成功数据插入日志表成功！插入条数：" + count);

		if (count > 0) {
			count = csAssignedCustConfigMsgDao.removeCsAssignedCustConfigMsg();
			log.info("待分配表中归档和分配成功数据删除成功！删除条数：" + count);
		}
	}
	
	/**
	 * 封装保持的客户信息、日志信息、更新 待分配任务状态
	 */
	private void packagingInformation(CsAssignedCustConfigMsg csAssignedCustConfigMsg, CsInvestadvisoryQuotaConfig csInvestadvisoryQuotaConfig) {
		CsAssignedCustConfigMsg configMsg = new CsAssignedCustConfigMsg();
		configMsg.setId(csAssignedCustConfigMsg.getId());

		// 设置记录操作人
		String operator = csAssignedCustConfigMsg.getCreator();
		if (StringUtils.isBlank(operator)) {
			configMsg.setModifier("sys-task");
		} else {
			configMsg.setModifier(operator);
		}

		configMsg.setModifydt(new Date());
		if (csInvestadvisoryQuotaConfig == null) {
			configMsg.setStat("1");//分配投顾失败
		} else {
			configMsg.setStat("2");//分配投顾成功
			String newConsCode=csInvestadvisoryQuotaConfig.getAdvisorId();
			configMsg.setConscode(newConsCode);
			configMsg.setConsname(csInvestadvisoryQuotaConfig.getAdvisorName());
			
			CustconstantInfoDomain cc = new CustconstantInfoDomain();
			cc.setCustno(csAssignedCustConfigMsg.getConscustno());
			cc.setStartdt(DateTimeUtil.getCurDate());
            cc.setConscode(newConsCode);
            cc.setNextcons(newConsCode);
			String reason;
			if(isThBySpecialMark(csAssignedCustConfigMsg.getSpecialmark())){
				reason= StaticVar.TRANSF_REASON_TH;
			}else{
				//仅当客户由 非IC[CenterOrgEnum.IC]/非HBC[CenterOrgEnum.OVERSEAS_BRANCH]/非机构[CenterOrgEnum.BUSDEP] 分配至 IC/HBC/机构 时，原因取“6-Leads分配”；
               // 其他情况均取“9-其他（客服）”；
				String assignConsCode=csAssignedCustConfigMsg.getConscode();//csAssigned表中带的consCode .未知含义
				String oldConsCode=custConstantDao.getConsCodeByCustNo(csAssignedCustConfigMsg.getConscustno());
				boolean isLeadsReason=isLeadsReason(oldConsCode,newConsCode);
				log.info("客户custNo:{},assignConsCode：{}，oldConsCode:{}, newConsCode:{},分配原因是否为：6-Leads分配:{}"
						,csAssignedCustConfigMsg.getConscustno(),assignConsCode,
						oldConsCode,newConsCode,isLeadsReason);
				reason=isLeadsReason? StaticVar.TRANSF_REASON_LEADSFP: StaticVar.TRANSF_REASON_OTHERKF;

			}
            cc.setReason(reason);
            String custtype = "新客户"; //客户类型:新客户；1:老客户
			try {
				QueryTxAcctInfoRequest queryTxAcctInfoRequest = new QueryTxAcctInfoRequest();
				queryTxAcctInfoRequest.setIdNoDigest(csAssignedCustConfigMsg.getIdnoDigest());
				queryTxAcctInfoRequest.setIdType(csAssignedCustConfigMsg.getIdtype());
				queryTxAcctInfoRequest.setInvstType(csAssignedCustConfigMsg.getInvsttype());
				if ("1".equals(csAssignedCustConfigMsg.getInvsttype())) {
					queryTxAcctInfoRequest.setDisCode("HB000A001");
				} else {
					queryTxAcctInfoRequest.setDisCode("FOF201710");
					queryTxAcctInfoRequest.setCustName(csAssignedCustConfigMsg.getCustname());
				}
				log.info("queryTxAcctInfoFacade.queryTxAcctInfoRequest：" + JSON.toJSONString(queryTxAcctInfoRequest));
				QueryTxAcctInfoResponse queryTxAcctInfoResponsersp = queryTxAcctInfoFacade.execute(queryTxAcctInfoRequest);
				log.info("queryTxAcctInfoFacade.QueryTxAcctInfoResponse：" + JSON.toJSONString(queryTxAcctInfoResponsersp));
				if (queryTxAcctInfoResponsersp != null && "0000000".equals(queryTxAcctInfoResponsersp.getReturnCode()) && StringUtils.isNotBlank(queryTxAcctInfoResponsersp.getDisTxAcctNo())) {
					custtype = "老客户";
				}
			} catch (Exception ex) {
				log.error("noTransaction:" + ex.getMessage(), ex);
			}
            // 客户类型
            cc.setCusttype(custtype);

			// 设置记录操作人
			if (StringUtils.isBlank(operator)) {
				cc.setModifier("sys-task");
				cc.setCreator("sys-task");
			} else {
				cc.setModifier(operator);
				cc.setCreator(operator);
			}
            cc.setModdt(DateTimeUtil.getCurDate());
            cc.setCustconstanthis(tenpayCustDao.getSeqValue("SEQ_CUSTREC").toString());
            updateCustMsgList.add(cc);
            
            configMsg.setCustconstanthis(cc.getCustconstanthis());
            
            // 添加自动分配日志记录
            CsAutoDistributeLog csAutoDistributeLog = new CsAutoDistributeLog();
			csAutoDistributeLog.setAdvisorId(csInvestadvisoryQuotaConfig.getAdvisorId());
			csAutoDistributeLog.setAdvisorName(csInvestadvisoryQuotaConfig.getAdvisorName());
			csAutoDistributeLog.setCustNo(csAssignedCustConfigMsg.getConscustno());
			csAutoDistributeLog.setCustName(csAssignedCustConfigMsg.getCustname());
			csAutoDistributeLog.setOrgCode(csAssignedCustConfigMsg.getOrgcode());
			csAutoDistributeLog.setProcId(csAssignedCustConfigMsg.getProcode());
			if(csAssignedCustConfigMsg.getWaitid() != null ){
				csAutoDistributeLog.setWaitId( Long.parseLong(csAssignedCustConfigMsg.getWaitid()));
			}
			csAutoDistributeLog.setStatisticType(csAssignedCustConfigMsg.getStatistictype());
			
			// 逻辑调整，对客户类型，默认设置为howBuy
			csAutoDistributeLog.setCustType(1); // howBuy
			csAutoDistributeLog.setAutoType(1); // “1”表示不是公募客户自动分配
			csAutoDistributeLog.setPubCustFlag(0);
			csAutoDistributeLog.setCustMobileCipher(csAssignedCustConfigMsg.getCustmobileCipher());
			csAutoDistributeLog.setCustMobileDigest(csAssignedCustConfigMsg.getCustmobileDigest());
			csAutoDistributeLog.setCustMobileMask(csAssignedCustConfigMsg.getCustmobileMask());
			csAutoDistributeLog.setCustSmsFlag(csAssignedCustConfigMsg.getCustsmsflag());

			csAutoDistributeLogList.add(csAutoDistributeLog); 
		}
		
		upCsAssignedCustConfigMsgList.add(configMsg);
		
	}


	/**
	 * 当客户由
	 * 非IC[CenterOrgEnum.IC]/非HBC[CenterOrgEnum.OVERSEAS_BRANCH]/非机构事业部[orgCode=150000001]
	 * 分配至
	 * IC/HBC/机构事业部[orgCode=150000001] 时，原因取“6-Leads分配”
	 * @return 满足条件 返回true
	 */
	private boolean isLeadsReason(String oldConsCode,String newConsCode){
		if(StringUtil.isEmpty(oldConsCode) || StringUtil.isEmpty(newConsCode)){
			return false;
		}
		List<String> orgList= Lists.newArrayList(CenterOrgEnum.IC.getCode(),CenterOrgEnum.OVERSEAS_BRANCH.getCode(),"150000001");
		boolean oldBoolean=hbOrgUserService.isConscodeUnderOrgList(oldConsCode,orgList);
		if(oldBoolean){
			return false;
		}
		boolean newBoolean=hbOrgUserService.isConscodeUnderOrgList(newConsCode,orgList);

		return newBoolean;
	}
	
	
	/**
	 * 分配部门
	 */
	private void assignOrgCode() {
		log.info("分配部门方法开始执行。。。");

		// 在客服沟通表中获取没有分配投顾的客户信息，并且不是垃圾客户
		List<Map<String, Object>> noAdvisorCusts = advisorTargetDao.getCallOutNoAdvisorCustNew();
		log.info("呼出待分配的客户数量：" + noAdvisorCusts.size());

		// 获取呼入待分配的客户数量
		List<Map<String, Object>> callInNoAdvisorCusts = advisorTargetDao.getCallInNoAdvisorCustNew();
		log.info("呼入待分配的客户数量" + callInNoAdvisorCusts.size());

		log.info("noAdvisorCusts.size() = " + noAdvisorCusts.size() + " callInNoAdvisorCusts.size() = " + callInNoAdvisorCusts.size());

		noAdvisorCusts.addAll(callInNoAdvisorCusts);
		log.info("总共客户数量：noAdvisorCusts.size = " + noAdvisorCusts.size());
		log.info("待分配的客户信息：" + JSON.toJSONString(noAdvisorCusts));

		if (noAdvisorCusts.size() == 0) {
			log.info("没有客户可以分配给部门！");
		} else {
			// 初始化一些自动分配需要的集合
			initCollection();
			
			log.info("初始化投顾和部门对应的map和list");		
			initAdvisorDeptMapList();

			List<String> conscustnoList = new ArrayList<>();
			for (int k = 0; k < noAdvisorCusts.size(); k++) {
				Map<String, Object> tempMap = noAdvisorCusts.get(k);
				try {
					String conscustNO = (String) tempMap.get("CONSCUSTNO");
					if (StringUtils.isNotBlank(conscustNO)) {
						if (conscustnoList.contains(conscustNO)) {
							continue;
						}
						conscustnoList.add(conscustNO);
					}
					String custName = (String) tempMap.get("CUSTNAME");
					String invsttype = (String) tempMap.get("INVSTTYPE");
					String idnoCipher = (String) tempMap.get("IDNO_CIPHER");
					String idno = null;
					if (StringUtils.isNotBlank(idnoCipher)) {
						idno = decryptSingleFacade.decrypt(idnoCipher).getCodecText();
					}
					String idtype = (String) tempMap.get("IDTYPE");

					String custMobileCipher = tempMap.get("MOBILE_CIPHER") == null ? null : tempMap.get("MOBILE_CIPHER").toString(); // 客户手机号密文
					String custMobile = null;
					if (StringUtils.isNotBlank(custMobileCipher)) {
						log.info("decryptSingleFacade.decrypt请求： " + custMobileCipher);
						CodecSingleResponse codecSingleResponse = decryptSingleFacade.decrypt(custMobileCipher);
						custMobile = codecSingleResponse.getCodecText();
						log.info("decryptSingleFacade.decrypt返回： " + JSON.toJSONString(codecSingleResponse));
					}

					String subCustMobile = StringUtils.isEmpty(custMobile) ? "" : custMobile.length() >= 7 ? custMobile.substring(0, 7) : "";
					int custSmsFlag = Integer.parseInt(tempMap.get("CUSTSMSFLAG").toString());
					String provCode = (String) tempMap.get("PROVCODE"); // 获取手机归属地省份

					long waitId = Long.parseLong(tempMap.get("WAITID").toString());
					int statisticType = Integer.parseInt(tempMap.get("STATISTICTYPE").toString());
					BigDecimal tasktypeBig = (BigDecimal) tempMap.get("TASKTYPE");
					String tasktype = tasktypeBig == null ? "" : tasktypeBig.toString();
					String specialmark = (String) tempMap.get("SPECIALMARK");

					// 实际处理人
					String userId = (String) tempMap.get("USERID");

					Map<String, String> phoneAreaMap = advisorTargetDao.qryPhoneArea(subCustMobile);
					String proCode = null;
					String cityCode = null;
					if (phoneAreaMap != null) {
						proCode = StringUtils.isEmpty(phoneAreaMap.get("PROVCITY_PROVCODE")) ? "" : phoneAreaMap.get("PROVCITY_PROVCODE");
						cityCode = StringUtils.isEmpty(phoneAreaMap.get("PROVCITY_CITYCODE")) ? "" : phoneAreaMap.get("PROVCITY_CITYCODE");
					}

					log.info("客户信息：conscustNO = " + conscustNO + " provCode = " + provCode + " proCode = " + proCode + " cityCode = " + cityCode  + " custName = " + custName + " custMobile = " + custMobile
							+ " custSmsFlag = " + custSmsFlag + " waitId = " + waitId + " statisticType = " + statisticType + " tasktype " + tasktype + " invsttype " + invsttype + " idno " + idno + " idtype " + idtype
							+ " specialmark = " + specialmark);

					// 初始化CurrAdivisorDistributeInfoVO对象
					initCurrAdivisorDistributeInfoVO(proCode, cityCode,specialmark);

					// 表示所在部门还有额度未满的投顾
					if (currAssignRuleInfoVO.isExistDeptFlag()) {
						log.info("客户有对应的部门：" + JSON.toJSONString(currAssignRuleInfoVO.getCsDeptAssignRuleOrgConfig()));

						// 初始化投顾客户关系
						initCustconstantList(conscustNO, custName, custMobile, custSmsFlag, waitId, statisticType, tasktype, proCode, cityCode, invsttype, idno, idtype, specialmark, userId);
					} else {
						log.info("没有部门可以分配");
					}
				} catch (Exception e) {
					log.error("分配部门失败,失败的配置信息为：" + JSON.toJSONString(tempMap));
					log.error("分配部门失败,失败原因为：" + e.getMessage(), e);
				}
			}
		}

		if (CollectionUtils.isNotEmpty(mergeCsAssignedCustConfigMsgList)) {
			csAssignedCustConfigMsgDao.mergeCsAssignedCustConfigMsgList(mergeCsAssignedCustConfigMsgList);
			log.info("批量merge自动客户分配表：mergeCsAssignedCustConfigMsgList：" + JSON.toJSONString(mergeCsAssignedCustConfigMsgList));
		}
		log.info("分配部门方法执行结束。。。");
	}

	/**
	 * 初始化投顾客户关系
	 */
	private void initCustconstantList(String conscustNO, String custName, String custMobile, int custSmsFlag, long waitId, int statisticType, String tasktype,
									  String proCode, String cityCode, String invsttype, String idno, String idtype, String specialmark, String userId) {
		CsDeptAssignRuleOrgConfig config = currAssignRuleInfoVO.getCsDeptAssignRuleOrgConfig();
		CsAssignedCustConfigMsg csAssignedCustConfigMsg = new CsAssignedCustConfigMsg();
		csAssignedCustConfigMsg.setOrgconfigid(config.getId());
		csAssignedCustConfigMsg.setConscustno(conscustNO);
		csAssignedCustConfigMsg.setCustname(custName);
		csAssignedCustConfigMsg.setOrgcode(config.getOrgcode());
		csAssignedCustConfigMsg.setOrgname(config.getOrgname());
		csAssignedCustConfigMsg.setProcode(proCode);
		csAssignedCustConfigMsg.setCitycode(cityCode);
		csAssignedCustConfigMsg.setCreatedt(new Date());
		csAssignedCustConfigMsg.setCreator(userId);
		csAssignedCustConfigMsg.setStat("0");//初始化
		csAssignedCustConfigMsg.setTasktype(tasktype);
		csAssignedCustConfigMsg.setWaitid(String.valueOf(waitId));
		csAssignedCustConfigMsg.setStatistictype(statisticType);
		csAssignedCustConfigMsg.setCustmobile(custMobile);
		if (StringUtils.isNotBlank(custMobile)) {
			csAssignedCustConfigMsg.setCustmobileDigest(DigestUtil.digest(custMobile.trim()));
			csAssignedCustConfigMsg.setCustmobileMask(MaskUtil.maskMobile(custMobile.trim()));
			csAssignedCustConfigMsg.setCustmobileCipher(encryptSingleFacade.encrypt(custMobile.trim()).getCodecText());
		}
		csAssignedCustConfigMsg.setCustsmsflag(custSmsFlag);
		csAssignedCustConfigMsg.setModifier(userId);
		csAssignedCustConfigMsg.setModifydt(new Date());
		csAssignedCustConfigMsg.setIdno(idno);
		if (StringUtils.isNotBlank(idno)) {
			csAssignedCustConfigMsg.setIdnoDigest(DigestUtil.digest(idno.trim()));
			csAssignedCustConfigMsg.setIdnoMask(MaskUtil.maskIdNo(idno.trim()));
			csAssignedCustConfigMsg.setIdnoCipher(encryptSingleFacade.encrypt(idno.trim()).getCodecText());
		}
		csAssignedCustConfigMsg.setInvsttype(invsttype);
		csAssignedCustConfigMsg.setIdtype(idtype);
		csAssignedCustConfigMsg.setSpecialmark(specialmark);
		mergeCsAssignedCustConfigMsgList.add(csAssignedCustConfigMsg);
	}


	/**
	 * 是否同行的判断
	 * @param specialmark
	 * @return
	 */
	private  boolean isThBySpecialMark(String specialmark){
     return StringUtils.isNotEmpty(specialmark) && "0001000000".equals(specialmark);
	}
	
	/**
	 * 初始化CurrAdivisorDistributeInfoVO
	 */
	private void initCurrAdivisorDistributeInfoVO(String proCode, String cityCode, String specialmark) {
		currAssignRuleInfoVO = new CurrAssignRuleInfoVO(); // 初始化对象
		CsDeptAssignRuleOrgConfig assignRuleOrgConfig = null;

		// 同行标签
		if (isThBySpecialMark(specialmark)) {
			assignRuleOrgConfig = getAssignRuleOrgConfig("2", proCode, cityCode, "1");
			// 省市
		} else if (StringUtils.isNotEmpty(proCode) || StringUtils.isNotEmpty(cityCode)) {
			assignRuleOrgConfig = getAssignRuleOrgConfig("1", proCode, cityCode, null);
		}
		
		// 判断其它是否有符合规则的分配
		if (assignRuleOrgConfig == null) {
			if (qtProOrCityMap != null && qtProOrCityMap.size() > 0) {
//				String keyStr = qtProOrCityMap.keySet().iterator().next();//见鬼的逻辑。map里就只有一个key值实际。 2023年1月30日
				Entry<String, LinkedList<CsDeptAssignRuleOrgConfig>> entryTemp=qtProOrCityMap.entrySet().iterator().next();
				String keyStr=entryTemp.getKey();
				if ("0".equals(keyStr)) {
					LinkedList<CsDeptAssignRuleOrgConfig> linkedList = entryTemp.getValue();//qtProOrCityMap.get(keyStr);
					if (CollectionUtils.isNotEmpty(linkedList)) {
						int cycleIndex = 0;
						for (CsDeptAssignRuleOrgConfig config : linkedList) {
							cycleIndex += config.getPayoutratio().intValue();
						}
						for (int i = 0; i < cycleIndex; i++) {
							String orgRadioStr = orgRadioMap.get(linkedList.get(0).getRoleconfigid());
							if (StringUtils.isEmpty(orgRadioStr)) {
								initRadioUse(linkedList, true);// 初始化
								LinkedList<CsDeptAssignRuleOrgConfig> tempUser = orgRadioUseMap.get(linkedList.get(0).getRoleconfigid());
								assignRuleOrgConfig = tempUser.pop();
							} else {
								//判断比例是否相等
								String orgRadioStrTemp=joinContentString(linkedList);
								//orgRadioStrTemp = orgRadioStrTemp.substring(0, orgRadioStrTemp.length()-1);
								if (orgRadioStrTemp.equals(orgRadioStr)) {
									LinkedList<CsDeptAssignRuleOrgConfig> tempUser = orgRadioUseMap.get(linkedList.get(0).getRoleconfigid());
									if (tempUser.size() != 0) {//判断存在使用
										assignRuleOrgConfig = tempUser.pop();
									} else {// 已不存在使用：初始化
										initRadioUse(linkedList, false);// 初始化
										tempUser = orgRadioUseMap.get(linkedList.get(0).getRoleconfigid());
										assignRuleOrgConfig = tempUser.pop();
									}
								} else {// 初始化
									initRadioUse(linkedList, true);// 初始化
									LinkedList<CsDeptAssignRuleOrgConfig> tempUser = orgRadioUseMap.get(linkedList.get(0).getRoleconfigid());
									assignRuleOrgConfig = tempUser.pop();
								}
							}

							if (assignRuleOrgConfig == null) {
								continue;
							}

							if (assignRuleOrgConfig.getPayoutupper() != null &&
									assignRuleOrgConfig.getPayoutupper().compareTo(orgUsedMap.get(assignRuleOrgConfig.getId()) == null ? BigDecimal.ZERO : orgUsedMap.get(assignRuleOrgConfig.getId())) <= 0) {
								assignRuleOrgConfig = null;
								continue;
							}

							if (assignRuleOrgConfig != null) {
								BigDecimal tempcount = orgUsedMap.get(assignRuleOrgConfig.getId());
								orgUsedMap.put(assignRuleOrgConfig.getId(), tempcount == null ? BigDecimal.ONE : tempcount.add(BigDecimal.ONE));
							}
							log.info("其它循环次数：" + i);
							log.info("其它：得到部门后对应orgRadioMap： " + (orgRadioMap == null ? "" : JSON.toJSONString(orgRadioMap)));
							log.info("其它：得到部门后对应orgRadioUseMap： " + (orgRadioUseMap == null ? "" : JSON.toJSONString(orgRadioUseMap)));
							break;
						}
					}
				}
			}
		}
		
		if(assignRuleOrgConfig != null ){
			currAssignRuleInfoVO.setExistDeptFlag(true);
			currAssignRuleInfoVO.setCsDeptAssignRuleOrgConfig(assignRuleOrgConfig);
		}
	}


	/**
	 * 拼接 id,payoutratio  ;隔开
	 * @param linkedList
	 * @return
	 */
	private String joinContentString(LinkedList<CsDeptAssignRuleOrgConfig> linkedList){
		StringBuilder orgRadioStrTemp=new StringBuilder();
		for (CsDeptAssignRuleOrgConfig csDeptAssignRuleOrgConfig : linkedList) {
			orgRadioStrTemp.append(csDeptAssignRuleOrgConfig.getId().toString()).append(",").append(csDeptAssignRuleOrgConfig.getPayoutratio().toString()).append(";");
		}
		return orgRadioStrTemp.toString();
	}
	
	/**
	 * 获取待分配的部门信息
	 * @param type：1-省市；2-同行
	 * @return
	 */
	private CsDeptAssignRuleOrgConfig getAssignRuleOrgConfig(String type,String proCode,String cityCode,String labelcode){
		CsDeptAssignRuleOrgConfig assignRuleOrgConfig = null;
		boolean isBreak = false;
		LinkedList<HashMap<String, LinkedList<CsDeptAssignRuleOrgConfig>>> roleOrgConfigLinkedList;
		boolean isContains;
		if ("2".equals(type)) {
			roleOrgConfigLinkedList = normalLabelList;
		} else {
			roleOrgConfigLinkedList = normalProOrCityList;
		}
		if(CollectionUtils.isNotEmpty(roleOrgConfigLinkedList)){
			for (Iterator<HashMap<String, LinkedList<CsDeptAssignRuleOrgConfig>>> iterator = roleOrgConfigLinkedList.iterator(); iterator.hasNext(); ) {
				if (isBreak) {
					break;
				}
				HashMap<String, LinkedList<CsDeptAssignRuleOrgConfig>> tempMap = iterator.next();
				if (tempMap == null || tempMap.size() == 0) {
					iterator.remove();
				} else {
//					String keyStr = tempMap.keySet().iterator().next();//见鬼的逻辑。map里就只有一个key值实际。 2023年1月30日
//					List<String> keyList = Arrays.asList(keyStr.split(","));
					Entry<String, LinkedList<CsDeptAssignRuleOrgConfig>> entryTemp=tempMap.entrySet().iterator().next();
					List<String> keyList = Arrays.asList(entryTemp.getKey().split(","));
					//同行标签
					if ("2".equals(type)) {
						isContains = keyList.contains(labelcode);
						//省市
					} else {
						isContains = "1".equals(type) && (keyList.contains(proCode) || keyList.contains(cityCode));
					}

					if (isContains) {
						LinkedList<CsDeptAssignRuleOrgConfig> linkedList = entryTemp.getValue();//tempMap.get(keyStr);
						if (CollectionUtils.isNotEmpty(linkedList)) {
							int cycleIndex = 0;
							for (CsDeptAssignRuleOrgConfig config : linkedList) {
								cycleIndex += config.getPayoutratio().intValue();
							}
							for (int i = 0; i < cycleIndex; i++) {
								String orgRadioStr = orgRadioMap.get(linkedList.get(0).getRoleconfigid());
								if (StringUtils.isEmpty(orgRadioStr)) {
									initRadioUse(linkedList, true);//初始化
									LinkedList<CsDeptAssignRuleOrgConfig> tempUser = orgRadioUseMap.get(linkedList.get(0).getRoleconfigid());
									assignRuleOrgConfig = tempUser.pop();
								} else {
									//判断比例是否相等//
									//orgRadioStrTemp = orgRadioStrTemp.substring(0, orgRadioStrTemp.length()-1);
									String orgRadioStrTemp=joinContentString(linkedList);
									if (orgRadioStrTemp.equals(orgRadioStr)) {
										LinkedList<CsDeptAssignRuleOrgConfig> tempUser = orgRadioUseMap.get(linkedList.get(0).getRoleconfigid());
										if (tempUser.size() != 0) {//判断存在使用
											assignRuleOrgConfig = tempUser.pop();
										} else {//已不存在使用：初始化
											initRadioUse(linkedList, false);//初始化
											tempUser = orgRadioUseMap.get(linkedList.get(0).getRoleconfigid());
											assignRuleOrgConfig = tempUser.pop();
										}
									} else {//初始化
										initRadioUse(linkedList, true);//初始化
										LinkedList<CsDeptAssignRuleOrgConfig> tempUser = orgRadioUseMap.get(linkedList.get(0).getRoleconfigid());
										assignRuleOrgConfig = tempUser.pop();
									}
								}

								if (assignRuleOrgConfig == null) {
									continue;
								}

								if (assignRuleOrgConfig.getPayoutupper() != null &&
										assignRuleOrgConfig.getPayoutupper().compareTo(orgUsedMap.get(assignRuleOrgConfig.getId()) == null ? BigDecimal.ZERO : orgUsedMap.get(assignRuleOrgConfig.getId())) <= 0) {
									assignRuleOrgConfig = null;
									continue;
								} else {
									BigDecimal tempcount = orgUsedMap.get(assignRuleOrgConfig.getId());
									orgUsedMap.put(assignRuleOrgConfig.getId(), tempcount == null ? BigDecimal.ONE : tempcount.add(BigDecimal.ONE));
								}
								
								isBreak = true;
								log.info("非其它循环次数：" + i);
								log.info("非其它：得到部门后对应orgRadioMap： " + (orgRadioMap == null ? "" : JSON.toJSONString(orgRadioMap)));
								log.info("非其它：得到部门后对应orgRadioUseMap： " + (orgRadioUseMap == null ? "" : JSON.toJSONString(orgRadioUseMap)));
								break;
							}
						}
						
					}
				}
			}
		}
		
		return assignRuleOrgConfig;
	}
	
	/**
	 * 初始化投顾和部门对应的map和list
	 */
	private void initRadioUse(LinkedList<CsDeptAssignRuleOrgConfig> listCsDeptAssignRuleOrgConfig, boolean upOrgRadioMapStat) {
		String ratioStr;
		BigDecimal payoutratio;
		LinkedList<CsDeptAssignRuleOrgConfig> linkedList = new LinkedList<>();
		if (upOrgRadioMapStat) {
			orgRadioMap.put(listCsDeptAssignRuleOrgConfig.get(0).getRoleconfigid(), "");
		}

		for (CsDeptAssignRuleOrgConfig config : listCsDeptAssignRuleOrgConfig) {
			payoutratio = config.getPayoutratio();
			if (upOrgRadioMapStat) {
				if (orgRadioMap.containsKey(config.getRoleconfigid())) {
					ratioStr = orgRadioMap.get(config.getRoleconfigid()) + config.getId().toString() + "," + payoutratio.toString() + ";";
				} else {
					ratioStr = config.getId().toString() + "," + payoutratio.toString() + ";";
				}
				orgRadioMap.put(config.getRoleconfigid(), ratioStr);
			}

			for (int i = 0; i < payoutratio.intValue(); i++) {
				linkedList.add(config);
			}
		}
		orgRadioUseMap.put(listCsDeptAssignRuleOrgConfig.get(0).getRoleconfigid(), linkedList);

		log.info("初始化投顾和部分状态： " + upOrgRadioMapStat);
		log.info("初始化投顾和部门对应orgRadioMap： " + (orgRadioMap == null ? "" : JSON.toJSONString(orgRadioMap)));
		log.info("初始化投顾和部门对应orgRadioUseMap： " + (JSON.toJSONString(orgRadioUseMap)));
	}
	
	/**
	 * 初始化自动分配需要的list和map等集合
	 */
	private void initCollection() {
		mergeCsAssignedCustConfigMsgList = new LinkedList<>();
		normalProOrCityList = new LinkedList<>();
		normalLabelList = new LinkedList<>();
		qtProOrCityMap = new HashMap<>();
		orgUsedMap = new HashMap<>();
		if (orgRadioMap == null) {
			orgRadioMap = new HashMap<>();
		}
		if (orgRadioUseMap == null) {
			orgRadioUseMap = new HashMap<>();
		}
	}


	/**
	 * 初始化投顾和部门对应的map和list
	 */
	private void initAdvisorDeptMapList() {
		Map<String, Object> param = new HashMap<>();
		param.put("isdel", "1");
		List<CsDeptAssignRuleConfig> listCsDeptAssignRuleConfig = csDeptAssignRuleConfigDao.listCsDeptAssignRuleConfig(param);
		if (CollectionUtils.isNotEmpty(listCsDeptAssignRuleConfig)) {
			for (CsDeptAssignRuleConfig csDeptAssignRuleConfig : listCsDeptAssignRuleConfig) {
				param.put("roleconfigid", csDeptAssignRuleConfig.getId());
				List<CsDeptAssignRuleOrgConfig> listCsDeptAssignRuleOrgConfig = csDeptAssignRuleOrgConfigDao.listCsDeptAssignRuleOrgConfig(param);
				LinkedList<CsDeptAssignRuleOrgConfig> tempList = new LinkedList<>();
				if ("0".equals(csDeptAssignRuleConfig.getAttributioncodeflag())) {
					for (CsDeptAssignRuleOrgConfig csDeptAssignRuleOrgConfig : listCsDeptAssignRuleOrgConfig) {
						tempList.add(csDeptAssignRuleOrgConfig);
					}
					qtProOrCityMap.put(csDeptAssignRuleConfig.getAttributioncodeflag(), tempList);
				} else {
					HashMap<String, LinkedList<CsDeptAssignRuleOrgConfig>> tempMap = new HashMap<>();
					for (CsDeptAssignRuleOrgConfig csDeptAssignRuleOrgConfig : listCsDeptAssignRuleOrgConfig) {
						tempList.add(csDeptAssignRuleOrgConfig);
					}

					//2-标签;1-归属地
					if ("2".equals(csDeptAssignRuleConfig.getRuletype())) {
						tempMap.put(csDeptAssignRuleConfig.getLabelcode(), tempList);
						normalLabelList.add(tempMap);
					} else if ("1".equals(csDeptAssignRuleConfig.getRuletype())) {
						tempMap.put(csDeptAssignRuleConfig.getAttributioncodeflag(), tempList);
						normalProOrCityList.add(tempMap);
					}
				}
			}

			Iterator<BigDecimal> iter = orgRadioMap.keySet().iterator();
			while (iter.hasNext()) {
				BigDecimal key = iter.next();
				boolean isDel = true;
				for (CsDeptAssignRuleConfig csDeptAssignRuleConfig : listCsDeptAssignRuleConfig) {
					if (csDeptAssignRuleConfig.getId().equals(key)) {
						isDel = false;
						break;
					}
				}
				if (isDel) {
					iter.remove();
				}
			}

			iter = orgRadioUseMap.keySet().iterator();
			while (iter.hasNext()) {
				BigDecimal key = iter.next();
				boolean isDel = true;
				for (CsDeptAssignRuleConfig csDeptAssignRuleConfig : listCsDeptAssignRuleConfig) {
					if (csDeptAssignRuleConfig.getId().equals(key)) {
						isDel = false;
						break;
					}
				}
				if (isDel) {
					iter.remove();
				}
			}
		}

		List<Map<String, BigDecimal>> listMap = csAssignedCustConfigMsgDao.selOrgUsed();
		if (CollectionUtils.isNotEmpty(listMap)) {
			for (Map<String, BigDecimal> map : listMap) {
				orgUsedMap.put(map.get("ORGCONFIGID"), map.get("USEDCOUNT"));
			}
		}
		printInfo();
	}
	
	/**
	 * 测试用
	 */
	private void printInfo() {
		/*log.info("============================其它（省市）部门额度总数  start==========================");
		Iterator<Entry<String, LinkedList<CsDeptAssignRuleOrgConfig>>> init_Iter = qtProOrCityMap.entrySet().iterator();
		while (init_Iter.hasNext()) {
			Entry<String, LinkedList<CsDeptAssignRuleOrgConfig>> entry = init_Iter.next();
			Object key = entry.getKey();
			LinkedList<CsDeptAssignRuleOrgConfig> listCsDeptAssignRuleOrgConfig = entry.getValue();
			log.info("其它部门key = " + key + " ,val = " + (CollectionUtils.isEmpty(listCsDeptAssignRuleOrgConfig) ? "" : JSON.toJSONString(listCsDeptAssignRuleOrgConfig)));
		}

		log.info("============================部门额度总数  start==========================");
		if(CollectionUtils.isNotEmpty(normalProOrCityList)){
			for(HashMap<String, LinkedList<CsDeptAssignRuleOrgConfig>>  map : normalProOrCityList){
				init_Iter = map.entrySet().iterator();
				while (init_Iter.hasNext()) {
					Entry<String, LinkedList<CsDeptAssignRuleOrgConfig>> entry = init_Iter.next();
					Object key = entry.getKey();
					LinkedList<CsDeptAssignRuleOrgConfig> listCsDeptAssignRuleOrgConfig = entry.getValue();
					log.info("非其他： 省市key = " + key + " ,val = " + (CollectionUtils.isEmpty(listCsDeptAssignRuleOrgConfig) ? "" : JSON.toJSONString(listCsDeptAssignRuleOrgConfig)));
				}
			}
		}else{
			log.info("部门额度List为空！");
		}
		
		log.info("============================标签额度总数  start==========================");
		if(CollectionUtils.isNotEmpty(normalLabelList)){
			for(HashMap<String, LinkedList<CsDeptAssignRuleOrgConfig>>  map : normalLabelList){
				init_Iter = map.entrySet().iterator();
				while (init_Iter.hasNext()) {
					Entry<String, LinkedList<CsDeptAssignRuleOrgConfig>> entry = init_Iter.next();
					Object key = entry.getKey();
					LinkedList<CsDeptAssignRuleOrgConfig> listCsDeptAssignRuleOrgConfig = entry.getValue();
					log.info("非其他： 标签key = " + key + " ,val = " + (CollectionUtils.isEmpty(listCsDeptAssignRuleOrgConfig) ? "" : JSON.toJSONString(listCsDeptAssignRuleOrgConfig)));
				}
			}
		}else{
			log.info("标签额度List为空！");
		}*/
		
		log.info("其它（省市）部门额度： " + (qtProOrCityMap == null ? "" : JSON.toJSONString(qtProOrCityMap)));
		log.info("部门额度： " + (normalProOrCityList == null ? "" : JSON.toJSONString(normalProOrCityList)));
		log.info("标签额度： " + (normalLabelList == null ? "" : JSON.toJSONString(normalLabelList)));
		
		
		log.info("省市部门配置比例： " + (orgRadioMap == null ? "" : JSON.toJSONString(orgRadioMap)));
		log.info("省市部门分配数据： " + (orgRadioUseMap == null ? "" : JSON.toJSONString(orgRadioUseMap)));
		log.info("省市部门使用个数： " + (orgUsedMap == null ? "" : JSON.toJSONString(orgUsedMap)));
		
	}
	
	
	/**
	 * 发送短信
	 */
	private void sendSmsInfoToRolePsLeader(String orgName,String smsTemplet) {
		Map<String, String> parmaMap = new HashMap<>();
		parmaMap.put("deptName", orgName);
		
		String result = sendSmsService.sendSms(mobilesRolePsLeader, StaticVar.TP_COMMON, Integer.parseInt(smsTemplet), parmaMap);
		if (result != null) {
			String[] results = result.split("\\|");
			if ("0000".equals(results[0])) {
				log.info("短信发送成功！ mobilesRolePsLeader :{} " , JSONObject.toJSONString(mobilesRolePsLeader) );
			} else {
				log.error("短信发送失败！ mobilesRolePsLeader :{} " + JSONObject.toJSONString(mobilesRolePsLeader));
			}
		}
	}
	

	/**
	 * 给自动分配的客户和投顾发送邮件或者短信通知
	 */
	public void do_sendMsg() {
		List<SendMsgInfo> sendMsgInfos = sendMsgDao.getNeedSendInfo();
		if (sendMsgInfos.isEmpty()) {
			log.info("没有信息可以发送");
			return;
		}
		for (SendMsgInfo tempSendMsgInfo : sendMsgInfos) {
			try {
				dealSendMsg(tempSendMsgInfo);
			} catch (Exception e) {
				log.error("给自动分配的客户和投顾发送邮件或者短信通知，出现异常异常！，autodistribute_log表 id:{}", tempSendMsgInfo.getId(), e);
			} finally {
				// 更新 消息推送 的处理状态为：已处理
				sendMsgDao.updateDealPushMsgFlag(tempSendMsgInfo.getId());
			}

		}
	}


	/**
	 * @description:(请在此添加描述)
	 * @param tempSendMsgInfo
	 * @return void
	 * @author: jin.wang03
	 * @date: 2025/6/24 14:12
	 * @since JDK 1.8
	 */
	private void dealSendMsg(SendMsgInfo tempSendMsgInfo) {
		long id = tempSendMsgInfo.getId();
		String advisorName = tempSendMsgInfo.getAdvisorName();
		String advisorMobile = tempSendMsgInfo.getAdvisorMobile();
		String email = tempSendMsgInfo.getEmail();
		String custno = tempSendMsgInfo.getCustno();
		String custname = tempSendMsgInfo.getCustname();

		int aEmailFlag = tempSendMsgInfo.getaEmailFlag();
		int aSmsFlag = tempSendMsgInfo.getaSmsFlag();
		int custSmsFlag = tempSendMsgInfo.getCustSmsFlag();

		// 投顾客户号为空，则跳过
		if (StringUtils.isBlank(custno)) {
			return;
		}

		String custMobileCipher = tempSendMsgInfo.getCustMobileCipher();
		String custMobile = null;
		if (StringUtils.isNotBlank(custMobileCipher)) {
			custMobile = decryptSingleFacade.decrypt(custMobileCipher).getCodecText();
		}
		String advisorTelno = tempSendMsgInfo.getAdvisorTelno();
		String genderInfo = tempSendMsgInfo.getGenderInfo();

		// 处理香港渠道推送逻辑
		if (CmsDisCodeEnum.HK_CHANNEL.getCode().equals(tempSendMsgInfo.getDisCode())) {
			Map<String, String> paramMap = Maps.newHashMap();
			paramMap.put("custname", tempSendMsgInfo.getCustname());
			paramMap.put("custno", tempSendMsgInfo.getCustno());
			// 邮箱解密
			CodecSingleResponse emailResponse = decryptSingleFacade.decrypt(tempSendMsgInfo.getCustEmailCipher());
			String emailAddr = StringUtil.isEmpty(tempSendMsgInfo.getCustEmailCipher()) ? "暂无" : emailResponse.getCodecText();
			paramMap.put("Email", emailAddr);
			// 手机号解密
			CodecSingleResponse mobileResponse = decryptSingleFacade.decrypt(tempSendMsgInfo.getCustMobileCipher());
			String mobile = StringUtil.isEmpty(tempSendMsgInfo.getMobileAreaCode()) ? mobileResponse.getCodecText() : StringUtils.join(tempSendMsgInfo.getMobileAreaCode(), "-", mobileResponse.getCodecText());
			paramMap.put("custMobile", mobile);
			paramMap.put("noticeContent", tempSendMsgInfo.getBookingContent());
			sendConsQyWechat(id, tempSendMsgInfo.getAdvisorId(), paramMap);
			return;
		}

		// 咨询内容
		String contentStr = sendMsgDao.getCustContent(custno);
		// 给投顾发送邮件
		// 发送投顾邮件模板：请于今天尽快联系该客户，称呼：<#if custName?? &
		// custName!=''>${custName}</#if>，性别：<#if genderInfo?? &
		// genderInfo!=''>${genderInfo}</#if>，
		// 联系方式：${custMobile}，咨询内容：${noticeContent} ，谢谢！
		if (aEmailFlag == 0 && custMobile != null && CommonUtil.checkEmail(email)) {
			Map<String, String> emailMap = new HashMap<>();
			emailMap.put("custName", custname);
			emailMap.put("genderInfo", genderInfo);
			emailMap.put("custMobile", custMobile);
			emailMap.put("noticeContent", contentStr);

			// int templateId = Integer.valueOf(PropertyManager.getProperty("CS.CS_CONS_EMAIL_ACTIONID"));
			sendConsEmail(id, email, 384, emailMap);
		}

		// 给投顾发送短信
		// 发送投顾短信模板：称呼：<#if custName?? & custName!=''>${custName}</#if>，性别：<#if
		// genderInfo?? & genderInfo!=''>${genderInfo}</#if>，
		// 联系方式：${custMobile}，咨询内容：${noticeContent} ，请速联系，谢谢！
		if (aSmsFlag == 0 && custMobile != null && CommonUtil.isMobile(advisorMobile)) {
			Map<String, String> parmaMap = new HashMap<>();
			parmaMap.put("custName", custname);
			parmaMap.put("genderInfo", genderInfo);
			parmaMap.put("conscustNo", custno);
			parmaMap.put("noticeContent", contentStr);

			int templateId = Integer.parseInt(consSmsActionId);
			sendConsSmsInfo(id, parmaMap, advisorMobile, templateId, SendFlag.AdvisorMobile.getColName());
		}

		// 给投顾发送站内信、企微消息，
		// 2025-03-19 需求地址：http://dms.intelnal.howbuy.com/pages/viewpage.action?pageId=91130583 【A0233】leads分配推送消息提醒
		// 消息模版：请于今天尽快联系该客户，客户：${custName}，投顾客户号：${custNo}，性别：${genderInfo}，
		// 联系方式：${custMobile}，咨询内容：${commcontent} ，请投顾联系，谢谢!
		if (StringUtils.isNotBlank(tempSendMsgInfo.getAdvisorId()) && custMobile != null) {
			Map<String, String> pushMsgMap = new HashMap<>();
			pushMsgMap.put("custName", custname);
			pushMsgMap.put("custNo", custno);
			pushMsgMap.put("genderInfo", genderInfo);
			pushMsgMap.put("custMobile", custMobile);
			pushMsgMap.put("commcontent", contentStr);
			log.info("leads分配推送消息提醒==推送给投顾的消息内容：{}", JSON.toJSONString(pushMsgMap));
			cmPushMsgService.pushMsgByConsCodeList(LEADS_TRANSFER_MSG_TEMPLATE_ID, Lists.newArrayList(tempSendMsgInfo.getAdvisorId()), pushMsgMap);
		}


		// 给客户发送短信
		if (custSmsFlag == 0 && CommonUtil.isMobile(custMobile) && advisorMobile != null && advisorTelno != null) {
			// 如果投顾是“CS待处理库”，则不给客户发短信
			if ("CS待处理库".equals(advisorName)) {
				return;
			}

			Map<String, String> parmaMap = new HashMap<>();
			parmaMap.put("custName", custname);
			parmaMap.put("consName", advisorName);

			int templateId = Integer.parseInt(custSmsActionId);
			sendConsSmsInfo(id, parmaMap, custMobile, templateId, SendFlag.CustMobile.getColName());
		}
	}

	/**
	 * 给投顾发送短信
	 */
	private void sendConsSmsInfo(long logId, Map<String, String> parmaMap, String mobile, int templateId, String colName) {
		try {
			String result = sendSmsService.sendSms(new String[] { mobile }, StaticVar.TP_COMMON, templateId, parmaMap);
			if (result != null) {
				String[] results = result.split("\\|");
				int sendSuccFlag;
				String remark = results[1];
				if ("0000".equals(results[0])) {
					log.info("logId为：" + logId + "，手机号为：" + mobile + "，短信发送成功！ ");
					sendSuccFlag = StaticVar.SUCCESS;
				} else {
					log.info("logId为：" + logId + "，手机号为：" + mobile + "，短信发送失败！ ");
					sendSuccFlag = StaticVar.ERROR;
				}
				
				// 修改短信发送标识
				Map<String, Object> param = new HashMap<String, Object>();
				param.put("logId", logId);
				param.put("sendSuccFlag", sendSuccFlag);
				param.put("colName", colName);
				log.info("sendSuccFlag：" + sendSuccFlag);
				boolean updateAutoLogFlag = sendMsgDao.updateSendSmsFlag(param) > 0;
				if (updateAutoLogFlag) {
					log.info("短信发送标识更新成功");
				}

				// 记录发送日志
				CsSendMsgLog csSendMsgLog = new CsSendMsgLog();
				csSendMsgLog.setReceiver(mobile);
				csSendMsgLog.setSendFlag(sendSuccFlag);
				csSendMsgLog.setSendType(SendType.Mobile.getSendType());
				csSendMsgLog.setRemark(remark);
				boolean addSendLogFlag = sendMsgDao.addSendLog(csSendMsgLog) > 0;
				if (addSendLogFlag) {
					log.info("短信发送信息已记录到日志表");
				}
			}

		} catch (Exception ex) {
			log.error("给投顾发送短信失败，失败原因：" + ex.getMessage(), ex);
		}

	}
	
	/**
	 * 给投顾发送邮件
	 */
	private void sendConsEmail(long logId, String addToUserAddress, int actionId, Map<String, String> emailMap) {
		try {
			String result = sendSmsService.sendEmail(new String[] { addToUserAddress }, actionId, emailMap);
			if (result != null) {
				String[] results = result.split("\\|");
				int sendSuccFlag;
				String remark = results[1];
				if ("0000".equals(results[0])) {
					log.info("logId为：" + logId + "，邮件为：" + addToUserAddress + "，邮件发送成功！ ");
					sendSuccFlag = StaticVar.SUCCESS;
				} else {
					log.info("logId为：" + logId + "，邮件为：" + addToUserAddress + "，邮件发送失败！ ");
					sendSuccFlag = StaticVar.ERROR;
				}

				// 修改邮件发送标识
				Map<String, Object> param = new HashMap<>();
				param.put("logId", logId);
				param.put("sendSuccFlag", sendSuccFlag);
				param.put("colName", SendFlag.AdvisorEmail.getColName());
				log.info("sendSuccFlag：" + sendSuccFlag);
				boolean updateAutoLogFlag = sendMsgDao.updateSendEmailFlag(param) > 0;
				if (updateAutoLogFlag) {
					log.info("邮件发送标识更新成功");
				}

				// 记录发送日志
				CsSendMsgLog csSendMsgLog = new CsSendMsgLog();
				csSendMsgLog.setReceiver(addToUserAddress);
				csSendMsgLog.setSendFlag(sendSuccFlag);
				csSendMsgLog.setSendType(SendType.Email.getSendType());
				csSendMsgLog.setRemark(remark);
				boolean addSendLogFlag = sendMsgDao.addSendLog(csSendMsgLog) > 0;
				if (addSendLogFlag) {
					log.info("邮件发送信息已记录到日志表");
				}
			}
		} catch (Exception ex) {
			log.error("给投顾发送邮件失败，失败原因：" + ex.getMessage(),ex);
		}

	}

	/**
	 * 给投顾发送企微消息 & 站内信
	 * 备注：历史发送短信&邮件分别记录发送成功标识，本次企微站内信不另加字段，使用已有推送标识字段
	 */
	private void sendConsQyWechat(long logId, String consCode, Map<String, String> paramsMap) {
		try {
			String result = sendSmsService.sendWechat(Constants.HK_MSG_TEMPLATE_ID_201695, Lists.newArrayList(consCode), paramsMap);
			log.info("给投顾发送企微站内信 sendConsQyWechat result={}, consCode={}", result, consCode);
			if (result != null) {
				String[] results = result.split("\\|");
				// 修改短信发送标识
				if ("0000".equals(results[0])) {
					Map<String, Object> param = new HashMap<>();
					param.put("logId", logId);
					Integer updateSendSmsFlag = sendMsgDao.updateSendSmsFlag(param);
					Integer updateSendEmailFlag = sendMsgDao.updateSendEmailFlag(param);
					log.info("sendConsQyWechat logId={}, 更新企微站内信推送标识状态 updateSendSmsFlag={}, updateSendEmailFlag={}, consCode={}", logId, updateSendSmsFlag, updateSendEmailFlag, consCode);
				}
			}
		} catch (Exception ex) {
			log.error("给投顾发送企微站内信失败，失败原因：" + ex.getMessage(), ex);
		}
	}

	/**
	 * 用于存放对应部门比例的使用缓存 key
	 */
	private static final String ORGRADIOUSEMAPKEY = "ORGRADIOUSEMAPKEY";
	/**
	 * 用于存放对应部门比例的缓存 key
	 */
	private static final String ORGRADIOMAPKEY = "ORGRADIOMAPKEY";
	/**
	 * 用于存放投顾使用的缓存 key
	 */
	private static final String USEDCODELISTKEY = "USEDCODELISTKEY";
	/**
	 * 用于存放额度已满的投顾信息的缓存 key
	 */
	private static final String OVERQUOTACONFIGMAPKEY = "OVERQUOTACONFIGMAPKEY";

	/**
	 * @description: 初始化缓存数据(后面考虑重构，放缓存也不合适)
	 * @return void
	 * @author: hongdong.xie
	 * @date: 2023/11/17 13:17
	 * @since JDK 1.8
	 */
	private void initUseDataFromCache(){
		orgRadioUseMap = CacheServiceImpl.getInstance().get(CacheKeyPrefix.LOCK_KEY_PREFIX+ORGRADIOUSEMAPKEY);
		orgRadioMap = CacheServiceImpl.getInstance().get(CacheKeyPrefix.LOCK_KEY_PREFIX+ORGRADIOMAPKEY);
		usedCodeList = CacheServiceImpl.getInstance().get(CacheKeyPrefix.LOCK_KEY_PREFIX+USEDCODELISTKEY);
		overQuotaConfigMap = CacheServiceImpl.getInstance().get(CacheKeyPrefix.LOCK_KEY_PREFIX+OVERQUOTACONFIGMAPKEY);
		log.info("initUseDataFromCache orgRadioUseMap:{}",orgRadioUseMap);
		log.info("initUseDataFromCache orgRadioMap:{}",orgRadioMap);
		log.info("initUseDataFromCache usedCodeList:{}",usedCodeList);
		log.info("initUseDataFromCache overQuotaConfigMap:{}",overQuotaConfigMap);
	}

	/**
	 * @description: 将 leads 是用分配的数据放入缓存（后面考虑重构）
	 * @return void
	 * @author: hongdong.xie
	 * @date: 2023/11/17 13:18
	 * @since JDK 1.8
	 */
	private void saveUseDataToCache(){
		CacheServiceImpl.getInstance().put(CacheKeyPrefix.LOCK_KEY_PREFIX+ORGRADIOUSEMAPKEY, orgRadioUseMap);
		CacheServiceImpl.getInstance().put(CacheKeyPrefix.LOCK_KEY_PREFIX+ORGRADIOMAPKEY, orgRadioMap);
		CacheServiceImpl.getInstance().put(CacheKeyPrefix.LOCK_KEY_PREFIX+USEDCODELISTKEY, usedCodeList);
		CacheServiceImpl.getInstance().put(CacheKeyPrefix.LOCK_KEY_PREFIX+OVERQUOTACONFIGMAPKEY, overQuotaConfigMap);
		log.info("saveUseDataToCache orgRadioUseMap:{}",orgRadioUseMap);
		log.info("saveUseDataToCache orgRadioMap:{}",orgRadioMap);
		log.info("saveUseDataToCache usedCodeList:{}",usedCodeList);
		log.info("saveUseDataToCache overQuotaConfigMap:{}",overQuotaConfigMap);
	}
	
}