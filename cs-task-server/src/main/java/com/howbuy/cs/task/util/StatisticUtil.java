package com.howbuy.cs.task.util;

import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

public class StatisticUtil {

    private static final String SEP = ",";// 分隔符常量

    /**
     * List转换String
     *
     * @param list:需要转换的List
     * @return String转换后的字符串
     */
    public static String listToString(List<?> list) {
        String result = "";
        if (list == null ) {
            return result;
        }
        StringBuilder sb = new StringBuilder();
        if (list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i) == null || list.get(i) == "") {
                    continue;
                }

                // 如果值是list类型则调用自己
                if (list.get(i) instanceof List) {
                    sb.append("'").append(listToString((List<?>) list.get(i))).append("'");
                    sb.append(SEP);
                } else if (list.get(i) instanceof Map) {
                    sb.append("'").append(mapToString((Map<?, ?>) list.get(i))).append("'");
                    sb.append(SEP);
                } else {
                    sb.append("'").append(list.get(i)).append("'");
                    sb.append(SEP);
                }
            }
        }

        if (StringUtils.isNotBlank(sb.toString()) && SEP.equals(sb.substring(sb.length() - 1))) {
            result = sb.substring(0, sb.length() - 1);
        }
        return result;
    }

    /**
     * Map转换String
     *
     * @param map:需要转换的Map
     * @return String转换后的字符串
     */
    public static String mapToString(Map<?, ?> map) {
        String result = "";

        if (map == null) {
            return result;
        }
        StringBuilder sb = new StringBuilder();
        // 遍历map
        for (Object obj : map.keySet()) {
            if (obj == null) {
                continue;
            }
            Object key = obj;
            Object value = map.get(key);
            if (value instanceof List<?>) {
                sb.append(listToString((List<?>) value));
                sb.append(SEP);
            } else if (value instanceof Map<?, ?>) {
                sb.append(mapToString((Map<?, ?>) value));
                sb.append(SEP);
            } else if (value instanceof String[]) {
                String[] args = (String[]) value;
                for (int i = 0; i < args.length; i++) {
                    sb.append("'").append(args[i]).append("'");
                    sb.append(SEP);
                }
            } else {
                sb.append(value.toString());
                sb.append(SEP);
            }
        }
        if (StringUtils.isNotBlank(sb.toString()) && SEP.equals(sb.substring(sb.length() - 1))) {
            result = sb.substring(0, sb.length() - 1);
        }
        return result;
    }

}