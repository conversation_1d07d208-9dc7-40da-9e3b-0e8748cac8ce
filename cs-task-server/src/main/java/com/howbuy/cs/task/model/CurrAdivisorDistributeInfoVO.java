package com.howbuy.cs.task.model;

import java.util.LinkedList;

public class CurrAdivisorDistributeInfoVO {
	private LinkedList<CsInvestAdvisorTarget> curr_AdvisorList; // 目前对应哪个投顾list
	private boolean topFlag;
	private boolean existDeptFlag; // 属于某部门的投顾是否还有
	private Custconstant currCustconstant; // 投顾客户关系对象
	private boolean sendSmsFlag; // 是否需要发送短信
	private String sendSmsOrgCode; // 需要发送短信通知管理员的部门编号
	private String currOrgCode; // 当前队列所属的部门编号
	private LinkedList<CsInvestAdvisorTarget> curr_Non_Type_AdvisorList; // 存放当然不符合类型的投顾list

	public CurrAdivisorDistributeInfoVO() {
		this.curr_AdvisorList = new LinkedList<CsInvestAdvisorTarget>();
		this.topFlag = false;
		this.existDeptFlag = false;
		this.currCustconstant = new Custconstant();
		this.sendSmsFlag = false;
		this.curr_Non_Type_AdvisorList = new LinkedList<CsInvestAdvisorTarget>();
	}

	public LinkedList<CsInvestAdvisorTarget> getCurr_AdvisorList() {
		return curr_AdvisorList;
	}

	public void setCurr_AdvisorList(LinkedList<CsInvestAdvisorTarget> curr_AdvisorList) {
		this.curr_AdvisorList = curr_AdvisorList;
	}

	public boolean isTopFlag() {
		return topFlag;
	}

	public void setTopFlag(boolean topFlag) {
		this.topFlag = topFlag;
	}

	public boolean isExistDeptFlag() {
		return existDeptFlag;
	}

	public void setExistDeptFlag(boolean existDeptFlag) {
		this.existDeptFlag = existDeptFlag;
	}

	public Custconstant getCurrCustconstant() {
		return currCustconstant;
	}

	public void setCurr_Custconstant(Custconstant currCustconstant) {
		this.currCustconstant = currCustconstant;
	}

	public boolean isSendSmsFlag() {
		return sendSmsFlag;
	}

	public void setSendSmsFlag(boolean sendSmsFlag) {
		this.sendSmsFlag = sendSmsFlag;
	}

	public String getSendSmsOrgCode() {
		return sendSmsOrgCode;
	}

	public void setSendSmsOrgCode(String sendSmsOrgCode) {
		this.sendSmsOrgCode = sendSmsOrgCode;
	}

	public String getCurrOrgCode() {
		return currOrgCode;
	}

	public void setCurrOrgCode(String currOrgCode) {
		this.currOrgCode = currOrgCode;
	}

	public LinkedList<CsInvestAdvisorTarget> getCurr_Non_Type_AdvisorList() {
		return curr_Non_Type_AdvisorList;
	}

	public void setCurr_Non_Type_AdvisorList(LinkedList<CsInvestAdvisorTarget> curr_Non_Type_AdvisorList) {
		this.curr_Non_Type_AdvisorList = curr_Non_Type_AdvisorList;
	}

}
