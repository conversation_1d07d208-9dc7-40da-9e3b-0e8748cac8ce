package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.TpFundVolRec;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface TpFundVolRecMapper {
    int deleteByPrimaryKey(String recordNo);

    int insert(TpFundVolRec record);

    int insertSelective(TpFundVolRec record);

    TpFundVolRec selectByPrimaryKey(String recordNo);

    int updateByPrimaryKeySelective(TpFundVolRec record);

    int updateByPrimaryKey(TpFundVolRec record);

    int batchInsert(@Param("list") List<TpFundVolRec> list);

    int deleteByFundCode(@Param("fundCode") String fundCode);
}