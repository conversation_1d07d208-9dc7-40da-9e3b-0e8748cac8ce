/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.service;

import com.howbuy.common.util.DateUtils;
import com.howbuy.cs.task.dao.SyncBpFundBasicInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/12/23 15:58
 * @since JDK 1.8
 */
@Service("syncBpFundBasicInfoServiceImpl")
public class SyncBpFundBasicInfoServiceImpl {
    @Autowired
    public SyncBpFundBasicInfoMapper syncBpFundBasicInfoMapper;

    /**
     * @description: 从基金信息表同步基金信息
     * @return int
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class)
    public int syncFundInfoFromJJXX1() {
        String createDate = DateUtils.currentDate();
        return syncBpFundBasicInfoMapper.insertFromJJXX1(createDate);
    }
}