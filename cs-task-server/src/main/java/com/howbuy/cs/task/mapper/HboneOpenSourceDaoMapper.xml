<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.cs.task.dao.HboneOpenSourceDao">
	<select id="getHboneOpenSource"	resultType="com.howbuy.cs.task.model.HboneOpenSource">
		select t1.hbone_no,
		       t1.reg_outlet_code,
		       t1.provcode,
		       t1.citycode,
		       t2.conscustno,
		       t2.invsttype
		  from hbone_open_source t1
		  left join cm_conscust t2
		    on t2.hbone_no = t1.hbone_no
		 where t1.deal_status = 2
		   and t1.reg_outlet_code is not null
		   and t2.conscuststatus = '0'
		   and t2.invsttype is not null
	</select>

	<update id="updateCustconstant" parameterType="com.howbuy.cs.task.model.HboneOpenSource" >
    	update cm_custconstant
		   set conscode = #{consCode, jdbcType = VARCHAR},
		       startdt  = to_char(sysdate, 'yyyymmdd'),
			   modifier = #{modifier},
			   moddt = to_char(sysdate,'yyyymmdd'),
			   beforehisid = #{beforehisid},
		       binddate = sysdate,
			   operate_date = sysdate
		 where custno = #{conscustNo, jdbcType = VARCHAR}
		   and recstat = '1'
	</update>

	<insert id="insertCustconstantHis" useGeneratedKeys="false">
		insert into cm_custconstanthis
			(custconshisid, custno, conscode, startdt, enddt, creator,
			 credt, binddate, unbunddate, nextcons, reason,
			 beforehisid,operate_date)
		select nvl(#{custconshisid, jdbcType = VARCHAR},'') as custconshisid,
			   t.custno as custno,
			   t.conscode as conscode,
			   t.startdt as startdt,
			   to_char(sysdate, 'yyyymmdd') as enddt,
			   nvl(#{creator, jdbcType = VARCHAR},'') as creator,
			   to_char(sysdate, 'yyyymmdd') as credt,
			   t.binddate,
			   sysdate as unbunddate,
			   nvl(#{nextcons, jdbcType = VARCHAR},'') as nextcons,
			   nvl(#{reason, jdbcType = VARCHAR},'') as reason,
			   t.beforehisid,
			   sysdate as operate_date
		from cm_custconstant t
		where t.custno = #{custno, jdbcType = VARCHAR}
	</insert>

	<select id="getOldConscode" resultType="String">
		select conscode from cm_custconstant
		where custno = #{conscustNo, jdbcType = VARCHAR}
	</select>

	<update id="updateHboneOpenSource" parameterType="com.howbuy.cs.task.model.HboneOpenSource">
		update hbone_open_source t set t.deal_status = 1 where t.hbone_no=#{hboneNo, jdbcType=NUMERIC}
	</update>

</mapper>