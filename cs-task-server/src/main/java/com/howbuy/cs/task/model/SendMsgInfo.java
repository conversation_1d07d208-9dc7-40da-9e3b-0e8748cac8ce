package com.howbuy.cs.task.model;

public class SendMsgInfo {

	private long id;
	private String advisorId;
	private String advisorName;
	private String advisorMobile;
	private String email;
	private String advisorTelno;
	private String orgcode;
	private String custno;
	private String custname;
	private int aEmailFlag;
	private int aSmsFlag;
	private int custSmsFlag;
	private int custSendFlag;
	private String custMobile;
	private String mobileAreaCode;
	private String custMobileCipher;
	private String custEmailCipher;
	private String genderInfo;
	private String bookingContent;
	private String disCode;
	
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public String getAdvisorId() {
		return advisorId;
	}
	public void setAdvisorId(String advisorId) {
		this.advisorId = advisorId;
	}
	public String getAdvisorName() {
		return advisorName;
	}
	public void setAdvisorName(String advisorName) {
		this.advisorName = advisorName;
	}
	public String getAdvisorMobile() {
		return advisorMobile;
	}
	public void setAdvisorMobile(String advisorMobile) {
		this.advisorMobile = advisorMobile;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getOrgcode() {
		return orgcode;
	}
	public void setOrgcode(String orgcode) {
		this.orgcode = orgcode;
	}
	public String getCustno() {
		return custno;
	}
	public void setCustno(String custno) {
		this.custno = custno;
	}
	public String getCustname() {
		return custname;
	}
	public void setCustname(String custname) {
		this.custname = custname;
	}
	public int getaEmailFlag() {
		return aEmailFlag;
	}
	public void setaEmailFlag(int aEmailFlag) {
		this.aEmailFlag = aEmailFlag;
	}
	public int getaSmsFlag() {
		return aSmsFlag;
	}
	public void setaSmsFlag(int aSmsFlag) {
		this.aSmsFlag = aSmsFlag;
	}
	public int getCustSmsFlag() {
		return custSmsFlag;
	}
	public void setCustSmsFlag(int custSmsFlag) {
		this.custSmsFlag = custSmsFlag;
	}
	public int getCustSendFlag() {
		return custSendFlag;
	}
	public void setCustSendFlag(int custSendFlag) {
		this.custSendFlag = custSendFlag;
	}
	public String getCustMobile() {
		return custMobile;
	}
	public void setCustMobile(String custMobile) {
		this.custMobile = custMobile;
	}
	public String getCustMobileCipher() {
		return custMobileCipher;
	}
	public void setCustMobileCipher(String custMobileCipher) {
		this.custMobileCipher = custMobileCipher;
	}
	public String getAdvisorTelno() {
		return advisorTelno;
	}
	public void setAdvisorTelno(String advisorTelno) {
		this.advisorTelno = advisorTelno;
	}
	public String getGenderInfo() {
		return genderInfo;
	}
	public void setGenderInfo(String genderInfo) {
		this.genderInfo = genderInfo;
	}

	public String getMobileAreaCode() {
		return mobileAreaCode;
	}

	public void setMobileAreaCode(String mobileAreaCode) {
		this.mobileAreaCode = mobileAreaCode;
	}

	public String getBookingContent() {
		return bookingContent;
	}

	public void setBookingContent(String bookingContent) {
		this.bookingContent = bookingContent;
	}

	public String getCustEmailCipher() {
		return custEmailCipher;
	}

	public void setCustEmailCipher(String custEmailCipher) {
		this.custEmailCipher = custEmailCipher;
	}

	public String getDisCode() {
		return disCode;
	}

	public void setDisCode(String disCode) {
		this.disCode = disCode;
	}
}
