/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.service;

import com.github.pagehelper.PageHelper;
import com.howbuy.cs.task.dao.CmConsultantChangeorgRecMapper;
import com.howbuy.cs.task.dao.CmConsultantExpMapper;
import com.howbuy.cs.task.dao.CmConsultantMapper;
import com.howbuy.cs.task.dao.CustConstantDao;
import com.howbuy.cs.task.model.CmConsultant;
import com.howbuy.cs.task.model.CmConsultantChangeorgRec;
import com.howbuy.cs.task.model.CmConsultantExp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description:  投顾管理层变更记录表服务实现类
 * <AUTHOR>
 * @date 2024/11/25 16:49
 * @since JDK 1.8
 */
@Slf4j
@Service("cmConsultantChangeorgRecService")
public class CmConsultantChangeorgRecServiceImpl {

    @Autowired
    private CustConstantDao custConstantDao;

    @Autowired
    private CmConsultantChangeorgRecMapper cmConsultantChangeorgRecMapper;

    @Autowired
    private CmConsultantMapper cmConsultantMapper;

    @Autowired
    private CmConsultantExpMapper cmConsultantExpMapper;

    /**
     * @description: 查询所有的投顾code
     * @param pageNo 页码
     * @param pageSize	 每页大小
     * @return java.util.List<java.lang.String>
     * @author: hongdong.xie
     * @date: 2024/11/25 17:15
     * @since JDK 1.8
     */
    public List<String> getAllConsCode(int pageNo, int pageSize) {
        PageHelper.startPage(pageNo, pageSize);
        return custConstantDao.selectAllConsCode();
    }

    /**
     * @description: 根据投顾code查询每个管理层级的最新的变更记录
     * @param consCode 投顾code
     * @return java.util.Map<java.lang.String,com.howbuy.cs.task.model.CmConsultantChangeorgRec>
     * @author: hongdong.xie
     * @date: 2024/11/25 17:29
     * @since JDK 1.8
     */
    public Map<String,CmConsultantChangeorgRec> getNewstByConsCodeAndLevel(String consCode) {
        List<CmConsultantChangeorgRec> list = cmConsultantChangeorgRecMapper.selectNewstByConsCodeAndLevel(consCode);
        if(CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(CmConsultantChangeorgRec::getManagementLevel, cmConsultantChangeorgRec -> cmConsultantChangeorgRec));
    }

    /**
     * @description: 批量插入或更新
     * @param addList 添加列表
     * @param updateList 更新列表
     * @return void
     * @author: hongdong.xie
     * @date: 2024/11/25 18:17
     * @since JDK 1.8
     */
    public void insertOrUpdate(List<CmConsultantChangeorgRec> addList, List<CmConsultantChangeorgRec> updateList) {
        addList.forEach(cmConsultantChangeorgRec -> cmConsultantChangeorgRecMapper.insertSelective(cmConsultantChangeorgRec));
        updateList.forEach(cmConsultantChangeorgRec -> cmConsultantChangeorgRecMapper.updateByPrimaryKeySelective(cmConsultantChangeorgRec));
    }

    /**
     * @description: 根据投顾code查询投顾信息
     * @param consCode 投顾code
     * @return com.howbuy.cs.task.model.CmConsultant
     * @author: hongdong.xie
     * @date: 2024/11/25 19:04
     * @since JDK 1.8
     */
    public CmConsultant getConsultantByConsCode(String consCode) {
        return cmConsultantMapper.selectByConsCode(consCode);
    }

    /**
     * @description: 根据userId查询投顾信息
     * @param userId 用户id，即投顾code
     * @return com.howbuy.cs.task.model.CmConsultantExp
     * @author: hongdong.xie
     * @date: 2024/12/23 15:14
     * @since JDK 1.8
     */
    public CmConsultantExp getCmConsultantExpByUserId(String userId) {
        return cmConsultantExpMapper.selectByPrimaryKey(userId);
    }
}