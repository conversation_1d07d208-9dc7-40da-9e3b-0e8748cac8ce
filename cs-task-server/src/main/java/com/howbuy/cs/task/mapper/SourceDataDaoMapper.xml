<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.cs.task.dao.SourceDataDao">

    <select id="getBookingCustData" resultType="com.howbuy.cs.task.model.BookingCust">
        select *
        from (select rownum r, f.*
              from (select
                        ID as id,
                        CUSTNAME as custName,
                        ACTIVITYTYPE as activityType,
                        MOBILE_DIGEST as mobileDigest,
                        MOBILE_MASK as mobileMask,
                        MOBILE_CIPHER as mobileCipher,
                        EMAIL_DIGEST as emailDigest,
                        EMAIL_MASK as emailMask,
                        EMAIL_CIPHER as emailCipher,
                        BOOKINGDT as bookingDt,
                        BOOKINGCONTENT as bookingContent,
                        BOOKINGSERIALNO as bookingSerialNo,
                        BOOKINGDETAILDT as bookingDetailDt,
                        DISCODE as disCode,
                        MOBILE_AREA_CODE as mobileAreaCode,
                        SOURCE_SYS as sourceSys
                    from cm_bookingcust
                    where mobile_digest is not null
                          and (readflag = 0 or readflag is null)
                          and HANDLESTAT = '0'
                        order by BOOKINGDETAILDT desc) f
              where rownum <![CDATA[ <= ]]> 50 )
        where r > 0
    </select>

    <update id="updateBookCustReadFlag" parameterType="list" >
        update cm_bookingcust set readflag = 1
        where id in
        (
           <foreach item="item" index="index" collection="list" separator=",">
              #{item , jdbcType= NUMERIC}
           </foreach>
        )
    </update>

    <insert id="insertBatchAddSourceData" parameterType="list" useGeneratedKeys="false">
        insert into cs_callout_waitdistribute
        (
        waitid,
        taskid,
        task_type,
        waitdate,
        handle_state,
        distribute_flag,
        disposenum,
        sub_task_type,
        custname,
        source_dt,
        bookingcontent,
        cmsno,
        consultdt,
        mobile_mask,
        mobile_digest,
        mobile_cipher,
        email_mask,
        email_digest,
        email_cipher
        )
        select seq_cs_callout_waitdistribute.nextval ,A.* from (
        <foreach item="item" index="index" collection="list" separator="union all">
           (
           select #{item.taskId ,jdbcType=NUMERIC} as a1,
                  #{item.taskType ,jdbcType=NUMERIC} as a2,
                  sysdate as o1,
                  0 as o2,
                  0 as o3,
                  0 as o4,
                  #{item.subTaskType , jdbcType=NUMERIC } as a3,
                  #{item.custName , jdbcType=VARCHAR } as a4,
                  #{item.sourceDt , jdbcType=VARCHAR } as a7,
                  #{item.bookingContent , jdbcType=VARCHAR } as a8,
                  #{item.cmsNo , jdbcType=VARCHAR } as a9,
                  #{item.consultDt , jdbcType=VARCHAR } as a10,
                  #{item.mobileMask , jdbcType=VARCHAR } as a11,
                  #{item.mobileDigest , jdbcType=VARCHAR } as a12,
                  #{item.mobileCipher , jdbcType=VARCHAR } as a13,
                  #{item.emailMask , jdbcType=VARCHAR } as a14,
                  #{item.emailDigest , jdbcType=VARCHAR } as a15,
                  #{item.emailCipher , jdbcType=VARCHAR } as a16
             from dual
           )
         </foreach>
        )A
    </insert>

    <select id="getCallLossCustData" resultType="map">
         select id,
                to_char(calldate, 'yyyyMMdd hh24:mi:ss') calldate,
                calleeno,
                agentno,
                agentname,
                msg,
                readflag,
                losstype,
                dumpdate,
                keynum,
                callerno_mask,
                callerno_digest,
                callerno_cipher
           from cs_callloss_cust
          where rowid in (select rid
                            from (select rownum rn, rid
                                  from (select rowid rid, a.*
                                        from cs_callloss_cust a
                                        where readflag = 0
                                        order by a.calldate)
                                  where rownum <![CDATA[ <=]]> 50)
                            where rn <![CDATA[ > ]]> 0)
          order by calldate
    </select>

    <update id="updateCallLossReadFlag" parameterType="list" >
        update cs_callloss_cust set readflag = 1
        where id in (
        <foreach item="item" index="index" collection="list" separator=",">
            #{item , jdbcType= NUMERIC}
        </foreach>
        )
    </update>

    <!-- 获取云翌呼损数据方法 -->
    <select id="getYunEasyCustData" resultType="map">
		select id,
			   replace(start_time, '-', '') start_time,
			   readflag,
			   queuenum,
			   dtmf,
			   caller_num_mask,
			   caller_num_digest,
			   caller_num_cipher,
			   credt
		  from cs_cdr
		 where rowid in (select rid
						   from (select rownum rn, rid
								   from (select rowid rid, a.*
										   from cs_cdr a
										  where readflag = 0
										    and call_type = 1
                                            and agent_duration = 0
										  order by a.credt)
								  where rownum <![CDATA[ <= ]]> 50)
						  where rn <![CDATA[ > ]]> 0)
         order by credt
    </select>

    <!-- 修改云翌数据读取标识 -->
    <update id="updateYunEasyReadFlag" parameterType="list">
        update cs_cdr set readflag = 1
        where id in (
        <foreach item="item" index="index" collection="list" separator=",">
            #{item , jdbcType= NUMERIC}
        </foreach>
        )
    </update>

    <insert id="insertCmProCeDusLog" parameterType="com.howbuy.cs.task.model.CmProCedusLog">
        insert into cm_procedus_log
            (PONAME, POMSG)
        values (#{poName,jdbcType=VARCHAR}, #{poMsg,jdbcType=VARCHAR})
    </insert>
</mapper>