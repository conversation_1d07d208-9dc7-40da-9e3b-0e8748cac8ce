<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.cs.task.dao.AgentDao">

    <select id="getValidateAgent" resultType="com.howbuy.cs.task.model.CsTaskTargetSource">
        select conscode, remark as finalSource
          from cs_task_target_source a
         where exists
          (
            select 1
              from cs_sign b
             where to_char(signindate, 'yyyy-mm-dd') = to_char(sysdate, 'yyyy-mm-dd')
               and signoutflag = 0
               and a.conscode = b.conscode
               and b.remark = 'cs'
          )
          and a.rec_stat = 0
    </select>

    <select id="getValidateAgentByAssignRate" resultType="com.howbuy.cs.task.model.ValidateAgentVO">
        select b.conscode as conscode,
               nvl(a.assignCount, 0) as assignCount,
               b.targetCount as targetCount,
               (nvl(a.assignCount, 0) / b.targetCount) as assignRate
          from (
                select userid, count(userid) as assignCount
                  from cs_task_assign_day
                 group by userid
               ) a,
               (
                select conscode, target_count as targetCount
                  from cs_task_target
                 where rec_stat = 0
               ) b
         where a.userid(+) = b.conscode
           and exists
                (
                select 1
                  from cs_sign c
                 where to_char(signindate, 'yyyy-mm-dd') = to_char(sysdate, 'yyyy-mm-dd')
                   and signoutflag = 0
                   and c.conscode = b.conscode
                   and c.remark = 'cs'
                )
           and (nvl(a.assignCount, 0) / b.targetCount) != 1
         order by assignRate
    </select>

    <select id="getAllWaitDate" resultType="string">
        select t.waitdate
          from (
                select distinct to_char(waitdate, 'yyyy-mm-dd') as waitdate
                  from cs_callout_waitdistribute
                 where distribute_flag = 0
               ) t
         order by to_date(t.waitdate, 'yyyy-mm-dd')
    </select>

    <select id="getAllWaitCsCalloutWaitDistribute" parameterType="map"
            resultType="com.howbuy.cs.task.model.CsCalloutWaitDistribute">
        select waitid,
               taskid,
               task_type as taskType,
               waitdate,
               handle_state as handleState,
               distribute_flag as distributeFlag,
               disposenum,
               to_char(disposedate, 'yyyy-mm-dd hh24:mi:ss') as disposeDate,
               sub_task_type as subTaskType,
               orderFlag,
               orderUserId
          from cs_callout_waitdistribute
         where distribute_flag = 0
           and ((handle_state = 0) or
               (handle_state = 1 and to_char(disposedate, 'yyyy-mm-dd') = to_char(sysdate, 'yyyy-mm-dd')) and task_type != 1100)
           and to_char(waitdate, 'yyyy-mm-dd') = #{waitDate}
    </select>

    <select id="getValidDisposeMode" resultType="com.howbuy.cs.task.model.CsTaskAssignModel">
      select *
        from (select * from cs_task_assign_model
               where modetype = '0'
               order by modedate desc)
       where rownum <![CDATA[ <=]]> 1
    </select>

    <update id="updateDistributeFlag">
     update cs_callout_waitdistribute b
        set distribute_flag = 1
      where exists
      (select 1 from cs_task_assign_day a where a.waitid = b.waitid)
    </update>

    <insert id="insertCsTaskAssignDayBatch" parameterType="list" useGeneratedKeys="false">
        insert into cs_task_assign_day
        (
        taskid,
        waitid,
        userid,
        distribute_mode,
        distributedate,
        handle_flag,
        orderDate,
        handle_count,
        order_count,
        ihandle_flag
        )
        select seq_cs_task_assign_day.nextval, a.* from (
        <foreach item="item" index="index" collection="list" separator="union all">
            (
            select
            #{item.waitId, jdbcType=INTEGER} as a1,
            #{item.userId, jdbcType=VARCHAR} as a2,
            #{item.distributeMode, jdbcType=INTEGER } as a3,
            sysdate as a4,
            #{item.handleFlag, jdbcType=INTEGER} as a5,
            null as a6,
            0 as a7,
            0 as a8,
            #{item.handleFlag, jdbcType=INTEGER} as a9
            from dual
            )
        </foreach>
        ) a
    </insert>
</mapper>