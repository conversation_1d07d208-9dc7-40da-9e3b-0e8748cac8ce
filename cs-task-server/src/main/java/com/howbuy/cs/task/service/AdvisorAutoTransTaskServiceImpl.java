package com.howbuy.cs.task.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

import com.github.pagehelper.PageHelper;
import com.google.common.base.Throwables;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.facade.query.querycustinfo.QueryCustInfoFacade;
import com.howbuy.acccenter.facade.query.querycustinfo.QueryCustInfoRequest;
import com.howbuy.acccenter.facade.query.querycustinfo.QueryCustInfoResponse;
import com.howbuy.crm.conscust.dto.CmSourceInfoDomain;
import com.howbuy.crm.conscust.dto.CustconstantInfoDomain;
import com.howbuy.crm.conscust.request.QueryCmSourceInfoRequest;
import com.howbuy.crm.conscust.request.UpdateCustconstantInfoRequest;
import com.howbuy.crm.conscust.response.QueryCmSourceInfoResponse;
import com.howbuy.crm.conscust.service.QueryCmSourceInfoService;
import com.howbuy.crm.conscust.service.UpdateCustconstantInfoService;
import com.howbuy.cs.task.dao.TransOwnerDao;
import com.howbuy.cs.task.util.DateTimeUtil;
import com.howbuy.cs.task.util.StaticVar;
import org.springframework.transaction.annotation.Transactional;

@Service("advisorAutoTransTaskService")
public class AdvisorAutoTransTaskServiceImpl implements AdvisorAutoTransTaskService {
	private static final Logger log = LoggerFactory.getLogger(AdvisorAutoTransTaskServiceImpl.class);

	@Autowired
	private TransOwnerDao transOwnerDao;

	@Autowired
	private QueryCustInfoFacade queryCustInfoFacade;

	@Autowired
	private QueryCmSourceInfoService queryCmSourceInfoService;

	@Autowired
	private UpdateCustconstantInfoService updateCustconstantInfoService;

	@Autowired
	private AdvisorAutoTransTaskServiceImpl advisorAutoTransTaskService;

	@Autowired
	private ThreadPoolTaskExecutor transOwnerTask;

	//客户服务部-高端-无意向库
	private static final String V_NEWCONSCODE1 = "wuyixiang";
	//客户服务部-高端-错误信息
	private static final String V_NEWCONSCODE2 = "false";
	//客户服务部-零售-零售无意向库
	private static final String V_NEWCONSCODE3 = "lingshouwuyixiang";

	/**
	 * 客户投顾划转方法
	 */
	@Override
	public void autoTransOwnerTask(String arg) {
		log.info("客户投顾划转第一部分开始执行。。。");
		batchTransOwner();
		log.info("客户投顾划转第一部分执行结束。。。");
		System.out.println();
		
		log.info("客户投顾划转第二部分（无意向）开始执行。。。");
		List<Map<String, Object>> wyxList = transOwnerDao.getWuyixiangCustData();
		log.info("客户投顾划转第二部分（无意向）客户数：" + wyxList.size());
		if (wyxList.size() > 0) {
			for (int k = 0; k < wyxList.size(); k++) {
				Map<String, Object> tempDataMap = wyxList.get(k);
				if (tempDataMap.get("HBONENO") != null) {
					String conscustNo = tempDataMap.get("CONSCUSTNO").toString();
					String hboneNo = tempDataMap.get("HBONENO").toString();

					// 查询一账通接口判断手机验证状态
					QueryCustInfoRequest queryHboneInfo = new QueryCustInfoRequest();
					queryHboneInfo.setHboneNo(hboneNo);
					queryHboneInfo.setDisCode("HB000A001");
					try {
						QueryCustInfoResponse queryCustInfoResponse = queryCustInfoFacade.execute(queryHboneInfo);
						if (queryCustInfoResponse != null && "0000000".equals(queryCustInfoResponse.getReturnCode()) && queryCustInfoResponse.getCustInfoBean() !=null && StringUtils.isNotBlank(queryCustInfoResponse.getCustInfoBean().getRegOutletCode())) {
							log.info("账户中心接口返回状态："+queryCustInfoResponse.getReturnCode());
							log.info("账户中心接口返回注册网点号："+queryCustInfoResponse.getCustInfoBean().getRegOutletCode());
							log.info("账户中心接口返回用户类型："+queryCustInfoResponse.getCustInfoBean().getInvstType());
							log.info("账户中心接口返回详细信息："+JSON.toJSONString(queryCustInfoResponse));
							Map<String, String> parammap = this.getSourceNoAndConsCode(queryCustInfoResponse.getCustInfoBean().getRegOutletCode(), queryCustInfoResponse.getCustInfoBean().getInvstType());
							log.info("获取到的来源与投顾信息："+JSON.toJSONString(parammap));
							if (parammap != null) {
								if (parammap.containsKey("consCode")) {
									String consCode = parammap.get("consCode");
									String newConsCode = "";
									if ("CXGN_CSLS".equals(consCode)) {
										newConsCode = "CXGY_CSLS";
									} else if ("ZSJJN_CSLS".equals(consCode)) {
										newConsCode = "JSJJY_CSLS";
									} else if ("HZN_CSLS".equals(consCode)) {
										newConsCode = "HZY_CSLS";
									} else if ("WZQTN_CSLS".equals(consCode)) {
										newConsCode = "WZQTY_CSLS";
									}
									log.info("转换后的新投顾信息："+newConsCode);
									if(StringUtils.isNotBlank(newConsCode)) {
										CustconstantInfoDomain custDomain = new CustconstantInfoDomain();
										custDomain.setCustno(conscustNo);
										custDomain.setConscode(newConsCode);
										custDomain.setModifier("auto-sys");
										UpdateCustconstantInfoRequest updateRequest = new UpdateCustconstantInfoRequest();
										updateRequest.setCustconstantInfoDomain(custDomain);
										updateCustconstantInfoService.updateCustconstant(updateRequest);
									}
								}
							}
						} else {
							log.info("账户中心接口返回详细信息："+JSON.toJSONString(queryCustInfoResponse));
							log.info("客户投顾划转第二部分（无意向）未匹配到客户信息！");
						}
					} catch (Exception e) {
						log.info("==>获取接口数据异常："+e.getMessage());
					}

				}
			}
			log.info("客户投顾划转第二部分（无意向）执行成功！");
		} else {
			log.info("客户投顾划转第二部分（无意向）未找到相关数据！");
		}
		log.info("客户投顾划转第二部分（无意向）执行结束。。。");
		System.out.println();
		
		log.info("客户投顾划转第三部分（未维护客户）开始执行。。。");
		List<Map<String, Object>> wwhList = transOwnerDao.getWeiweihuCustData();
		log.info("客户投顾划转第三部分（未维护客户）客户数：" + wwhList.size());
		if (wwhList.size() > 0) {
			for (int k = 0; k < wwhList.size(); k++) {
				Map<String, Object> tempDataMap = wwhList.get(k);
				if (tempDataMap.get("CONSCUSTNO") != null && tempDataMap.get("CONSCODE") != null) {
					String conscustNo = tempDataMap.get("CONSCUSTNO").toString();
					String consCode = tempDataMap.get("CONSCODE").toString();
					String newConsCode = "";
					if ("CXGN_CSLS".equals(consCode)) {
						newConsCode = "CXGY_CSLS";
					} else if ("ZSJJN_CSLS".equals(consCode)) {
						newConsCode = "JSJJY_CSLS";
					} else if ("HZN_CSLS".equals(consCode)) {
						newConsCode = "HZY_CSLS";
					} else if ("WZQTN_CSLS".equals(consCode)) {
						newConsCode = "WZQTY_CSLS";
					}
					log.info("转换后的新投顾信息："+newConsCode);
					if(StringUtils.isNotBlank(newConsCode)) {
						CustconstantInfoDomain custDomain = new CustconstantInfoDomain();
						custDomain.setCustno(conscustNo);
						custDomain.setConscode(newConsCode);
						custDomain.setModifier("auto-sys");
						custDomain.setNextcons(newConsCode);
						custDomain.setReason(StaticVar.TRANSF_REASON_OTHERKF);
						custDomain.setModdt(DateTimeUtil.getCurDate());
						UpdateCustconstantInfoRequest updateRequest = new UpdateCustconstantInfoRequest();
						updateRequest.setCustconstantInfoDomain(custDomain);
						updateCustconstantInfoService.updateCustconstant(updateRequest);
					}

				}
			}
			log.info("客户投顾划转第三部分（未维护客户）执行成功！");
		} else {
			log.info("客户投顾划转第三部分（未维护客户）未找到相关数据！");
		}
		log.info("客户投顾划转第三部分（未维护客户）执行结束。。。");

	}

	/**
	 * 根据开户网点号和客户类型获取来源和所属投顾
	 * @param regOutletCode
	 * @param invsttype
	 * @return Map<String, String>
	 */
	public Map<String, String> getSourceNoAndConsCode(String regOutletCode, String invsttype) {
		Map<String, String> scMap = new HashMap<String, String>();
		if (StringUtils.isNotBlank(regOutletCode)) {
			QueryCmSourceInfoRequest request = new QueryCmSourceInfoRequest();
			request.setSourceNo(regOutletCode);
			try {
				QueryCmSourceInfoResponse response = queryCmSourceInfoService.queryCmSourceInfo(request);
				if (response != null && "0000".equals(response.getReturnCode())) {
					CmSourceInfoDomain cmSourceInfoDomain = response.getCmSourceInfo();
					if (cmSourceInfoDomain != null) {
						String firstLevelCode = cmSourceInfoDomain.getFirstLevelCode();
						String consCode = "";

						// 获取客户所属投顾
						if ("0".equals(invsttype) || "2".equals(invsttype)) {
							consCode = "JGKH_ORG";
						} else if ("C".equals(firstLevelCode)) {
							consCode = "CXGN_CSLS";
						} else if ("K".equals(firstLevelCode)) {
							consCode = "ZSJJN_CSLS";
						} else if ("L".equals(firstLevelCode) || "H".equals(firstLevelCode) || "F".equals(firstLevelCode)) {
							consCode = "HZN_CSLS";
						} else {
							consCode = "WZQTN_CSLS";
						}
						scMap.put("sourceNo", cmSourceInfoDomain.getSourceNo());
						scMap.put("consCode", consCode);
					}
				}
			} catch (Exception e) {
				log.info("==>获取来源和投顾编码异常！");
			}
		}
		return scMap;
	}


	@Transactional(rollbackFor = { Exception.class })
	public void copyHisAndUpdateCustConstant(String custNo, String newConsCode) {
		transOwnerDao.copyCustConstantToHistory(custNo);
		transOwnerDao.updateConsCodeByCustNo(newConsCode, custNo);
	}

	public void updateCustConstantWithNoHis(String custNo, String newConsCode) {
		log.info("custNo={}更新为:{}", custNo, newConsCode);
		int i = transOwnerDao.updateConsCodeWithNoBindDateByCustNo(newConsCode, custNo);
		log.info("custNo={}更新为:{},成功条数:{}", custNo, newConsCode, i);
	}


	/**
	 * @description  客户投顾划转第一部分的批量操作（存储过程改造）采取分批sql分页查询，放入线程池
	 * @return
	 * <AUTHOR>
	 * @date 2023/9/14 下午3:58
	 * @since JDK 1.7
	 */
	public void batchTransOwner() {
		int pageNum = 1;
		int pageSize = 500;
		while(true) {
			PageHelper.startPage(pageNum, pageSize);
			List<String> gd400SanWuCusts = transOwnerDao.getGd400SanWuCusts();
			if (CollectionUtils.isEmpty(gd400SanWuCusts)) {
				break;
			}
			doTransOwner(gd400SanWuCusts, V_NEWCONSCODE1, true);
			pageNum ++;
		}
		log.info("高端400呼入三无客户-划转完成");
		pageNum = 1;

		while(true) {
			PageHelper.startPage(pageNum, pageSize);
			List<String> dd400SanWuCusts = transOwnerDao.getDd400SanWuCusts();
			if (CollectionUtils.isEmpty(dd400SanWuCusts)) {
				break;
			}
			doTransOwner(dd400SanWuCusts, V_NEWCONSCODE3, true);
			pageNum ++;
		}
		log.info("低端400呼入三无客户-划转完成");
		pageNum = 1;

		while(true) {
			PageHelper.startPage(pageNum, pageSize);
			List<String> kehuSanWuCusts = transOwnerDao.getKehuSanWuCusts();
			if (CollectionUtils.isEmpty(kehuSanWuCusts)) {
				break;
			}
			doTransOwner(kehuSanWuCusts, V_NEWCONSCODE1, false);
			pageNum ++;
		}
		log.info("客户服务部三无客户-划转完成");
		pageNum = 1;

		while(true) {
			PageHelper.startPage(pageNum, pageSize);
			List<String> kehuCallErrorCusts = transOwnerDao.getKehuCallErrorCusts();
			if (CollectionUtils.isEmpty(kehuCallErrorCusts)) {
				break;
			}
			doTransOwner(kehuCallErrorCusts, V_NEWCONSCODE2, false);
			pageNum ++;
		}
		log.info("客户服务部呼叫错误客户-划转完成");
		pageNum = 1;

		while(true) {
			PageHelper.startPage(pageNum, pageSize);
			List<String> csSanWuCusts = transOwnerDao.getCsSanWuCusts();
			if (CollectionUtils.isEmpty(csSanWuCusts)) {
				break;
			}
			doTransOwner(csSanWuCusts, V_NEWCONSCODE1, true);
			pageNum ++;
		}
		log.info("cs待处理库客户-划转完成");

	}

	/**
	 * @description  具体调用线程池进行批量操作
	 * @param custNos
	 * @param newConsCode
	 * @param noHis	由于cs待处理库客户 直接划转，不需要留痕。调用的sql方法不同。由这个字段作区分，true-不做留痕 false-做留痕
	 * @return
	 * <AUTHOR>
	 * @date 2023/9/14 下午4:03
	 * @since JDK 1.7
	 */
	private void doTransOwner(List<String> custNos, String newConsCode, boolean noHis) {
		if (CollectionUtils.isNotEmpty(custNos)) {
			log.info("客户划转为{}的个数={}", newConsCode, custNos.size());
			//考虑到原有代码中 客户投顾划转有三部分 不知道异步先执行会有什么影响，所以此处使用CountDownLatch等待主线程
			CountDownLatch latch = new CountDownLatch(custNos.size());
			for (String custNo : custNos) {
				transOwnerTask.execute(new TransCustConstantTask(custNo, newConsCode, latch, advisorAutoTransTaskService, noHis));
			}
			try {
				latch.await();
			} catch (InterruptedException e) {
				log.error("latch异常:{}", Throwables.getStackTraceAsString(e));
			}
		}
	}




}