package com.howbuy.cs.task.dao;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.mybatis.spring.annotation.MapperScan;

import com.howbuy.cs.task.model.CsAssignedCustConfigMsg;

@MapperScan
public interface CsAssignedCustConfigMsgDao {

	/**
	 * @Description:得到数据对象List
	 * @param param
	 * @return
	 */
	List<CsAssignedCustConfigMsg> listCsAssignedCustConfigMsg(Map<String, Object> param);
	
	/**
	 * @Description:更新无效客户的待分配状态：客户号不存在、客户状态为无效、处理分配任务中的部门不存在
	 * @param 
	 * @return
	 */
	void updateInvalidCust();
	
	
	/**
	 * @Description:部门配置已使用
	 * @param 
	 * @return
	 */
	List<Map<String,BigDecimal>> selOrgUsed();
	
	
	/**
	 * 批量更新客户分配表中的信息
	 */
	Integer updateBatchCsAssignedCustConfigMsg(final List<CsAssignedCustConfigMsg> csAssignedCustConfigMsgList);
	
	
	
	/**
	 * 批量merge客户分配表中的信息
	 */
	Integer mergeCsAssignedCustConfigMsgList(final List<CsAssignedCustConfigMsg> csAssignedCustConfigMsgList);
	
   
	/**
	 * 移除待分配表中的归档和分配成功的数据
	 */
	Integer removeCsAssignedCustConfigMsg();
}
