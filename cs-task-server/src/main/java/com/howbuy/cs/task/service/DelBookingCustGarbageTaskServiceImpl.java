/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.service;

import com.howbuy.cs.bookcust.dao.BookingCustInfoDao;
import com.howbuy.cs.task.dao.SourceDataDao;
import com.howbuy.cs.task.model.CmProCedusLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: 清除cms预约垃圾数据service接口实现类
 * <AUTHOR>
 * @date 2023/11/15 13:06
 * @since JDK 1.8
 */

@Slf4j
@Service("delBookingCustGarbageTaskService")
public class DelBookingCustGarbageTaskServiceImpl implements DelBookingCustGarbageTaskService {

    @Autowired
    private BookingCustInfoDao bookingCustInfoDao;

    @Autowired
    private SourceDataDao sourceDataDao;

    @Override
    public void delBookingCustGarbage(String arg) {
        try {
            log.info("清除cms预约垃圾数据开始");
            int delNum = bookingCustInfoDao.delBookingCustGarbage();
            log.info("清除cms预约垃圾数据结束，垃圾数据条数：{}", delNum);
        } catch (Exception e) {
            log.error("清除cms预约垃圾数据异常", e);

            CmProCedusLog cmProCedusLog = new CmProCedusLog();
            cmProCedusLog.setPoName("PRO_TASK_DELGARBAGECMSCUST");
            String msg = "PRO_TASK_DELGARBAGECMSCUST：" + e.getMessage();
            cmProCedusLog.setPoMsg(StringUtils.substring(msg, 0, Math.min(500, msg.length())));
            sourceDataDao.insertCmProCeDusLog(cmProCedusLog);
        }
    }
}