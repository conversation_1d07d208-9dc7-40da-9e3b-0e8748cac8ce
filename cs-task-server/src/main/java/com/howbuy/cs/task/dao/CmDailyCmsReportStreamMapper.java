package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.CmDailyCmsReportStream;
import java.math.BigDecimal;

public interface CmDailyCmsReportStreamMapper {
    int deleteByPrimaryKey(BigDecimal id);

    int insert(CmDailyCmsReportStream record);

    int insertSelective(CmDailyCmsReportStream record);

    CmDailyCmsReportStream selectByPrimaryKey(BigDecimal id);

    int updateByPrimaryKeySelective(CmDailyCmsReportStream record);

    int updateByPrimaryKey(CmDailyCmsReportStream record);

    /**
     * @description:(请在此添加描述)
     * @param record
     * @return int
     * @author: jin.wang03
     * @date: 2025/6/19 11:06
     * @since JDK 1.8
     */
    int countByHboneNoAndSendDate(CmDailyCmsReportStream record);

}