<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.cs.task.dao.CsInvestadvisoryQuotaConfigDao">   
	
	 <select id="listCsInvestadvisoryQuotaConfig" parameterType="Map" resultType="com.howbuy.cs.task.model.CsInvestadvisoryQuotaConfig" useCache="false">
	     SELECT * FROM CS_INVESTADVISORY_QUOTA_CONFIG
		 where 1=1
		<if test="isdel != null"> AND isdel = #{isdel} </if>
		<if test="orgCode != null"> AND orgcode = #{orgCode} </if> 
		order by  levelNum asc 
     </select>
     
     
     <update id="updateBatchInvestadvisoryQuotaConfig" parameterType="java.util.List">
	      <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
	         update CS_INVESTADVISORY_QUOTA_CONFIG
	         <set>
		          currcustnum = #{item.currCustNum,jdbcType=NUMERIC},
		          levelnum = #{item.levelNum,jdbcType=NUMERIC}, 
	         </set>
	         where advisorid = #{item.advisorId}
			   and validateflag = 0
       	  </foreach>
     </update>
     
     <select id="getTodayCsInvestadvisoryQuotaConfig"  resultType="com.howbuy.cs.task.model.CsInvestadvisoryQuotaConfig"  useCache="false">
		select a.*,(select t.constcode from hb_constant t   where t.typecode = 'maxCustLimit') as maxCustLimit,c.usedAdvisoryQuota
		  from CS_INVESTADVISORY_QUOTA_CONFIG a, cm_consultant b,
		      (select t.conscode, count(1) as usedAdvisoryQuota
               from cm_custconstant t
               join cm_consultant t2
                 on t2.conscode = t.conscode
              where t2.isvirtual != '1'
                and instr(t2.conscode, '.') <![CDATA[>]]> 0
              group by t.conscode) c
		 where instr(a.workflag, (to_char(sysdate, 'D') - 1)) != 0
		   and a.validateflag = 0
		   and a.advisorid = b.conscode
		   and a.advisorid = c.conscode
		   and b.consstatus = '1'
		 order by a.levelnum
	</select>
	
	
	<update id="initCsInvestadvisoryQuotaConfig">
          update CS_INVESTADVISORY_QUOTA_CONFIG
             set currcustnum = null,
                 sysModdt    = sysdate
           where validateflag = 0 and numfrequency = '1'
    </update>
	
</mapper>



