package com.howbuy.cs.task.model;

import lombok.Data;

import java.io.Serializable;


/**
 * @Description: 实体类Custconstant.java
 * <AUTHOR>
 * @version 1.0
 * @created 
 */
@Data
public class Custconstant implements Serializable {

	private static final long serialVersionUID = -1049152378635399990L;

	private String custno;
	
	private String conscode;
	
	private String startdt;
	
	private String enddt;
	
	private String memo;
	
	private String recstat;
	
	private String checkflag;
	
	private String creator;
	
	private String modifier;
	
	private String checker;
	
	private String credt;
	
	private String moddt;
	
    private String tasktype;
    
    private String waitid;
	
}
