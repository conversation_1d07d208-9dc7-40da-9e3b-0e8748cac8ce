package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.CmConsultant;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface CmConsultantMapper {
    int insert(CmConsultant record);

    int insertSelective(CmConsultant record);

    int batchInsert(@Param("list") List<CmConsultant> list);

    /**
     * @description: 根据投顾code查询投顾信息
     * @param consCode	 投顾code
     * @return com.howbuy.cs.task.model.CmConsultant
     * @author: hongdong.xie
     * @date: 2024/11/25 19:02
     * @since JDK 1.8
     */
    CmConsultant selectByConsCode(@Param("consCode") String consCode);
}