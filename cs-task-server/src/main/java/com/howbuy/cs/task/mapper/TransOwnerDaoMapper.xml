<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.cs.task.dao.TransOwnerDao">

    <select id="transOwnerByProcedure" statementType="CALLABLE" parameterType="Map" resultType="Map" useCache="false">
        <![CDATA[{call PRO_BATCH_TRANS_OWNER(
        	#{PO_RETCODE,mode=OUT,jdbcType=VARCHAR},
    		#{PO_RETMSG,mode=OUT,jdbcType=VARCHAR}
    	)} ]]>
    </select>
    
    <select id="getWuyixiangCustData" resultType="Map">
        SELECT A.CONSCUSTNO, A.HBONE_NO AS HBONENO
		  FROM CM_CONSCUST A
		  LEFT JOIN CM_CUSTCONSTANT B
		    ON B.CUSTNO = A.CONSCUSTNO
		 WHERE A.HBONE_NO IS NOT NULL
		   AND B.CONSCODE = 'wuyixiang'
		   AND A.NEWSOURCENO IN ('PH1007T10', 'PH1007T11', 'PH0805T15', 'PH1107T76')
		   AND EXISTS
		 (
		  SELECT 1 FROM CS_COMMUNICATE_VISIT B WHERE B.CONSCUSTNO = A.CONSCUSTNO
		 )
    </select>
    
    <select id="getWeiweihuCustData" resultType="Map">
        SELECT A.CONSCUSTNO, B.CONSCODE
		  FROM CM_CONSCUST A
		  LEFT JOIN CM_CUSTCONSTANT B
		    ON B.CUSTNO = A.CONSCUSTNO
		 WHERE A.INVSTTYPE = '1' AND B.CONSCODE IN ('CXGN_CSLS', 'ZSJJN_CSLS', 'HZN_CSLS', 'WZQTN_CSLS')
		   AND EXISTS
		 (
		  SELECT 1 FROM CS_COMMUNICATE_VISIT B WHERE B.CONSCUSTNO = A.CONSCUSTNO
		 )
    </select>
	
	<insert id="copyCustConstantToHistory" parameterType="string" useGeneratedKeys="false">
		INSERT INTO CM_CUSTCONSTANTHIS
		 ( CUSTCONSHISID,
           CUSTNO,
           CONSCODE,
           STARTDT,
           ENDDT,
           CREATOR,
           CREDT,
           BINDDATE,
           UNBUNDDATE)
         SELECT SEQ_CUSTREC.NEXTVAL,
             T1.CUSTNO AS CUSTNO,
             T1.CONSCODE AS CONSCODE,
             T1.STARTDT AS STARTDT,
             TO_CHAR(SYSDATE, 'yyyyMMdd') AS ENDDT,
             'sys' AS CREATOR,
             TO_CHAR(SYSDATE, 'yyyyMMdd') AS CREDT,
             T1.BINDDATE,
             SYSDATE
         FROM CM_CUSTCONSTANT T1
         WHERE T1.CUSTNO = #{custNo}
	</insert>

    <update id="updateConsCodeByCustNo" parameterType="string">
        UPDATE CM_CUSTCONSTANT T1
        SET T1.CONSCODE = #{newConsCode},
           T1.STARTDT  = TO_CHAR(SYSDATE, 'yyyyMMdd'),
           T1.MODIFIER = 'sys',
           T1.MODDT    = TO_CHAR(SYSDATE, 'yyyyMMdd'),
           T1.BINDDATE = SYSDATE
        WHERE T1.CUSTNO = #{custNo}
    </update>


    <update id="updateConsCodeWithNoBindDateByCustNo" parameterType="string">
        UPDATE CM_CUSTCONSTANT T1
       SET T1.CONSCODE = #{newConsCode},
           T1.MODIFIER = 'sys',
           T1.MODDT    = TO_CHAR(SYSDATE, 'yyyyMMdd')
     WHERE T1.CUSTNO = #{custNo}
    </update>

    <select id="getGd400SanWuCusts" resultType="string">
        SELECT T3.CUSTNO AS CUSTNO
        FROM CS_CALLIN T1
                 LEFT JOIN (SELECT *
                            FROM (SELECT A.CONSCUSTNO,
                                         A.COMMINTENT,
                                         A.AMOUNTFLAG,
                                         A.INVESTINTENT,
                                         ROW_NUMBER() OVER (PARTITION BY CONSCUSTNO ORDER BY HISID DESC) R
                                  FROM CS_COMMUNICATE_VISIT A
                                  WHERE A.HISFLAG = '1')
                            WHERE R = 1) T2
                           ON T2.CONSCUSTNO = T1.CONSCUSTNO
                 LEFT JOIN CM_CUSTCONSTANT T3
                           ON T3.CUSTNO = T1.CONSCUSTNO
        WHERE T1.IVR in ('3406', '3230', '3222', '3300')
          AND T1.CONSCUSTNO IS NOT NULL
          AND T2.COMMINTENT = 1
          AND T2.AMOUNTFLAG = 1
          AND T2.INVESTINTENT = 0
          AND T3.CONSCODE = 'DFPXNK'
    </select>

    <select id="getDd400SanWuCusts" resultType="string">
        SELECT T3.CUSTNO AS CUSTNO
        FROM CS_CALLIN T1
                 LEFT JOIN (SELECT *
                            FROM (SELECT A.CONSCUSTNO,
                                         A.COMMINTENT,
                                         A.AMOUNTFLAG,
                                         A.INVESTINTENT,
                                         ROW_NUMBER() OVER (PARTITION BY CONSCUSTNO ORDER BY HISID DESC) R
                                  FROM CS_COMMUNICATE_VISIT A
                                  WHERE A.HISFLAG = '1')
                            WHERE R = 1) T2
                           ON T2.CONSCUSTNO = T1.CONSCUSTNO
                 LEFT JOIN CM_CUSTCONSTANT T3
                           ON T3.CUSTNO = T1.CONSCUSTNO
        WHERE T1.IVR in ('3400', '3224')
          AND T1.CONSCUSTNO IS NOT NULL
          AND T2.COMMINTENT = 1
          AND T2.AMOUNTFLAG = 1
          AND T2.INVESTINTENT = 0
          AND T3.CONSCODE = 'DFPXNK'
    </select>


    <select id="getKehuSanWuCusts" resultType="string">
        SELECT T3.CUSTNO
        FROM CS_TASK_ASSIGN_DAY T1
                 LEFT JOIN CS_CALLOUT_WAITDISTRIBUTE T2
                           ON T2.WAITID = T1.WAITID
                 LEFT JOIN CM_CUSTCONSTANT T3
                           ON T3.CUSTNO = T2.CONSCUSTNO
                 LEFT JOIN (SELECT *
                            FROM (SELECT A.CONSCUSTNO,
                                         COMMINTENT,
                                         AMOUNTFLAG,
                                         INVESTINTENT,
                                         ROW_NUMBER() OVER (PARTITION BY CONSCUSTNO ORDER BY HISID DESC) R
                                  FROM CS_COMMUNICATE_VISIT A
                                  WHERE A.HISFLAG = '1'
                                    AND TO_CHAR(A.CREDT, 'yyyy-MM-dd') = TO_CHAR(SYSDATE, 'yyyy-MM-dd'))
                            WHERE R = 1) C
                           ON C.CONSCUSTNO = T2.CONSCUSTNO
        WHERE T1.HANDLEDATE IS NOT NULL
          AND (T1.CALLOUTSTATUS IN (3, 4, 5) OR
               (T1.CALLOUTSTATUS = 1 AND C.COMMINTENT = '1' AND C.AMOUNTFLAG = '1' AND C.INVESTINTENT = '0'))
          AND T2.TASK_TYPE = '1001'
          AND T2.DISPOSENUM = 5
          AND TO_CHAR(T2.DISPOSEDATE, 'yyyy-MM-dd') = TO_CHAR(SYSDATE, 'yyyy-MM-dd')
          AND T3.CONSCODE IN
              (SELECT T1.CONSCODE
               FROM CM_CONSULTANT T1
                        LEFT JOIN HB_ORGANIZATION T2
                                  ON T1.OUTLETCODE = T2.ORGCODE
               WHERE T1.CONSSTATUS = '1'
                 AND T2.ORGCODE = '12'
               UNION
               SELECT T1.CONSCODE
               FROM CM_CONSULTANT T1
                        LEFT JOIN HB_ORGANIZATION T2
                                  ON T1.OUTLETCODE = T2.ORGCODE
               WHERE T1.CONSCODE not in
                     ('20wTemporaryLibrary', 'wuyixiang')
                 AND T1.CONSSTATUS = '1'
                 AND T2.ORGCODE = '11')
          AND T2.CONSCUSTNO IS NOT NULL
    </select>


    <select id="getKehuCallErrorCusts" resultType="string">
        SELECT T3.CUSTNO
        FROM CS_TASK_ASSIGN_DAY T1
                 LEFT JOIN CS_CALLOUT_WAITDISTRIBUTE T2
                           ON T2.WAITID = T1.WAITID
                 LEFT JOIN CM_CUSTCONSTANT T3
                           ON T3.CUSTNO = T2.CONSCUSTNO
        WHERE T1.HANDLEDATE IS NOT NULL
          AND T1.CALLOUTSTATUS = 8
          AND T2.TASK_TYPE = '1001'
          AND T2.DISPOSENUM = 5
          AND TO_CHAR(T2.DISPOSEDATE, 'yyyy-MM-dd') = TO_CHAR(SYSDATE, 'yyyy-MM-dd')
          AND T3.CONSCODE IN
              (SELECT T1.CONSCODE
               FROM CM_CONSULTANT T1
                        LEFT JOIN HB_ORGANIZATION T2
                                  ON T1.OUTLETCODE = T2.ORGCODE
               WHERE T1.CONSSTATUS = '1'
                 AND T2.ORGCODE = '12'
               UNION
               SELECT T1.CONSCODE
               FROM CM_CONSULTANT T1
                        LEFT JOIN HB_ORGANIZATION T2
                                  ON T1.OUTLETCODE = T2.ORGCODE
               WHERE T1.CONSCODE NOT IN
                     ('20wTemporaryLibrary', 'false')
                 AND T1.CONSSTATUS = '1'
                 AND T2.ORGCODE = '11')
          AND T2.CONSCUSTNO IS NOT NULL
    </select>

    <select id="getCsSanWuCusts" resultType="string">
        SELECT T1.CUSTNO
        FROM CM_CUSTCONSTANT T1
        WHERE T1.CONSCODE = 'DFPXNK'
          AND EXISTS(SELECT 1
                     FROM CS_CALLOUT_WAITDISTRIBUTE T
                     WHERE T.CONSCUSTNO = T1.CUSTNO
                       AND T.DISPOSENUM = '5'
                       AND T.DISPOSEDATE <![CDATA[ < ]]> SYSDATE)
          AND EXISTS(SELECT 1
                     FROM CS_COMMUNICATE_VISIT T
                     WHERE T.CONSCUSTNO = T1.CUSTNO
                       AND T.COMMINTENT IN ('1', '3')
                       AND T.INVESTINTENT = '0'
                       AND T.AMOUNTFLAG = '1'
                       AND T.HISFLAG = '1')
    </select>
</mapper>