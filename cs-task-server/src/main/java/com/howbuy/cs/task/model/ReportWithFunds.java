/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.model;


import com.howbuy.member.dto.report.ProductReportUpdatePushDTO;

import java.util.List;

/**
 * @description: 报告和基金的包装类
 * <AUTHOR>
 * @date 2025-06-18 14:11:44
 * @since JDK 1.8
 */
public class ReportWithFunds {
    
    /**
     * 报告对象
     */
    private ProductReportUpdatePushDTO report;
    
    /**
     * 客户在该报告中持有的基金列表
     */
    private List<String> funds;

    /**
     * @description: 构造方法
     * @param report 报告对象
     * @param funds 基金列表
     * <AUTHOR>
     * @date 2025-06-18 14:11:44
     * @since JDK 1.8
     */
    public ReportWithFunds(ProductReportUpdatePushDTO report, List<String> funds) {
        this.report = report;
        this.funds = funds;
    }

    /**
     * @description: 获取报告对象
     * @return Object 报告对象
     * <AUTHOR>
     * @date 2025-06-18 14:11:44
     * @since JDK 1.8
     */
    public ProductReportUpdatePushDTO getReport() {
        return report;
    }

    /**
     * @description: 设置报告对象
     * @param report 报告对象
     * <AUTHOR>
     * @date 2025-06-18 14:11:44
     * @since JDK 1.8
     */
    public void setReport(ProductReportUpdatePushDTO report) {
        this.report = report;
    }

    /**
     * @description: 获取基金列表
     * @return List<String> 基金列表
     * <AUTHOR>
     * @date 2025-06-18 14:11:44
     * @since JDK 1.8
     */
    public List<String> getFunds() {
        return funds;
    }

    /**
     * @description: 设置基金列表
     * @param funds 基金列表
     * <AUTHOR>
     * @date 2025-06-18 14:11:44
     * @since JDK 1.8
     */
    public void setFunds(List<String> funds) {
        this.funds = funds;
    }
}
