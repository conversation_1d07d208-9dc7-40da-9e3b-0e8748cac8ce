package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.BookingCust;
import com.howbuy.cs.task.model.CmProCedusLog;
import com.howbuy.cs.task.model.CsCalloutWaitDistribute;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;
import java.util.Map;

@MapperScan
public interface SourceDataDao {

	/**
	 * 获取预约客户信息
	 */
	List<BookingCust> getBookingCustData();

	/**
	 * 将源数据插入到待分配表中
	 */
	Integer insertBatchAddSourceData(List<CsCalloutWaitDistribute> csCalloutWaitDistributeList);

	/**
	 * 更新预约表中的已经读取的标识符
	 */
	Integer updateBookCustReadFlag(List<Integer> orderDataList);

	/**
	 * 获取呼损客户数据
	 */
	List<Map<String, Object>> getCallLossCustData();

	/**
	 * 更新呼损客户表中的已读标识符
	 */
	Integer updateCallLossReadFlag(List<Integer> orderDataList);

	/**
	 * 获取云翌原始数据方法
	 */
	List<Map<String, Object>> getYunEasyCustData();

	/**
	 *
	 * 修改云翌数据读取标识
	 */
	Long updateYunEasyReadFlag(List<Long> orderDataList);

	/**
	 * @description: 插入数据库错误日志
	 * @param cmProCedusLog 数据库错误日志内容
	 * @return int 插入条数
	 * @author: jin.wang03
	 * @date: 2023/11/15 13:52
	 * @since JDK 1.8
	 */
	int insertCmProCeDusLog(CmProCedusLog cmProCedusLog);
}
