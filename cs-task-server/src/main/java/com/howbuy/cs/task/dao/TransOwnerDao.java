package com.howbuy.cs.task.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

/**
 * 执行划转客户投顾操作
 */
@MapperScan
public interface TransOwnerDao {

	/**
	 * 通过存储过程对客户投顾进行划转
	 */
	void transOwnerByProcedure(Map<String, String> paramMap);

	/**
	 * 获取无意向客户信息
	 */
	List<Map<String, Object>> getWuyixiangCustData();
	
	/**
	 * 获取未维护客户信息
	 */
	List<Map<String, Object>> getWeiweihuCustData();

	int copyCustConstantToHistory(@Param("custNo")String custNo);

	int updateConsCodeByCustNo(@Param("newConsCode")String newConsCode, @Param("custNo")String custNo);

	int updateConsCodeWithNoBindDateByCustNo(@Param("newConsCode")String newConsCode, @Param("custNo")String custNo);

	/**
	 * @description 高端400呼入三无客户
	 * @return
	 * <AUTHOR>
	 * @date 2023/9/12 下午4:41
	 * @since JDK 1.8
	 */
	List<String> getGd400SanWuCusts();

	/**
	 * @description 低端400呼入三无客户
	 * @return
	 * <AUTHOR>
	 * @date 2023/9/12 下午4:41
	 * @since JDK 1.8
	 */
	List<String> getDd400SanWuCusts();

	/**
	 * @description 客户服务部三无客户
	 * @return
	 * <AUTHOR>
	 * @date 2023/9/12 下午4:41
	 * @since JDK 1.8
	 */
	List<String> getKehuSanWuCusts();

	/**
	 * @description 客户服务部呼叫错误客户
	 * @return
	 * <AUTHOR>
	 * @date 2023/9/12 下午4:41
	 * @since JDK 1.8
	 */
	List<String> getKehuCallErrorCusts();

	/**
	 * @description cs待处理库的客户
	 * @return
	 * <AUTHOR>
	 * @date 2023/9/12 下午4:41
	 * @since JDK 1.8
	 */
	List<String> getCsSanWuCusts();
}
