package com.howbuy.cs.task.model;

import java.util.Date;

/**
 * 客户投顾信息历史表
 */
public class CmCustconstanthis {
    /**
    * 客户投顾历史编号
    */
    private String custconshisid;

    /**
    * 客户号
    */
    private String custno;

    /**
    * 投资顾问代码
    */
    private String conscode;

    /**
    * 开始日期
    */
    private String startdt;

    /**
    * 结束日期
    */
    private String enddt;

    /**
    * 备注
    */
    private String memo;

    /**
    * 记录状态
    */
    private String recstat;

    /**
    * 复核标志
    */
    private String checkflag;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 复核人
    */
    private String checker;

    /**
    * 记录创建日期
    */
    private String credt;

    /**
    * 记录修改日期
    */
    private String moddt;

    /**
    * 潜在客户编号
    */
    private String pcustid;

    /**
    * 绑定（分配）时间（带时分秒）
    */
    private Date binddate;

    /**
    * 解绑（解分配）时间（带时分秒）
    */
    private Date unbunddate;

    /**
    * 下一个投顾
    */
    private String nextcons;

    /**
    * 划转原因
    */
    private String reason;

    /**
    * 划转备注
    */
    private String remark;

    /**
    * 地区
    */
    private String citycodeMobile;

    /**
    * 无线渠道
    */
    private String wifichannel;

    /**
    * 客户来源
    */
    private String custsource;

    /**
    * 处理类型：1实时；2再处理
    */
    private String dealtype;

    /**
    * 地区
    */
    private String area;

    /**
    * 客户类型：枚举有新客户、老客户
    */
    private String custtype;

    /**
    * 客户成交状态1成交；0潜客
    */
    private String custtradetype;

    /**
    * 上一条分配id
    */
    private String beforehisid;

    /**
    * 重复客户划转关联日志id
    */
    private Integer repeatcustlogid;

    /**
    * 重复客户划转表ID
    */
    private Integer transfLogId;

    /**
    * 操作时间
    */
    private Date operateDate;

    public String getCustconshisid() {
        return custconshisid;
    }

    public void setCustconshisid(String custconshisid) {
        this.custconshisid = custconshisid;
    }

    public String getCustno() {
        return custno;
    }

    public void setCustno(String custno) {
        this.custno = custno;
    }

    public String getConscode() {
        return conscode;
    }

    public void setConscode(String conscode) {
        this.conscode = conscode;
    }

    public String getStartdt() {
        return startdt;
    }

    public void setStartdt(String startdt) {
        this.startdt = startdt;
    }

    public String getEnddt() {
        return enddt;
    }

    public void setEnddt(String enddt) {
        this.enddt = enddt;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getRecstat() {
        return recstat;
    }

    public void setRecstat(String recstat) {
        this.recstat = recstat;
    }

    public String getCheckflag() {
        return checkflag;
    }

    public void setCheckflag(String checkflag) {
        this.checkflag = checkflag;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getChecker() {
        return checker;
    }

    public void setChecker(String checker) {
        this.checker = checker;
    }

    public String getCredt() {
        return credt;
    }

    public void setCredt(String credt) {
        this.credt = credt;
    }

    public String getModdt() {
        return moddt;
    }

    public void setModdt(String moddt) {
        this.moddt = moddt;
    }

    public String getPcustid() {
        return pcustid;
    }

    public void setPcustid(String pcustid) {
        this.pcustid = pcustid;
    }

    public Date getBinddate() {
        return binddate;
    }

    public void setBinddate(Date binddate) {
        this.binddate = binddate;
    }

    public Date getUnbunddate() {
        return unbunddate;
    }

    public void setUnbunddate(Date unbunddate) {
        this.unbunddate = unbunddate;
    }

    public String getNextcons() {
        return nextcons;
    }

    public void setNextcons(String nextcons) {
        this.nextcons = nextcons;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCitycodeMobile() {
        return citycodeMobile;
    }

    public void setCitycodeMobile(String citycodeMobile) {
        this.citycodeMobile = citycodeMobile;
    }

    public String getWifichannel() {
        return wifichannel;
    }

    public void setWifichannel(String wifichannel) {
        this.wifichannel = wifichannel;
    }

    public String getCustsource() {
        return custsource;
    }

    public void setCustsource(String custsource) {
        this.custsource = custsource;
    }

    public String getDealtype() {
        return dealtype;
    }

    public void setDealtype(String dealtype) {
        this.dealtype = dealtype;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getCusttype() {
        return custtype;
    }

    public void setCusttype(String custtype) {
        this.custtype = custtype;
    }

    public String getCusttradetype() {
        return custtradetype;
    }

    public void setCusttradetype(String custtradetype) {
        this.custtradetype = custtradetype;
    }

    public String getBeforehisid() {
        return beforehisid;
    }

    public void setBeforehisid(String beforehisid) {
        this.beforehisid = beforehisid;
    }

    public Integer getRepeatcustlogid() {
        return repeatcustlogid;
    }

    public void setRepeatcustlogid(Integer repeatcustlogid) {
        this.repeatcustlogid = repeatcustlogid;
    }

    public Integer getTransfLogId() {
        return transfLogId;
    }

    public void setTransfLogId(Integer transfLogId) {
        this.transfLogId = transfLogId;
    }

    public Date getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(Date operateDate) {
        this.operateDate = operateDate;
    }
}