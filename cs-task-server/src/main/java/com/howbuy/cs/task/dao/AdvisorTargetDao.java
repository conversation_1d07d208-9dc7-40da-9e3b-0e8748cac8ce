package com.howbuy.cs.task.dao;

import java.util.List;
import java.util.Map;

import org.mybatis.spring.annotation.MapperScan;

import com.howbuy.cs.task.model.CsInvestAdvisorTarget;

@MapperScan
public interface AdvisorTargetDao {
	/**
	 * 获取当天工作的投顾配额信息
	 */
	public List<CsInvestAdvisorTarget> getToadyValidateAdvisorTotalNum();

	/**
	 * 在客服沟通表中获取没有分配投顾的客户信息，并且不是垃圾客户
	 */
	public List<Map<String, Object>> getCallOutNoAdvisorCust();

	/**
	 * 获取呼入待分配的客户信息
	 */
	public List<Map<String, Object>> getCallInNoAdvisorCust();

	/**
	 * 初始化投顾额度表
	 */
	public Integer updateInitAdvisorTarget();

	/**
	 * 批量更新投顾额度表中的信息
	 */
	public Integer updateBatchUpdateAdvisorTarget(final List<CsInvestAdvisorTarget> mergerAdvisorList);
	
	/**
	 * 在客服沟通表中获取没有分配投顾的客户信息，并且不是垃圾客户:针对新任务
	 */
	public List<Map<String, Object>> getCallOutNoAdvisorCustNew();
	
	
	/**
	 * 获取呼入待分配的客户信息:针对新任务
	 */
	public List<Map<String, Object>> getCallInNoAdvisorCustNew();
	
	
	Map<String,String> qryPhoneArea(String phoneNumber);

}
