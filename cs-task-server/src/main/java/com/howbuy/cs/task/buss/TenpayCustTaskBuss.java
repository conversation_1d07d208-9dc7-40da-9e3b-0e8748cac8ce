package com.howbuy.cs.task.buss;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.howbuy.common.util.StringUtil;
import com.howbuy.cs.task.dao.TenpayCustDao;

@Service("tenpayCustTaskBuss")
public class TenpayCustTaskBuss {
	
	@Autowired
	private TenpayCustDao tenpayCustDao;
	
	/**
     * 获取投顾客户号
     */
    public String getConsCustNo() {
    	long seqValue = tenpayCustDao.getSeqValue("SEQ_CUSTNO");
    	
    	// 固定长度的值
		String fixSeqValue = StringUtil.fillZero(seqValue + "", 8);
		return new StringBuffer("1").append(fixSeqValue).append(StringUtil.getValiateCode("1" + fixSeqValue)).toString();
	}
    

}
