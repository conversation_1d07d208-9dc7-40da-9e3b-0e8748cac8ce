<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.cs.task.dao.CsDeptAssignRuleOrgConfigDao">

      <select id="listCsDeptAssignRuleOrgConfig" parameterType="Map" resultType="com.howbuy.cs.task.model.CsDeptAssignRuleOrgConfig" useCache="false">
	    SELECT * FROM CS_DEPT_ASSIGN_RULE_ORG_CONFIG a
		where 1=1 
		and exists (select 1 from hb_organization d where a.orgcode =d.orgcode  and d.status = '0')
		<if test="id != null"> AND a.id = #{id} </if>
		<if test="roleconfigid != null"> AND a.roleconfigid = #{roleconfigid} </if>
		<if test="orgcode != null"> AND a.orgcode = #{orgcode} </if>
		<if test="orgname != null"> AND a.orgname = #{orgname} </if>
		<if test="payoutratio != null"> AND a.payoutratio = #{payoutratio} </if>
		<if test="payoutupper != null"> AND a.payoutupper = #{payoutupper} </if>
		<if test="createdt != null"> AND a.createdt = #{createdt} </if>
		<if test="modifydt != null"> AND a.modifydt = #{modifydt} </if>
		<if test="modifier != null"> AND a.modifier = #{modifier} </if>
		<if test="creator != null"> AND a.creator = #{creator} </if>
		<if test="isdel != null"> AND a.isdel = #{isdel} </if>  
		order by  a.modifydt asc ,a.createdt asc,a.orgname asc
      </select>
</mapper>



