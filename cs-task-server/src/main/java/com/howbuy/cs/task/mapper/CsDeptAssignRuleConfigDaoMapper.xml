<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.cs.task.dao.CsDeptAssignRuleConfigDao">

    <select id="listCsDeptAssignRuleConfig" parameterType="Map" resultType="com.howbuy.cs.task.model.CsDeptAssignRuleConfig" useCache="false">
	    SELECT ID,ATTRIBUTIONCODEFLAG,ATTRIBUTIONNAMEFLAG,LEVELNUM,CREATEDT,MODIFYDT,MODIFIER,CREATOR,ISDEL, RULETYPE,LABELCODE,LABELNAME
	    FROM CS_DEPT_ASSIGN_RULE_CONFIG
		where 1=1
		<if test="id != null"> AND id = #{id} </if>
		<if test="attributioncodeflag != null"> AND attributioncodeflag = #{attributioncodeflag} </if>
		<if test="attributionnameflag != null"> AND attributionnameflag = #{attributionnameflag} </if>
		<if test="levelnum != null"> AND levelnum = #{levelnum} </if>
		<if test="createdt != null"> AND createdt = #{createdt} </if>
		<if test="modifydt != null"> AND modifydt = #{modifydt} </if>
		<if test="modifier != null"> AND modifier = #{modifier} </if>
		<if test="creator != null"> AND creator = #{creator} </if>
		<if test="isdel != null"> AND isdel = #{isdel} </if>  
		order by  levelnum        
      </select>
           
           
</mapper>



