<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.cs.task.dao.CmStockSplitConfigMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.cs.task.model.CmStockSplitConfig">
    <!--@mbg.generated-->
    <!--@Table CM_STOCK_SPLIT_CONFIG-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CONFIG_TYPE" jdbcType="VARCHAR" property="configType" />
    <result column="FORMER_ORG_CODE" jdbcType="VARCHAR" property="formerOrgCode" />
    <result column="FORMER_CONS_CODE" jdbcType="VARCHAR" property="formerConsCode" />
    <result column="NEWLY_ORG_CODE" jdbcType="VARCHAR" property="newlyOrgCode" />
    <result column="NEWLY_CONS_CODE" jdbcType="VARCHAR" property="newlyConsCode" />
    <result column="CONFIG_LEVEL" jdbcType="VARCHAR" property="configLevel" />
    <result column="ACTIVE_DT" jdbcType="VARCHAR" property="activeDt" />
    <result column="ACTIVE_FLAG" jdbcType="VARCHAR" property="activeFlag" />
    <result column="VALID_FLAG" jdbcType="VARCHAR" property="validFlag" />
    <result column="CAL_START_DT" jdbcType="VARCHAR" property="calStartDt" />
    <result column="CAL_END_DT" jdbcType="VARCHAR" property="calEndDt" />
    <result column="FORMER_CAL_RATE" jdbcType="DECIMAL" property="formerCalRate" />
    <result column="NEWLY_CAL_RATE" jdbcType="DECIMAL" property="newlyCalRate" />
    <result column="LOCK_DURATION_AMT" jdbcType="DECIMAL" property="lockDurationAmt" />
    <result column="AUDIT_STATUS" jdbcType="VARCHAR" property="auditStatus" />
    <result column="LAST_AUDIT_STATUS" jdbcType="VARCHAR" property="lastAuditStatus" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIMESTAMP" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIMESTAMP" jdbcType="TIMESTAMP" property="modifyTimestamp" />
    <result column="AUDITOR" jdbcType="VARCHAR" property="auditor" />
    <result column="AUDIT_TIMESTAMP" jdbcType="TIMESTAMP" property="auditTimestamp" />
    <result column="REC_STAT" jdbcType="VARCHAR" property="recStat" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CONFIG_TYPE, FORMER_ORG_CODE, FORMER_CONS_CODE, NEWLY_ORG_CODE, NEWLY_CONS_CODE, 
    CONFIG_LEVEL, ACTIVE_DT, ACTIVE_FLAG, VALID_FLAG, CAL_START_DT, CAL_END_DT, FORMER_CAL_RATE, 
    NEWLY_CAL_RATE, LOCK_DURATION_AMT, AUDIT_STATUS, LAST_AUDIT_STATUS, REMARK, CREATOR, 
    CREATE_TIMESTAMP, MODIFIER, MODIFY_TIMESTAMP, AUDITOR, AUDIT_TIMESTAMP, REC_STAT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_STOCK_SPLIT_CONFIG
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CM_STOCK_SPLIT_CONFIG
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.cs.task.model.CmStockSplitConfig">
    <!--@mbg.generated-->
    insert into CM_STOCK_SPLIT_CONFIG (ID, CONFIG_TYPE, FORMER_ORG_CODE, 
      FORMER_CONS_CODE, NEWLY_ORG_CODE, NEWLY_CONS_CODE, 
      CONFIG_LEVEL, ACTIVE_DT, ACTIVE_FLAG, 
      VALID_FLAG, CAL_START_DT, CAL_END_DT, 
      FORMER_CAL_RATE, NEWLY_CAL_RATE, LOCK_DURATION_AMT, 
      AUDIT_STATUS, LAST_AUDIT_STATUS, REMARK, 
      CREATOR, CREATE_TIMESTAMP, MODIFIER, 
      MODIFY_TIMESTAMP, AUDITOR, AUDIT_TIMESTAMP, 
      REC_STAT)
    values (#{id,jdbcType=VARCHAR}, #{configType,jdbcType=VARCHAR}, #{formerOrgCode,jdbcType=VARCHAR}, 
      #{formerConsCode,jdbcType=VARCHAR}, #{newlyOrgCode,jdbcType=VARCHAR}, #{newlyConsCode,jdbcType=VARCHAR}, 
      #{configLevel,jdbcType=VARCHAR}, #{activeDt,jdbcType=VARCHAR}, #{activeFlag,jdbcType=VARCHAR}, 
      #{validFlag,jdbcType=VARCHAR}, #{calStartDt,jdbcType=VARCHAR}, #{calEndDt,jdbcType=VARCHAR}, 
      #{formerCalRate,jdbcType=DECIMAL}, #{newlyCalRate,jdbcType=DECIMAL}, #{lockDurationAmt,jdbcType=DECIMAL}, 
      #{auditStatus,jdbcType=VARCHAR}, #{lastAuditStatus,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{createTimestamp,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, 
      #{modifyTimestamp,jdbcType=TIMESTAMP}, #{auditor,jdbcType=VARCHAR}, #{auditTimestamp,jdbcType=TIMESTAMP}, 
      #{recStat,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.cs.task.model.CmStockSplitConfig">
    <!--@mbg.generated-->
    insert into CM_STOCK_SPLIT_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="configType != null">
        CONFIG_TYPE,
      </if>
      <if test="formerOrgCode != null">
        FORMER_ORG_CODE,
      </if>
      <if test="formerConsCode != null">
        FORMER_CONS_CODE,
      </if>
      <if test="newlyOrgCode != null">
        NEWLY_ORG_CODE,
      </if>
      <if test="newlyConsCode != null">
        NEWLY_CONS_CODE,
      </if>
      <if test="configLevel != null">
        CONFIG_LEVEL,
      </if>
      <if test="activeDt != null">
        ACTIVE_DT,
      </if>
      <if test="activeFlag != null">
        ACTIVE_FLAG,
      </if>
      <if test="validFlag != null">
        VALID_FLAG,
      </if>
      <if test="calStartDt != null">
        CAL_START_DT,
      </if>
      <if test="calEndDt != null">
        CAL_END_DT,
      </if>
      <if test="formerCalRate != null">
        FORMER_CAL_RATE,
      </if>
      <if test="newlyCalRate != null">
        NEWLY_CAL_RATE,
      </if>
      <if test="lockDurationAmt != null">
        LOCK_DURATION_AMT,
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS,
      </if>
      <if test="lastAuditStatus != null">
        LAST_AUDIT_STATUS,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="createTimestamp != null">
        CREATE_TIMESTAMP,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="modifyTimestamp != null">
        MODIFY_TIMESTAMP,
      </if>
      <if test="auditor != null">
        AUDITOR,
      </if>
      <if test="auditTimestamp != null">
        AUDIT_TIMESTAMP,
      </if>
      <if test="recStat != null">
        REC_STAT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="configType != null">
        #{configType,jdbcType=VARCHAR},
      </if>
      <if test="formerOrgCode != null">
        #{formerOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="formerConsCode != null">
        #{formerConsCode,jdbcType=VARCHAR},
      </if>
      <if test="newlyOrgCode != null">
        #{newlyOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="newlyConsCode != null">
        #{newlyConsCode,jdbcType=VARCHAR},
      </if>
      <if test="configLevel != null">
        #{configLevel,jdbcType=VARCHAR},
      </if>
      <if test="activeDt != null">
        #{activeDt,jdbcType=VARCHAR},
      </if>
      <if test="activeFlag != null">
        #{activeFlag,jdbcType=VARCHAR},
      </if>
      <if test="validFlag != null">
        #{validFlag,jdbcType=VARCHAR},
      </if>
      <if test="calStartDt != null">
        #{calStartDt,jdbcType=VARCHAR},
      </if>
      <if test="calEndDt != null">
        #{calEndDt,jdbcType=VARCHAR},
      </if>
      <if test="formerCalRate != null">
        #{formerCalRate,jdbcType=DECIMAL},
      </if>
      <if test="newlyCalRate != null">
        #{newlyCalRate,jdbcType=DECIMAL},
      </if>
      <if test="lockDurationAmt != null">
        #{lockDurationAmt,jdbcType=DECIMAL},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="lastAuditStatus != null">
        #{lastAuditStatus,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTimestamp != null">
        #{modifyTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="auditor != null">
        #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="auditTimestamp != null">
        #{auditTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.cs.task.model.CmStockSplitConfig">
    <!--@mbg.generated-->
    update CM_STOCK_SPLIT_CONFIG
    <set>
      <if test="configType != null">
        CONFIG_TYPE = #{configType,jdbcType=VARCHAR},
      </if>
      <if test="formerOrgCode != null">
        FORMER_ORG_CODE = #{formerOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="formerConsCode != null">
        FORMER_CONS_CODE = #{formerConsCode,jdbcType=VARCHAR},
      </if>
      <if test="newlyOrgCode != null">
        NEWLY_ORG_CODE = #{newlyOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="newlyConsCode != null">
        NEWLY_CONS_CODE = #{newlyConsCode,jdbcType=VARCHAR},
      </if>
      <if test="configLevel != null">
        CONFIG_LEVEL = #{configLevel,jdbcType=VARCHAR},
      </if>
      <if test="activeDt != null">
        ACTIVE_DT = #{activeDt,jdbcType=VARCHAR},
      </if>
      <if test="activeFlag != null">
        ACTIVE_FLAG = #{activeFlag,jdbcType=VARCHAR},
      </if>
      <if test="validFlag != null">
        VALID_FLAG = #{validFlag,jdbcType=VARCHAR},
      </if>
      <if test="calStartDt != null">
        CAL_START_DT = #{calStartDt,jdbcType=VARCHAR},
      </if>
      <if test="calEndDt != null">
        CAL_END_DT = #{calEndDt,jdbcType=VARCHAR},
      </if>
      <if test="formerCalRate != null">
        FORMER_CAL_RATE = #{formerCalRate,jdbcType=DECIMAL},
      </if>
      <if test="newlyCalRate != null">
        NEWLY_CAL_RATE = #{newlyCalRate,jdbcType=DECIMAL},
      </if>
      <if test="lockDurationAmt != null">
        LOCK_DURATION_AMT = #{lockDurationAmt,jdbcType=DECIMAL},
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="lastAuditStatus != null">
        LAST_AUDIT_STATUS = #{lastAuditStatus,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTimestamp != null">
        MODIFY_TIMESTAMP = #{modifyTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="auditor != null">
        AUDITOR = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="auditTimestamp != null">
        AUDIT_TIMESTAMP = #{auditTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="recStat != null">
        REC_STAT = #{recStat,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.cs.task.model.CmStockSplitConfig">
    <!--@mbg.generated-->
    update CM_STOCK_SPLIT_CONFIG
    set CONFIG_TYPE = #{configType,jdbcType=VARCHAR},
      FORMER_ORG_CODE = #{formerOrgCode,jdbcType=VARCHAR},
      FORMER_CONS_CODE = #{formerConsCode,jdbcType=VARCHAR},
      NEWLY_ORG_CODE = #{newlyOrgCode,jdbcType=VARCHAR},
      NEWLY_CONS_CODE = #{newlyConsCode,jdbcType=VARCHAR},
      CONFIG_LEVEL = #{configLevel,jdbcType=VARCHAR},
      ACTIVE_DT = #{activeDt,jdbcType=VARCHAR},
      ACTIVE_FLAG = #{activeFlag,jdbcType=VARCHAR},
      VALID_FLAG = #{validFlag,jdbcType=VARCHAR},
      CAL_START_DT = #{calStartDt,jdbcType=VARCHAR},
      CAL_END_DT = #{calEndDt,jdbcType=VARCHAR},
      FORMER_CAL_RATE = #{formerCalRate,jdbcType=DECIMAL},
      NEWLY_CAL_RATE = #{newlyCalRate,jdbcType=DECIMAL},
      LOCK_DURATION_AMT = #{lockDurationAmt,jdbcType=DECIMAL},
      AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR},
      LAST_AUDIT_STATUS = #{lastAuditStatus,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODIFY_TIMESTAMP = #{modifyTimestamp,jdbcType=TIMESTAMP},
      AUDITOR = #{auditor,jdbcType=VARCHAR},
      AUDIT_TIMESTAMP = #{auditTimestamp,jdbcType=TIMESTAMP},
      REC_STAT = #{recStat,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <!-- 从临时表插入状态有效的配置数据到正式表 -->
  <insert id="insertValidConfigsFromTemp" parameterType="map" useGeneratedKeys="false">
    INSERT INTO CM_STOCK_SPLIT_CONFIG (
      ID, CONFIG_TYPE, FORMER_ORG_CODE, FORMER_CONS_CODE, NEWLY_ORG_CODE, NEWLY_CONS_CODE,
      CONFIG_LEVEL, ACTIVE_DT, ACTIVE_FLAG, VALID_FLAG, CAL_START_DT, CAL_END_DT,
      FORMER_CAL_RATE, NEWLY_CAL_RATE, LOCK_DURATION_AMT, AUDIT_STATUS, LAST_AUDIT_STATUS,
      REMARK, CREATOR, CREATE_TIMESTAMP, MODIFIER, MODIFY_TIMESTAMP, AUDITOR, AUDIT_TIMESTAMP, REC_STAT
    )
    select a.ID, a.CONFIG_TYPE, a.FORMER_ORG_CODE, a.FORMER_CONS_CODE, a.NEWLY_ORG_CODE, a.NEWLY_CONS_CODE,
      a.CONFIG_LEVEL, a.ACTIVE_DT, a.ACTIVE_FLAG, a.VALID_FLAG, a.CAL_START_DT, a.CAL_END_DT,
      a.FORMER_CAL_RATE, a.NEWLY_CAL_RATE, a.LOCK_DURATION_AMT, a.AUDIT_STATUS, a.LAST_AUDIT_STATUS,
      a.REMARK, a.CREATOR, a.CREATE_TIMESTAMP, a.MODIFIER, a.MODIFY_TIMESTAMP, a.AUDITOR, a.AUDIT_TIMESTAMP, a.REC_STAT
    from (
    SELECT
      ID, CONFIG_TYPE, FORMER_ORG_CODE, FORMER_CONS_CODE, NEWLY_ORG_CODE, NEWLY_CONS_CODE,
      CONFIG_LEVEL, ACTIVE_DT, ACTIVE_FLAG, VALID_FLAG, CAL_START_DT, CAL_END_DT,
      FORMER_CAL_RATE, NEWLY_CAL_RATE, LOCK_DURATION_AMT, AUDIT_STATUS, LAST_AUDIT_STATUS,
      REMARK, CREATOR, CREATE_TIMESTAMP, MODIFIER, MODIFY_TIMESTAMP, AUDITOR, AUDIT_TIMESTAMP, REC_STAT
    FROM CM_STOCK_SPLIT_CONFIG_TMP t
    WHERE
    t.REC_STAT = '1'
      AND t.VALID_FLAG = '1'
      AND t.CONFIG_TYPE = '10'
      AND t.CAL_START_DT between #{startDate,jdbcType=VARCHAR} AND #{endDate,jdbcType=VARCHAR}
    ) a
  </insert>
</mapper>