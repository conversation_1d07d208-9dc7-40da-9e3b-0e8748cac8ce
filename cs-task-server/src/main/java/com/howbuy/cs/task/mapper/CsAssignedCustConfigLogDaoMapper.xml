<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.cs.task.dao.CsAssignedCustConfigLogDao">

      <insert id="insertCsAssignedCustConfigLog" useGeneratedKeys="false">
         insert into CS_ASSIGNED_CUST_CONFIG_LOG(id,assignedcustid,custconstanthis,waitid,conscustno,custname,orgcode,orgname,procode,citycode,createdt,modifydt,modifier,creator,stat,
                 conscode,consname,tasktype,statistictype,custsmsflag,invsttype,idtype,idnomask,idnodigest,idnocipher,custmobilemask,custmobiledigest,custmobilecipher,assignedcustcreatedt,specialmark,orgconfigid)
         (   select SEQ_CS_ASSIGNED_CUST_CONFIG.nextval,id,custconstanthis,waitid,conscustno,custname,orgcode,orgname,procode,citycode,systimestamp,modifydt,modifier,creator,stat,
                 conscode,consname,tasktype,statistictype,custsmsflag,invsttype,idtype,idnomask,idnodigest,idnocipher,custmobilemask,custmobiledigest,custmobilecipher,createdt,specialmark,orgconfigid
             from CS_ASSIGNED_CUST_CONFIG_MSG 
             where stat in('2','3') 
         )
    </insert>    
    
</mapper>



