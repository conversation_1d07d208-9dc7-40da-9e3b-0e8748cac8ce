<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.cs.task.dao.SyncBpFundBasicInfoMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.cs.task.model.SyncBpFundBasicInfo">
    <!--@mbg.generated-->
    <!--@Table SYNC_BP_FUND_BASIC_INFO-->
    <id column="FUND_CODE" jdbcType="VARCHAR" property="fundCode" />
    <result column="FUND_NAME" jdbcType="VARCHAR" property="fundName" />
    <result column="TA_CODE" jdbcType="VARCHAR" property="taCode" />
    <result column="FUND_MAN_CODE" jdbcType="VARCHAR" property="fundManCode" />
    <result column="CATEGORY_ID" jdbcType="VARCHAR" property="categoryId" />
    <result column="CURRENCY" jdbcType="VARCHAR" property="currency" />
    <result column="DFLT_DIV_MODE" jdbcType="VARCHAR" property="dfltDivMode" />
    <result column="CHG_TRUSTEE_MODE" jdbcType="VARCHAR" property="chgTrusteeMode" />
    <result column="START_TM" jdbcType="VARCHAR" property="startTm" />
    <result column="END_TM" jdbcType="VARCHAR" property="endTm" />
    <result column="SUPPLE_SUBS_RULE" jdbcType="VARCHAR" property="suppleSubsRule" />
    <result column="FACE_VALUE" jdbcType="DECIMAL" property="faceValue" />
    <result column="FEE_CAL_MODE" jdbcType="VARCHAR" property="feeCalMode" />
    <result column="MIN_ACCT_VOL" jdbcType="DECIMAL" property="minAcctVol" />
    <result column="FUND_RISK_LEVEL" jdbcType="VARCHAR" property="fundRiskLevel" />
    <result column="REC_STAT" jdbcType="VARCHAR" property="recStat" />
    <result column="CHECK_FLAG" jdbcType="VARCHAR" property="checkFlag" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="CHECKER" jdbcType="VARCHAR" property="checker" />
    <result column="CRE_DT" jdbcType="VARCHAR" property="creDt" />
    <result column="MOD_DT" jdbcType="VARCHAR" property="modDt" />
    <result column="FUND_TYPE" jdbcType="VARCHAR" property="fundType" />
    <result column="FUND_ATTR_PINYIN" jdbcType="VARCHAR" property="fundAttrPinyin" />
    <result column="IPO_END_TM" jdbcType="VARCHAR" property="ipoEndTm" />
    <result column="MAIN_FUND_CODE" jdbcType="VARCHAR" property="mainFundCode" />
    <result column="DISTRIBUTE_SIZE" jdbcType="DECIMAL" property="distributeSize" />
    <result column="IPO_START_DT" jdbcType="VARCHAR" property="ipoStartDt" />
    <result column="IPO_END_DT" jdbcType="VARCHAR" property="ipoEndDt" />
    <result column="ESTABLISH_DT" jdbcType="VARCHAR" property="establishDt" />
    <result column="FUND_CUSTODIAN_CODE" jdbcType="VARCHAR" property="fundCustodianCode" />
    <result column="SYNC_DATE" jdbcType="TIMESTAMP" property="syncDate" />
    <result column="FUND_ATTR" jdbcType="VARCHAR" property="fundAttr" />
    <result column="PART_CODE" jdbcType="VARCHAR" property="partCode" />
    <result column="FUND_ATTR_HB" jdbcType="VARCHAR" property="fundAttrHb" />
    <result column="FUND_SUB_TYPE" jdbcType="VARCHAR" property="fundSubType" />
    <result column="REDE_OPEN_TERM" jdbcType="VARCHAR" property="redeOpenTerm" />
    <result column="SUMMARY" jdbcType="VARCHAR" property="summary" />
    <result column="FUND_OPEN_MODE" jdbcType="VARCHAR" property="fundOpenMode" />
    <result column="YIELD_TYPE" jdbcType="VARCHAR" property="yieldType" />
    <result column="RECOMM_INFO" jdbcType="VARCHAR" property="recommInfo" />
    <result column="FUND_CLASS" jdbcType="VARCHAR" property="fundClass" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    FUND_CODE, FUND_NAME, TA_CODE, FUND_MAN_CODE, CATEGORY_ID, CURRENCY, DFLT_DIV_MODE, 
    CHG_TRUSTEE_MODE, START_TM, END_TM, SUPPLE_SUBS_RULE, FACE_VALUE, FEE_CAL_MODE, MIN_ACCT_VOL, 
    FUND_RISK_LEVEL, REC_STAT, CHECK_FLAG, CREATOR, MODIFIER, CHECKER, CRE_DT, MOD_DT, 
    FUND_TYPE, FUND_ATTR_PINYIN, IPO_END_TM, MAIN_FUND_CODE, DISTRIBUTE_SIZE, IPO_START_DT, 
    IPO_END_DT, ESTABLISH_DT, FUND_CUSTODIAN_CODE, SYNC_DATE, FUND_ATTR, PART_CODE, FUND_ATTR_HB, 
    FUND_SUB_TYPE, REDE_OPEN_TERM, SUMMARY, FUND_OPEN_MODE, YIELD_TYPE, RECOMM_INFO, 
    FUND_CLASS
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from SYNC_BP_FUND_BASIC_INFO
    where FUND_CODE = #{fundCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from SYNC_BP_FUND_BASIC_INFO
    where FUND_CODE = #{fundCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.cs.task.model.SyncBpFundBasicInfo">
    <!--@mbg.generated-->
    insert into SYNC_BP_FUND_BASIC_INFO (FUND_CODE, FUND_NAME, TA_CODE, 
      FUND_MAN_CODE, CATEGORY_ID, CURRENCY, 
      DFLT_DIV_MODE, CHG_TRUSTEE_MODE, START_TM, 
      END_TM, SUPPLE_SUBS_RULE, FACE_VALUE, 
      FEE_CAL_MODE, MIN_ACCT_VOL, FUND_RISK_LEVEL, 
      REC_STAT, CHECK_FLAG, CREATOR, 
      MODIFIER, CHECKER, CRE_DT, 
      MOD_DT, FUND_TYPE, FUND_ATTR_PINYIN, 
      IPO_END_TM, MAIN_FUND_CODE, DISTRIBUTE_SIZE, 
      IPO_START_DT, IPO_END_DT, ESTABLISH_DT, 
      FUND_CUSTODIAN_CODE, SYNC_DATE, FUND_ATTR, 
      PART_CODE, FUND_ATTR_HB, FUND_SUB_TYPE, 
      REDE_OPEN_TERM, SUMMARY, FUND_OPEN_MODE, 
      YIELD_TYPE, RECOMM_INFO, FUND_CLASS
      )
    values (#{fundCode,jdbcType=VARCHAR}, #{fundName,jdbcType=VARCHAR}, #{taCode,jdbcType=VARCHAR}, 
      #{fundManCode,jdbcType=VARCHAR}, #{categoryId,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR}, 
      #{dfltDivMode,jdbcType=VARCHAR}, #{chgTrusteeMode,jdbcType=VARCHAR}, #{startTm,jdbcType=VARCHAR}, 
      #{endTm,jdbcType=VARCHAR}, #{suppleSubsRule,jdbcType=VARCHAR}, #{faceValue,jdbcType=DECIMAL}, 
      #{feeCalMode,jdbcType=VARCHAR}, #{minAcctVol,jdbcType=DECIMAL}, #{fundRiskLevel,jdbcType=VARCHAR}, 
      #{recStat,jdbcType=VARCHAR}, #{checkFlag,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{checker,jdbcType=VARCHAR}, #{creDt,jdbcType=VARCHAR}, 
      #{modDt,jdbcType=VARCHAR}, #{fundType,jdbcType=VARCHAR}, #{fundAttrPinyin,jdbcType=VARCHAR}, 
      #{ipoEndTm,jdbcType=VARCHAR}, #{mainFundCode,jdbcType=VARCHAR}, #{distributeSize,jdbcType=DECIMAL}, 
      #{ipoStartDt,jdbcType=VARCHAR}, #{ipoEndDt,jdbcType=VARCHAR}, #{establishDt,jdbcType=VARCHAR}, 
      #{fundCustodianCode,jdbcType=VARCHAR}, #{syncDate,jdbcType=TIMESTAMP}, #{fundAttr,jdbcType=VARCHAR}, 
      #{partCode,jdbcType=VARCHAR}, #{fundAttrHb,jdbcType=VARCHAR}, #{fundSubType,jdbcType=VARCHAR}, 
      #{redeOpenTerm,jdbcType=VARCHAR}, #{summary,jdbcType=VARCHAR}, #{fundOpenMode,jdbcType=VARCHAR}, 
      #{yieldType,jdbcType=VARCHAR}, #{recommInfo,jdbcType=VARCHAR}, #{fundClass,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.cs.task.model.SyncBpFundBasicInfo">
    <!--@mbg.generated-->
    insert into SYNC_BP_FUND_BASIC_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fundCode != null">
        FUND_CODE,
      </if>
      <if test="fundName != null">
        FUND_NAME,
      </if>
      <if test="taCode != null">
        TA_CODE,
      </if>
      <if test="fundManCode != null">
        FUND_MAN_CODE,
      </if>
      <if test="categoryId != null">
        CATEGORY_ID,
      </if>
      <if test="currency != null">
        CURRENCY,
      </if>
      <if test="dfltDivMode != null">
        DFLT_DIV_MODE,
      </if>
      <if test="chgTrusteeMode != null">
        CHG_TRUSTEE_MODE,
      </if>
      <if test="startTm != null">
        START_TM,
      </if>
      <if test="endTm != null">
        END_TM,
      </if>
      <if test="suppleSubsRule != null">
        SUPPLE_SUBS_RULE,
      </if>
      <if test="faceValue != null">
        FACE_VALUE,
      </if>
      <if test="feeCalMode != null">
        FEE_CAL_MODE,
      </if>
      <if test="minAcctVol != null">
        MIN_ACCT_VOL,
      </if>
      <if test="fundRiskLevel != null">
        FUND_RISK_LEVEL,
      </if>
      <if test="recStat != null">
        REC_STAT,
      </if>
      <if test="checkFlag != null">
        CHECK_FLAG,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="creDt != null">
        CRE_DT,
      </if>
      <if test="modDt != null">
        MOD_DT,
      </if>
      <if test="fundType != null">
        FUND_TYPE,
      </if>
      <if test="fundAttrPinyin != null">
        FUND_ATTR_PINYIN,
      </if>
      <if test="ipoEndTm != null">
        IPO_END_TM,
      </if>
      <if test="mainFundCode != null">
        MAIN_FUND_CODE,
      </if>
      <if test="distributeSize != null">
        DISTRIBUTE_SIZE,
      </if>
      <if test="ipoStartDt != null">
        IPO_START_DT,
      </if>
      <if test="ipoEndDt != null">
        IPO_END_DT,
      </if>
      <if test="establishDt != null">
        ESTABLISH_DT,
      </if>
      <if test="fundCustodianCode != null">
        FUND_CUSTODIAN_CODE,
      </if>
      <if test="syncDate != null">
        SYNC_DATE,
      </if>
      <if test="fundAttr != null">
        FUND_ATTR,
      </if>
      <if test="partCode != null">
        PART_CODE,
      </if>
      <if test="fundAttrHb != null">
        FUND_ATTR_HB,
      </if>
      <if test="fundSubType != null">
        FUND_SUB_TYPE,
      </if>
      <if test="redeOpenTerm != null">
        REDE_OPEN_TERM,
      </if>
      <if test="summary != null">
        SUMMARY,
      </if>
      <if test="fundOpenMode != null">
        FUND_OPEN_MODE,
      </if>
      <if test="yieldType != null">
        YIELD_TYPE,
      </if>
      <if test="recommInfo != null">
        RECOMM_INFO,
      </if>
      <if test="fundClass != null">
        FUND_CLASS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fundCode != null">
        #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="fundName != null">
        #{fundName,jdbcType=VARCHAR},
      </if>
      <if test="taCode != null">
        #{taCode,jdbcType=VARCHAR},
      </if>
      <if test="fundManCode != null">
        #{fundManCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="dfltDivMode != null">
        #{dfltDivMode,jdbcType=VARCHAR},
      </if>
      <if test="chgTrusteeMode != null">
        #{chgTrusteeMode,jdbcType=VARCHAR},
      </if>
      <if test="startTm != null">
        #{startTm,jdbcType=VARCHAR},
      </if>
      <if test="endTm != null">
        #{endTm,jdbcType=VARCHAR},
      </if>
      <if test="suppleSubsRule != null">
        #{suppleSubsRule,jdbcType=VARCHAR},
      </if>
      <if test="faceValue != null">
        #{faceValue,jdbcType=DECIMAL},
      </if>
      <if test="feeCalMode != null">
        #{feeCalMode,jdbcType=VARCHAR},
      </if>
      <if test="minAcctVol != null">
        #{minAcctVol,jdbcType=DECIMAL},
      </if>
      <if test="fundRiskLevel != null">
        #{fundRiskLevel,jdbcType=VARCHAR},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="checkFlag != null">
        #{checkFlag,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=VARCHAR},
      </if>
      <if test="creDt != null">
        #{creDt,jdbcType=VARCHAR},
      </if>
      <if test="modDt != null">
        #{modDt,jdbcType=VARCHAR},
      </if>
      <if test="fundType != null">
        #{fundType,jdbcType=VARCHAR},
      </if>
      <if test="fundAttrPinyin != null">
        #{fundAttrPinyin,jdbcType=VARCHAR},
      </if>
      <if test="ipoEndTm != null">
        #{ipoEndTm,jdbcType=VARCHAR},
      </if>
      <if test="mainFundCode != null">
        #{mainFundCode,jdbcType=VARCHAR},
      </if>
      <if test="distributeSize != null">
        #{distributeSize,jdbcType=DECIMAL},
      </if>
      <if test="ipoStartDt != null">
        #{ipoStartDt,jdbcType=VARCHAR},
      </if>
      <if test="ipoEndDt != null">
        #{ipoEndDt,jdbcType=VARCHAR},
      </if>
      <if test="establishDt != null">
        #{establishDt,jdbcType=VARCHAR},
      </if>
      <if test="fundCustodianCode != null">
        #{fundCustodianCode,jdbcType=VARCHAR},
      </if>
      <if test="syncDate != null">
        #{syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="fundAttr != null">
        #{fundAttr,jdbcType=VARCHAR},
      </if>
      <if test="partCode != null">
        #{partCode,jdbcType=VARCHAR},
      </if>
      <if test="fundAttrHb != null">
        #{fundAttrHb,jdbcType=VARCHAR},
      </if>
      <if test="fundSubType != null">
        #{fundSubType,jdbcType=VARCHAR},
      </if>
      <if test="redeOpenTerm != null">
        #{redeOpenTerm,jdbcType=VARCHAR},
      </if>
      <if test="summary != null">
        #{summary,jdbcType=VARCHAR},
      </if>
      <if test="fundOpenMode != null">
        #{fundOpenMode,jdbcType=VARCHAR},
      </if>
      <if test="yieldType != null">
        #{yieldType,jdbcType=VARCHAR},
      </if>
      <if test="recommInfo != null">
        #{recommInfo,jdbcType=VARCHAR},
      </if>
      <if test="fundClass != null">
        #{fundClass,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.cs.task.model.SyncBpFundBasicInfo">
    <!--@mbg.generated-->
    update SYNC_BP_FUND_BASIC_INFO
    <set>
      <if test="fundName != null">
        FUND_NAME = #{fundName,jdbcType=VARCHAR},
      </if>
      <if test="taCode != null">
        TA_CODE = #{taCode,jdbcType=VARCHAR},
      </if>
      <if test="fundManCode != null">
        FUND_MAN_CODE = #{fundManCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        CATEGORY_ID = #{categoryId,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        CURRENCY = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="dfltDivMode != null">
        DFLT_DIV_MODE = #{dfltDivMode,jdbcType=VARCHAR},
      </if>
      <if test="chgTrusteeMode != null">
        CHG_TRUSTEE_MODE = #{chgTrusteeMode,jdbcType=VARCHAR},
      </if>
      <if test="startTm != null">
        START_TM = #{startTm,jdbcType=VARCHAR},
      </if>
      <if test="endTm != null">
        END_TM = #{endTm,jdbcType=VARCHAR},
      </if>
      <if test="suppleSubsRule != null">
        SUPPLE_SUBS_RULE = #{suppleSubsRule,jdbcType=VARCHAR},
      </if>
      <if test="faceValue != null">
        FACE_VALUE = #{faceValue,jdbcType=DECIMAL},
      </if>
      <if test="feeCalMode != null">
        FEE_CAL_MODE = #{feeCalMode,jdbcType=VARCHAR},
      </if>
      <if test="minAcctVol != null">
        MIN_ACCT_VOL = #{minAcctVol,jdbcType=DECIMAL},
      </if>
      <if test="fundRiskLevel != null">
        FUND_RISK_LEVEL = #{fundRiskLevel,jdbcType=VARCHAR},
      </if>
      <if test="recStat != null">
        REC_STAT = #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="checkFlag != null">
        CHECK_FLAG = #{checkFlag,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="checker != null">
        CHECKER = #{checker,jdbcType=VARCHAR},
      </if>
      <if test="creDt != null">
        CRE_DT = #{creDt,jdbcType=VARCHAR},
      </if>
      <if test="modDt != null">
        MOD_DT = #{modDt,jdbcType=VARCHAR},
      </if>
      <if test="fundType != null">
        FUND_TYPE = #{fundType,jdbcType=VARCHAR},
      </if>
      <if test="fundAttrPinyin != null">
        FUND_ATTR_PINYIN = #{fundAttrPinyin,jdbcType=VARCHAR},
      </if>
      <if test="ipoEndTm != null">
        IPO_END_TM = #{ipoEndTm,jdbcType=VARCHAR},
      </if>
      <if test="mainFundCode != null">
        MAIN_FUND_CODE = #{mainFundCode,jdbcType=VARCHAR},
      </if>
      <if test="distributeSize != null">
        DISTRIBUTE_SIZE = #{distributeSize,jdbcType=DECIMAL},
      </if>
      <if test="ipoStartDt != null">
        IPO_START_DT = #{ipoStartDt,jdbcType=VARCHAR},
      </if>
      <if test="ipoEndDt != null">
        IPO_END_DT = #{ipoEndDt,jdbcType=VARCHAR},
      </if>
      <if test="establishDt != null">
        ESTABLISH_DT = #{establishDt,jdbcType=VARCHAR},
      </if>
      <if test="fundCustodianCode != null">
        FUND_CUSTODIAN_CODE = #{fundCustodianCode,jdbcType=VARCHAR},
      </if>
      <if test="syncDate != null">
        SYNC_DATE = #{syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="fundAttr != null">
        FUND_ATTR = #{fundAttr,jdbcType=VARCHAR},
      </if>
      <if test="partCode != null">
        PART_CODE = #{partCode,jdbcType=VARCHAR},
      </if>
      <if test="fundAttrHb != null">
        FUND_ATTR_HB = #{fundAttrHb,jdbcType=VARCHAR},
      </if>
      <if test="fundSubType != null">
        FUND_SUB_TYPE = #{fundSubType,jdbcType=VARCHAR},
      </if>
      <if test="redeOpenTerm != null">
        REDE_OPEN_TERM = #{redeOpenTerm,jdbcType=VARCHAR},
      </if>
      <if test="summary != null">
        SUMMARY = #{summary,jdbcType=VARCHAR},
      </if>
      <if test="fundOpenMode != null">
        FUND_OPEN_MODE = #{fundOpenMode,jdbcType=VARCHAR},
      </if>
      <if test="yieldType != null">
        YIELD_TYPE = #{yieldType,jdbcType=VARCHAR},
      </if>
      <if test="recommInfo != null">
        RECOMM_INFO = #{recommInfo,jdbcType=VARCHAR},
      </if>
      <if test="fundClass != null">
        FUND_CLASS = #{fundClass,jdbcType=VARCHAR},
      </if>
    </set>
    where FUND_CODE = #{fundCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.cs.task.model.SyncBpFundBasicInfo">
    <!--@mbg.generated-->
    update SYNC_BP_FUND_BASIC_INFO
    set FUND_NAME = #{fundName,jdbcType=VARCHAR},
      TA_CODE = #{taCode,jdbcType=VARCHAR},
      FUND_MAN_CODE = #{fundManCode,jdbcType=VARCHAR},
      CATEGORY_ID = #{categoryId,jdbcType=VARCHAR},
      CURRENCY = #{currency,jdbcType=VARCHAR},
      DFLT_DIV_MODE = #{dfltDivMode,jdbcType=VARCHAR},
      CHG_TRUSTEE_MODE = #{chgTrusteeMode,jdbcType=VARCHAR},
      START_TM = #{startTm,jdbcType=VARCHAR},
      END_TM = #{endTm,jdbcType=VARCHAR},
      SUPPLE_SUBS_RULE = #{suppleSubsRule,jdbcType=VARCHAR},
      FACE_VALUE = #{faceValue,jdbcType=DECIMAL},
      FEE_CAL_MODE = #{feeCalMode,jdbcType=VARCHAR},
      MIN_ACCT_VOL = #{minAcctVol,jdbcType=DECIMAL},
      FUND_RISK_LEVEL = #{fundRiskLevel,jdbcType=VARCHAR},
      REC_STAT = #{recStat,jdbcType=VARCHAR},
      CHECK_FLAG = #{checkFlag,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      CHECKER = #{checker,jdbcType=VARCHAR},
      CRE_DT = #{creDt,jdbcType=VARCHAR},
      MOD_DT = #{modDt,jdbcType=VARCHAR},
      FUND_TYPE = #{fundType,jdbcType=VARCHAR},
      FUND_ATTR_PINYIN = #{fundAttrPinyin,jdbcType=VARCHAR},
      IPO_END_TM = #{ipoEndTm,jdbcType=VARCHAR},
      MAIN_FUND_CODE = #{mainFundCode,jdbcType=VARCHAR},
      DISTRIBUTE_SIZE = #{distributeSize,jdbcType=DECIMAL},
      IPO_START_DT = #{ipoStartDt,jdbcType=VARCHAR},
      IPO_END_DT = #{ipoEndDt,jdbcType=VARCHAR},
      ESTABLISH_DT = #{establishDt,jdbcType=VARCHAR},
      FUND_CUSTODIAN_CODE = #{fundCustodianCode,jdbcType=VARCHAR},
      SYNC_DATE = #{syncDate,jdbcType=TIMESTAMP},
      FUND_ATTR = #{fundAttr,jdbcType=VARCHAR},
      PART_CODE = #{partCode,jdbcType=VARCHAR},
      FUND_ATTR_HB = #{fundAttrHb,jdbcType=VARCHAR},
      FUND_SUB_TYPE = #{fundSubType,jdbcType=VARCHAR},
      REDE_OPEN_TERM = #{redeOpenTerm,jdbcType=VARCHAR},
      SUMMARY = #{summary,jdbcType=VARCHAR},
      FUND_OPEN_MODE = #{fundOpenMode,jdbcType=VARCHAR},
      YIELD_TYPE = #{yieldType,jdbcType=VARCHAR},
      RECOMM_INFO = #{recommInfo,jdbcType=VARCHAR},
      FUND_CLASS = #{fundClass,jdbcType=VARCHAR}
    where FUND_CODE = #{fundCode,jdbcType=VARCHAR}
  </update>

  <insert id="insertFromJJXX1" parameterType="Map" useGeneratedKeys="false">
    insert into sync_bp_fund_basic_info(fund_code,fund_name,ta_code,fund_man_code,CURRENCY,FUND_ATTR,DFLT_DIV_MODE,
                                        CHG_TRUSTEE_MODE,START_TM,END_TM,SUPPLE_SUBS_RULE,REC_STAT,CHECK_FLAG,CREATOR,CRE_DT,FUND_ATTR_HB)
    (select t.jjdm,t.jjmc,'99' as ta_code,'010' fund_man_code,'156' as CURRENCY1,
           t.jjjc as FUND_ATTR,
           '1' as DFLT_DIV_MODE,'1' as CHG_TRUSTEE_MODE,'093000' as START_TM,
           '150000' as END_TM,'1110' as SUPPLE_SUBS_RULE,'0' as REC_STAT,
           '1' as CHECK_FLAG,'sys' as CREATOR1, #{createDate} as CRE_DT,
           t.jjjc as FUND_ATTR_HB
    from jjxx1 t where cpfl = '2' and jjdm not in(
      select fund_code from sync_bp_fund_basic_info
    ))
  </insert>
</mapper>