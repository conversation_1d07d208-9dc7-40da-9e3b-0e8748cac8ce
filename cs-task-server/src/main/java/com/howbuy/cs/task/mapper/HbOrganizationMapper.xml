<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.cs.task.dao.HbOrganizationMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.cs.task.model.HbOrganization">
    <!--@mbg.generated-->
    <!--@Table HB_ORGANIZATION-->
    <id column="ORGCODE" jdbcType="VARCHAR" property="orgcode" />
    <result column="ORGNAME" jdbcType="VARCHAR" property="orgname" />
    <result column="ENGLISHNAME" jdbcType="VARCHAR" property="englishname" />
    <result column="PARENTORGCODE" jdbcType="VARCHAR" property="parentorgcode" />
    <result column="SORT" jdbcType="DECIMAL" property="sort" />
    <result column="ORGTYPE" jdbcType="VARCHAR" property="orgtype" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="SHOWFLAG" jdbcType="VARCHAR" property="showflag" />
    <result column="PROVINCE" jdbcType="VARCHAR" property="province" />
    <result column="PROVINCENAME" jdbcType="VARCHAR" property="provincename" />
    <result column="CITY" jdbcType="VARCHAR" property="city" />
    <result column="CITYNAME" jdbcType="VARCHAR" property="cityname" />
    <result column="AREA" jdbcType="VARCHAR" property="area" />
    <result column="AREANAME" jdbcType="VARCHAR" property="areaname" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="RECSTAT" jdbcType="VARCHAR" property="recstat" />
    <result column="CHECKFLAG" jdbcType="VARCHAR" property="checkflag" />
    <result column="TELNO" jdbcType="VARCHAR" property="telno" />
    <result column="FAX" jdbcType="VARCHAR" property="fax" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="CHECKER" jdbcType="VARCHAR" property="checker" />
    <result column="CREDATE" jdbcType="TIMESTAMP" property="credate" />
    <result column="MODDATE" jdbcType="TIMESTAMP" property="moddate" />
    <result column="CHECKDATE" jdbcType="TIMESTAMP" property="checkdate" />
    <result column="SYSCODE" jdbcType="VARCHAR" property="syscode" />
    <result column="SYSNAME" jdbcType="VARCHAR" property="sysname" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ORGCODE, ORGNAME, ENGLISHNAME, PARENTORGCODE, SORT, ORGTYPE, "STATUS", SHOWFLAG, 
    PROVINCE, PROVINCENAME, CITY, CITYNAME, AREA, AREANAME, ADDRESS, RECSTAT, CHECKFLAG, 
    TELNO, FAX, CREATOR, MODIFIER, CHECKER, CREDATE, MODDATE, CHECKDATE, SYSCODE, SYSNAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from HB_ORGANIZATION
    where ORGCODE = #{orgcode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from HB_ORGANIZATION
    where ORGCODE = #{orgcode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.cs.task.model.HbOrganization">
    <!--@mbg.generated-->
    insert into HB_ORGANIZATION (ORGCODE, ORGNAME, ENGLISHNAME, 
      PARENTORGCODE, SORT, ORGTYPE, 
      "STATUS", SHOWFLAG, PROVINCE, 
      PROVINCENAME, CITY, CITYNAME, 
      AREA, AREANAME, ADDRESS, 
      RECSTAT, CHECKFLAG, TELNO, 
      FAX, CREATOR, MODIFIER, 
      CHECKER, CREDATE, MODDATE, 
      CHECKDATE, SYSCODE, SYSNAME
      )
    values (#{orgcode,jdbcType=VARCHAR}, #{orgname,jdbcType=VARCHAR}, #{englishname,jdbcType=VARCHAR}, 
      #{parentorgcode,jdbcType=VARCHAR}, #{sort,jdbcType=DECIMAL}, #{orgtype,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{showflag,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, 
      #{provincename,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{cityname,jdbcType=VARCHAR}, 
      #{area,jdbcType=VARCHAR}, #{areaname,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{recstat,jdbcType=VARCHAR}, #{checkflag,jdbcType=VARCHAR}, #{telno,jdbcType=VARCHAR}, 
      #{fax,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{checker,jdbcType=VARCHAR}, #{credate,jdbcType=TIMESTAMP}, #{moddate,jdbcType=TIMESTAMP}, 
      #{checkdate,jdbcType=TIMESTAMP}, #{syscode,jdbcType=VARCHAR}, #{sysname,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.cs.task.model.HbOrganization">
    <!--@mbg.generated-->
    insert into HB_ORGANIZATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orgcode != null">
        ORGCODE,
      </if>
      <if test="orgname != null">
        ORGNAME,
      </if>
      <if test="englishname != null">
        ENGLISHNAME,
      </if>
      <if test="parentorgcode != null">
        PARENTORGCODE,
      </if>
      <if test="sort != null">
        SORT,
      </if>
      <if test="orgtype != null">
        ORGTYPE,
      </if>
      <if test="status != null">
        "STATUS",
      </if>
      <if test="showflag != null">
        SHOWFLAG,
      </if>
      <if test="province != null">
        PROVINCE,
      </if>
      <if test="provincename != null">
        PROVINCENAME,
      </if>
      <if test="city != null">
        CITY,
      </if>
      <if test="cityname != null">
        CITYNAME,
      </if>
      <if test="area != null">
        AREA,
      </if>
      <if test="areaname != null">
        AREANAME,
      </if>
      <if test="address != null">
        ADDRESS,
      </if>
      <if test="recstat != null">
        RECSTAT,
      </if>
      <if test="checkflag != null">
        CHECKFLAG,
      </if>
      <if test="telno != null">
        TELNO,
      </if>
      <if test="fax != null">
        FAX,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="credate != null">
        CREDATE,
      </if>
      <if test="moddate != null">
        MODDATE,
      </if>
      <if test="checkdate != null">
        CHECKDATE,
      </if>
      <if test="syscode != null">
        SYSCODE,
      </if>
      <if test="sysname != null">
        SYSNAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orgcode != null">
        #{orgcode,jdbcType=VARCHAR},
      </if>
      <if test="orgname != null">
        #{orgname,jdbcType=VARCHAR},
      </if>
      <if test="englishname != null">
        #{englishname,jdbcType=VARCHAR},
      </if>
      <if test="parentorgcode != null">
        #{parentorgcode,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=DECIMAL},
      </if>
      <if test="orgtype != null">
        #{orgtype,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="showflag != null">
        #{showflag,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="provincename != null">
        #{provincename,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="cityname != null">
        #{cityname,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="areaname != null">
        #{areaname,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="recstat != null">
        #{recstat,jdbcType=VARCHAR},
      </if>
      <if test="checkflag != null">
        #{checkflag,jdbcType=VARCHAR},
      </if>
      <if test="telno != null">
        #{telno,jdbcType=VARCHAR},
      </if>
      <if test="fax != null">
        #{fax,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=VARCHAR},
      </if>
      <if test="credate != null">
        #{credate,jdbcType=TIMESTAMP},
      </if>
      <if test="moddate != null">
        #{moddate,jdbcType=TIMESTAMP},
      </if>
      <if test="checkdate != null">
        #{checkdate,jdbcType=TIMESTAMP},
      </if>
      <if test="syscode != null">
        #{syscode,jdbcType=VARCHAR},
      </if>
      <if test="sysname != null">
        #{sysname,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.cs.task.model.HbOrganization">
    <!--@mbg.generated-->
    update HB_ORGANIZATION
    <set>
      <if test="orgname != null">
        ORGNAME = #{orgname,jdbcType=VARCHAR},
      </if>
      <if test="englishname != null">
        ENGLISHNAME = #{englishname,jdbcType=VARCHAR},
      </if>
      <if test="parentorgcode != null">
        PARENTORGCODE = #{parentorgcode,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        SORT = #{sort,jdbcType=DECIMAL},
      </if>
      <if test="orgtype != null">
        ORGTYPE = #{orgtype,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "STATUS" = #{status,jdbcType=VARCHAR},
      </if>
      <if test="showflag != null">
        SHOWFLAG = #{showflag,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        PROVINCE = #{province,jdbcType=VARCHAR},
      </if>
      <if test="provincename != null">
        PROVINCENAME = #{provincename,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        CITY = #{city,jdbcType=VARCHAR},
      </if>
      <if test="cityname != null">
        CITYNAME = #{cityname,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        AREA = #{area,jdbcType=VARCHAR},
      </if>
      <if test="areaname != null">
        AREANAME = #{areaname,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="recstat != null">
        RECSTAT = #{recstat,jdbcType=VARCHAR},
      </if>
      <if test="checkflag != null">
        CHECKFLAG = #{checkflag,jdbcType=VARCHAR},
      </if>
      <if test="telno != null">
        TELNO = #{telno,jdbcType=VARCHAR},
      </if>
      <if test="fax != null">
        FAX = #{fax,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="checker != null">
        CHECKER = #{checker,jdbcType=VARCHAR},
      </if>
      <if test="credate != null">
        CREDATE = #{credate,jdbcType=TIMESTAMP},
      </if>
      <if test="moddate != null">
        MODDATE = #{moddate,jdbcType=TIMESTAMP},
      </if>
      <if test="checkdate != null">
        CHECKDATE = #{checkdate,jdbcType=TIMESTAMP},
      </if>
      <if test="syscode != null">
        SYSCODE = #{syscode,jdbcType=VARCHAR},
      </if>
      <if test="sysname != null">
        SYSNAME = #{sysname,jdbcType=VARCHAR},
      </if>
    </set>
    where ORGCODE = #{orgcode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.cs.task.model.HbOrganization">
    <!--@mbg.generated-->
    update HB_ORGANIZATION
    set ORGNAME = #{orgname,jdbcType=VARCHAR},
      ENGLISHNAME = #{englishname,jdbcType=VARCHAR},
      PARENTORGCODE = #{parentorgcode,jdbcType=VARCHAR},
      SORT = #{sort,jdbcType=DECIMAL},
      ORGTYPE = #{orgtype,jdbcType=VARCHAR},
      "STATUS" = #{status,jdbcType=VARCHAR},
      SHOWFLAG = #{showflag,jdbcType=VARCHAR},
      PROVINCE = #{province,jdbcType=VARCHAR},
      PROVINCENAME = #{provincename,jdbcType=VARCHAR},
      CITY = #{city,jdbcType=VARCHAR},
      CITYNAME = #{cityname,jdbcType=VARCHAR},
      AREA = #{area,jdbcType=VARCHAR},
      AREANAME = #{areaname,jdbcType=VARCHAR},
      ADDRESS = #{address,jdbcType=VARCHAR},
      RECSTAT = #{recstat,jdbcType=VARCHAR},
      CHECKFLAG = #{checkflag,jdbcType=VARCHAR},
      TELNO = #{telno,jdbcType=VARCHAR},
      FAX = #{fax,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      CHECKER = #{checker,jdbcType=VARCHAR},
      CREDATE = #{credate,jdbcType=TIMESTAMP},
      MODDATE = #{moddate,jdbcType=TIMESTAMP},
      CHECKDATE = #{checkdate,jdbcType=TIMESTAMP},
      SYSCODE = #{syscode,jdbcType=VARCHAR},
      SYSNAME = #{sysname,jdbcType=VARCHAR}
    where ORGCODE = #{orgcode,jdbcType=VARCHAR}
  </update>
  <select id="selectCenterLeaderUserId" resultType="java.lang.String">
    SELECT userid
    FROM cm_consultant_exp
    WHERE curmonthlevel = '29'
    AND outletcode = #{outletCode,jdbcType=VARCHAR}
    AND rownum = 1
  </select>

  <select id="selectOrgPathByOrgCode" resultMap="BaseResultMap">
    SELECT *
    FROM (
    SELECT
    t.*,
    LEVEL as tree_level,
    SYS_CONNECT_BY_PATH(ORGCODE, '/') as org_path
    FROM HB_ORGANIZATION t
    WHERE t.STATUS = '0'
    AND t.ORGCODE != '0'
    START WITH t.ORGCODE = #{orgCode, jdbcType=VARCHAR}
    CONNECT BY PRIOR t.PARENTORGCODE = t.ORGCODE
    ORDER SIBLINGS BY t.SORT
    )
    ORDER BY tree_level DESC
  </select>
</mapper>