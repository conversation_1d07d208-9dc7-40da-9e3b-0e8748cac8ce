/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.util;

import com.alibaba.fastjson.JSONObject;
import com.howbuy.cs.common.enums.CallDirectionEnum;
import com.howbuy.cs.common.enums.CallTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description: 统一日志打印工具类
 * @date 2025-04-01 11:28:48
 * @since JDK 1.8
 */
public class MainLogUtils {
    private static final Logger mainLogger = LogManager.getLogger("mainlog");

    private MainLogUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * @description: 打印接口调用日志
     * @param traceId 客户端标识
     * @param ranNo 调用随机号
     * @param remoteIp 远端IP
     * @param remoteName 远端应用名称
     * @param serviceName 接口名称
     * @param returnCode 返回码
     * @param cost 耗时
     * @param callType 调用类型（dubbo、mq、http）
     * @param direction 调用方向（in、out）
     * @return void
     * <AUTHOR>
     * @date 2025-04-01 11:28:48
     * @since JDK 1.8
     */
    public static void logInterfaceCall(String traceId, String ranNo, String remoteIp,
                                        String remoteName, String serviceName,
                                        String returnCode, long cost,
                                        CallTypeEnum callType, CallDirectionEnum direction) {
        if (mainLogger.isInfoEnabled()) {
            try {
                JSONObject logJson = new JSONObject();
                // 日志时间
                logJson.put("time", fmtDate(new Date(), "yyyy-MM-dd HH:mm:ss.SSS"));
                // 客户端标识
                logJson.put("traceId", StringUtils.defaultString(traceId));
                // 调用随机号
                logJson.put("ranNo", StringUtils.defaultString(ranNo));
                // 远端IP
                logJson.put("remoteIp", StringUtils.defaultString(remoteIp));
                // 远端应用名称
                logJson.put("remoteName", StringUtils.defaultString(remoteName));
                // 接口名称
                logJson.put("serviceName", StringUtils.defaultString(serviceName));
                // 返回码
                logJson.put("returnCode", StringUtils.defaultString(returnCode));
                // 耗时
                logJson.put("cost", cost);
                // 调用类型（dubbo、mq、http）
                logJson.put("callType", callType.getType());
                // 调用方向（in、out）
                logJson.put("direction", direction.getDirection());

                mainLogger.info(logJson.toJSONString());
            } catch (Exception e) {
                mainLogger.error("MainLogUtils logInterfaceCall error", e);
            }
        }
    }



    /**
     * @description: 生成随机traceId
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025-04-01 11:28:48
     * @since JDK 1.8
     */
    public static String generateTraceId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * @description: 日期格式化
     * @param date 日期
     * @param style 格式
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025-04-01 11:28:48
     * @since JDK 1.8
     */
    public static String fmtDate(Date date, String style) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(style);
        return dateFormat.format(date);
    }

    /**
     * @description: 记录Dubbo服务调用日志，自动获取远程IP和应用名称
     * @param traceId 客户端标识
     * @param ranNo 调用随机号
     * @param serviceName 接口名称
     * @param returnCode 返回码
     * @param cost 耗时
     * @return void
     * <AUTHOR>
     * @date 2025-04-02 20:36:47
     * @since JDK 1.8
     */
    public static void dubboCallIn(String traceId,String ranNo, String serviceName, String returnCode, long cost) {
        String remoteIp = RpcContext.getContext().getRemoteAddressString();
        String remoteName = RpcContext.getContext().getRemoteApplicationName();

        logInterfaceCall(
                traceId,
                ranNo,
                remoteIp,
                remoteName,
                serviceName,
                returnCode,
                cost,
                CallTypeEnum.DUBBO,
                CallDirectionEnum.IN
        );
    }

    /**
     * @description: 记录Dubbo服务出站调用日志，自动获取远程IP和应用名称
     * @param traceId 客户端标识
     * @param ranNo 调用随机号
     * @param serviceName 接口名称
     * @param returnCode 返回码
     * @param cost 耗时
     * @return void
     * <AUTHOR>
     * @date 2025-04-02 20:55:47
     * @since JDK 1.8
     */
    public static void dubboCallOut(String traceId, String ranNo, String serviceName, String returnCode, long cost) {
        String remoteIp = RpcContext.getContext().getRemoteAddressString();
        String remoteName = null;

        logInterfaceCall(
            traceId,
            ranNo,
            remoteIp,
            remoteName,
            serviceName,
            returnCode,
            cost,
            CallTypeEnum.DUBBO,
            CallDirectionEnum.OUT
        );
    }

    /**
     * @description: 获取接口名称
     * @param interfaceName 接口名称
     * @param methodName 方法名称
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025-04-09 11:28:48
     * @since JDK 1.8
     */
    public static String getInterfaceName(String interfaceName,String methodName) {
        return interfaceName + "." + methodName;
    }

}