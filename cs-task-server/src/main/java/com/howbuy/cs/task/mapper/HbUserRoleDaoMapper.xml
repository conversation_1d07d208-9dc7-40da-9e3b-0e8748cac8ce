<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.cs.task.dao.HbUserRoleDao">
	
      <!-- 加载角色对应的用户信息 -->
      <select id="listHbRoleUser" parameterType="Map" resultType="com.howbuy.cs.task.model.HbUserRole" useCache="false">
      	SELECT B.consname AS username, B.conscode AS usercode ,B.mobile
			  FROM HB_USERROLE A, CM_CONSULTANT B
			 where A.usercode = B.CONSCODE AND B.CONSSTATUS = 1 
			 <if test="rolelist.size() > 0" > 
			    AND A.rolecode in (
			      <foreach item="item" index="index" collection="rolelist" separator=",">
		               #{item}
		          </foreach>
			    )
			 </if> 
			 <if test="username != null"> AND (B.Consname like '%'||#{username}||'%' OR B.Conscode like '%'||#{username}||'%') </if> 
      </select>
	  
</mapper>



