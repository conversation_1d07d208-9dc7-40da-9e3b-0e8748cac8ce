package com.howbuy.cs.task.dao;

import java.util.List;
import java.util.Map;

import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface RecyTaskDao {

	/**
	 * 将未处理和再处理的task从新置成未分配
	 */
	Integer updateDistributeFlag();

	/**
	 * 将今日处理过的案件 插入到历史表中
	 */
	Integer insertDataTransferToHis();

	/**
	 * 删除今日表中的数据
	 */
	Integer deleteCsTaskAssignDay();
	
	/**
	 * 更新表中的状态：cs_callout_waitdistribute
	 */
	Integer mergeUpCalloutstatus();
	
	List<Map<String,Object>> qryAdvisorNPDistributeTaskMap(Map<String,String> param);


}
