package com.howbuy.cs.task.model;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 好买组织架构（部门）表
 */
public class HbOrganization {
    /**
    * 部门编码
    */
    private String orgcode;

    /**
    * 部门名称
    */
    private String orgname;

    /**
    * 英文简称
    */
    private String englishname;

    /**
    * 父部门编码
    */
    private String parentorgcode;

    /**
    * 排序
    */
    private BigDecimal sort;

    /**
    * 部门级别（0：部门/组织；1：团队（组））
    */
    private String orgtype;

    /**
    * 状态（0：正常；1：删除）
    */
    private String status;

    /**
    * 是否前台显示（0：显示；1：不显示）
    */
    private String showflag;

    /**
    * 省份编码
    */
    private String province;

    /**
    * 省名称
    */
    private String provincename;

    /**
    * 市编码
    */
    private String city;

    /**
    * 市名称
    */
    private String cityname;

    /**
    * 区县编码
    */
    private String area;

    /**
    * 区县名称
    */
    private String areaname;

    /**
    * 地址
    */
    private String address;

    /**
    * 记录状态（0：表示记录）
    */
    private String recstat;

    /**
    * 复核标志（4：表示已复核）
    */
    private String checkflag;

    /**
    * 联系电话
    */
    private String telno;

    /**
    * 传真
    */
    private String fax;

    /**
    * 创建者ID
    */
    private String creator;

    /**
    * 修改者ID
    */
    private String modifier;

    /**
    * 审核者ID
    */
    private String checker;

    /**
    * 创建时间
    */
    private Date credate;

    /**
    * 修改时间
    */
    private Date moddate;

    /**
    * 审核时间
    */
    private Date checkdate;

    /**
    * 体系编码（1：IC体系；2：CH体系；3：机构体系；4：其他投顾；5：IC二分体系）
    */
    private String syscode;

    /**
    * 体系名称
    */
    private String sysname;

    public String getOrgcode() {
        return orgcode;
    }

    public void setOrgcode(String orgcode) {
        this.orgcode = orgcode;
    }

    public String getOrgname() {
        return orgname;
    }

    public void setOrgname(String orgname) {
        this.orgname = orgname;
    }

    public String getEnglishname() {
        return englishname;
    }

    public void setEnglishname(String englishname) {
        this.englishname = englishname;
    }

    public String getParentorgcode() {
        return parentorgcode;
    }

    public void setParentorgcode(String parentorgcode) {
        this.parentorgcode = parentorgcode;
    }

    public BigDecimal getSort() {
        return sort;
    }

    public void setSort(BigDecimal sort) {
        this.sort = sort;
    }

    public String getOrgtype() {
        return orgtype;
    }

    public void setOrgtype(String orgtype) {
        this.orgtype = orgtype;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getShowflag() {
        return showflag;
    }

    public void setShowflag(String showflag) {
        this.showflag = showflag;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getProvincename() {
        return provincename;
    }

    public void setProvincename(String provincename) {
        this.provincename = provincename;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCityname() {
        return cityname;
    }

    public void setCityname(String cityname) {
        this.cityname = cityname;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getAreaname() {
        return areaname;
    }

    public void setAreaname(String areaname) {
        this.areaname = areaname;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getRecstat() {
        return recstat;
    }

    public void setRecstat(String recstat) {
        this.recstat = recstat;
    }

    public String getCheckflag() {
        return checkflag;
    }

    public void setCheckflag(String checkflag) {
        this.checkflag = checkflag;
    }

    public String getTelno() {
        return telno;
    }

    public void setTelno(String telno) {
        this.telno = telno;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getChecker() {
        return checker;
    }

    public void setChecker(String checker) {
        this.checker = checker;
    }

    public Date getCredate() {
        return credate;
    }

    public void setCredate(Date credate) {
        this.credate = credate;
    }

    public Date getModdate() {
        return moddate;
    }

    public void setModdate(Date moddate) {
        this.moddate = moddate;
    }

    public Date getCheckdate() {
        return checkdate;
    }

    public void setCheckdate(Date checkdate) {
        this.checkdate = checkdate;
    }

    public String getSyscode() {
        return syscode;
    }

    public void setSyscode(String syscode) {
        this.syscode = syscode;
    }

    public String getSysname() {
        return sysname;
    }

    public void setSysname(String sysname) {
        this.sysname = sysname;
    }
}