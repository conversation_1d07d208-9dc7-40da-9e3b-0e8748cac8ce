<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.cs.task.dao.AdvisorTargetDao">
	<update id="updateInitAdvisorTarget">
          update cs_investadvisortarget
             set targetnum   = currtargetnum,
                 multirate   = 1,
                 currcustnum = null,
                 sysModdt    = sysdate
           where validateflag = 0
    </update>
    
	<select id="getToadyValidateAdvisorTotalNum" resultType="com.howbuy.cs.task.model.CsInvestAdvisorTarget">
		select a.*, b.teamcode
		  from cs_investadvisortarget a, cm_consultant b
		 where instr(a.workflag, (to_char(sysdate, 'D') - 1)) != 0
		   and a.validateflag = 0
		   and a.advisorid = b.conscode
		   and not exists
		 (select 1
		          from (select t.conscode
		                  from cm_custconstant t
		                  join cm_consultant t2
		                    on t2.conscode = t.conscode
		                 where t2.isvirtual != '1'
		                   and instr(t2.conscode, '.') <![CDATA[>]]> 0
		                 group by t.conscode
		                having count(1) <![CDATA[>]]> (select t.constcode
		                                    from hb_constant t
		                                   where t.typecode = 'maxCustLimit')) c
		         where c.conscode = a.advisorid)
		 order by a.levelnum
	</select>
	
	<!-- 已弃用 -->
	<select id="getCallOutNoAdvisorCust" resultType="map">
        select *
		  from (select c.conscustno,
		               c.dept_flag as deptflag,
		               nvl(d.provcode, d.provcode_mobile) as provcode,
		               d.source,
		               d.custname,
		               c.mobile,
		               c.custsmsflag,
		               c.waitid,
		               c.amountflag,
		               1 statistictype,
		               row_number() over(partition by c.conscustno order by c.dept_flag desc) num,
		               c.tasktype
		          from (select a.dept_flag,
		                       a.conscustno,
		                       a.mobile,
		                       a.custsmsflag,
		                       a.waitid,
		                       e.amountflag,
		                       a.task_type  tasktype
		                  from cs_callout_waitdistribute a
		                  left join (select conscustno, amountflag
		                              from (select a.amountflag,
		                                           a.conscustno,
		                                           row_number() over(partition by conscustno order by id desc) r
		                                      from cs_communicate_visit a where a.hisflag='1')
		                             where r = 1) e
		                    on e.conscustno = a.conscustno
		                 where not exists (select 1
		                          from cm_custconstant b
		                         where a.conscustno = b.custno and b.conscode != 'DFPXNK')
		                   and a.handle_state = 2
		                   and nvl(a.dept_flag, 0) != 0
		                 order by a.waitdate) c,
		               cm_conscust d
		         where c.conscustno = d.conscustno(+))
		 where num = 1
    </select>
    
    <!-- 已弃用 -->
    <select id="getCallInNoAdvisorCust" resultType="map">
        select *
		  from (select c.conscustno,
		               c.dept_flag as deptflag,
		               nvl(d.provcode, d.provcode_mobile) as provcode,
		               d.source,
		               d.custname,
		               c.mobile,
		               c.custsmsflag,
		               c.callinid waitid,
		               c.amountflag,
		               2 statistictype,
		               row_number() over(partition by c.conscustno order by c.dept_flag desc) num
		          from (select a.handlestate,
		                       a.dept_flag,
		                       a.conscustno,
		                       a.mobile,
		                       a.custsmsflag,
		                       a.callinid,
		                       e.amountflag
		                  from cs_callin a
		                  left join (select conscustno, amountflag
		                              from (select a.amountflag,
		                                           a.conscustno,
		                                           row_number() over(partition by conscustno order by id desc) r
		                                      from cs_communicate_visit a where a.hisflag='1')
		                             where r = 1) e
		                    on e.conscustno = a.conscustno
		                 where not exists (select 1
		                          from cm_custconstant b
		                         where a.conscustno = b.custno and b.conscode != 'DFPXNK')
		                   and a.handlestate = 2
		                   and nvl(a.dept_flag, 0) != 0
		                 order by a.handledate) c,
		               cm_conscust d
		         where c.conscustno = d.conscustno(+))
		 where num = 1
    </select>
	
	<update id="updateBatchUpdateAdvisorTarget" parameterType="java.util.List">
	      <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
	         update cs_investadvisortarget
	         <set>
		          currcustnum = #{item.currCustNum},
		          levelnum = #{item.currLevelNum}
	         </set>
	         where advisorid = #{item.advisorId}
			   and validateflag = 0
       	  </foreach>
     </update>
        
    <select id="getCallOutNoAdvisorCustNew" resultType="map"  useCache="false">
        select *
		  from (select c.conscustno,
		               c.dept_flag as deptflag,
		               nvl(d.provcode, d.provcode_mobile) as provcode,
		               d.source,
		               d.custname,
		               c.mobile_cipher,
		               c.custsmsflag,
		               c.waitid,
		               c.amountflag,
		               1 statistictype,
		               row_number() over(partition by c.conscustno order by c.dept_flag desc) num,
		               c.tasktype,
		               d.invsttype,
		               e.idno_cipher,
		               d.idtype,
		               c.specialmark,
		               c.userid
		          from ( 
		                select a.dept_flag,
		                       a.conscustno,
		                       a.mobile_cipher,
		                       a.custsmsflag,
		                       a.waitid,
		                       e.amountflag,
		                       a.task_type  tasktype,
		                       a.waitdate,
		                       e.specialmark,
		                       nvl(d.userid, h.userid) as userid
		                  from cs_callout_waitdistribute a
		                  left join (select conscustno, amountflag,commintent,investintent,specialmark,WAITID
		                              from (select av.amountflag,
												   av.conscustno,
												   av.commintent,
												   av.investintent,
												   av.specialmark,
										           ta.WAITID,
		                                           row_number() over(partition by ta.WAITID order by av.id desc) r
		                                      from cs_communicate_visit av
										      left join (
												  select cd.TASKID,cd.WAITID from cs_task_assign_day  cd
												  union all
												  select ch.TASKID,ch.WAITID  from cs_task_assign_his ch
												  )ta on av.TASKID=ta.TASKID
											where av.hisflag='1'
										   )
		                              where r = 1) e on e.WAITID = a.WAITID
		                  left join cs_task_assign_day d on d.waitid = a.waitid
						  left join cs_task_assign_his h on h.waitid = a.waitid
		                 where  a.handle_state = 2					 
		                   and not exists (select 1  from CS_ASSIGNED_CUST_CONFIG_MSG w where w.conscustno = a.conscustno and w.stat in ('0','1') )		
		                   and exists (select 1  from cm_custconstant b  where a.conscustno = b.custno and b.conscode = 'leadsFPK')
                         order by a.waitdate desc
		                ) c,
		               cm_conscust d,
		               cm_conscust_cipher e
		         where c.conscustno = d.conscustno and d.conscustno = e.conscustno and d.conscuststatus = '0'
		         order by c.waitdate desc
		         )
		 where num = 1
    </select>
    
    <select id="getCallInNoAdvisorCustNew" resultType="map"   useCache="false">
        select *
		  from (select c.conscustno,
		               c.dept_flag as deptflag,
		               nvl(d.provcode, d.provcode_mobile) as provcode,
		               d.source,
		               d.custname,
		               c.mobile_cipher,
		               c.custsmsflag,
		               c.callinid waitid,
		               c.amountflag,
		               2 statistictype,
		               d.invsttype,
		               e.idno_cipher,
		               d.idtype,
		               row_number() over(partition by c.conscustno order by c.dept_flag desc) num,
		               c.specialmark
		          from (select a.handlestate,
		                       a.dept_flag,
		                       a.conscustno,
		                       a.mobile_cipher,
		                       a.custsmsflag,
		                       a.callinid,
		                       e.amountflag,
		                       e.specialmark
		                  from cs_callin a
		                  left join (select conscustno, amountflag,commintent,investintent,specialmark,CALLINID
		                              from (select av.amountflag,
												   av.conscustno,
												   av.commintent,
												   av.investintent,
												   av.specialmark,
												   av.CALLINID,
		                                           row_number() over(partition by av.CALLINID order by av.ID desc) r
		                                      from cs_communicate_visit av
											  where av.hisflag='1'
										      and av.CALLINID is not null
										   )
		                             where r = 1
						            ) e     on e.CALLINID=a.CALLINID
		                 where a.handlestate = 2
		                   and 	not exists (select 1  from CS_ASSIGNED_CUST_CONFIG_MSG w where w.conscustno = a.conscustno and w.stat in ('0','1') )      
		                   and exists (select 1  from cm_custconstant b  where a.conscustno = b.custno and b.conscode = 'leadsFPK')
		                 order by a.handledate desc
					   ) c,
		               cm_conscust d,
		               cm_conscust_cipher e
		         where c.conscustno = d.conscustno
				   and d.conscustno = e.conscustno
				   and d.conscuststatus = '0')
		 where num = 1
    </select>
    	 
    <select id="qryPhoneArea" parameterType="string" resultType="java.util.Map">
	      SELECT T.PROVCITY_CITYCODE, T.PROVCITY_PROVCODE
		    FROM CM_PHONEINFO T
		   WHERE t.phone = #{phoneNumber}
		     AND ROWNUM = 1
	 </select>
	  
</mapper>