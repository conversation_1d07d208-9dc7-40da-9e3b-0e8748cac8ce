<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.cs.task.dao.TpFundVolRecMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.cs.task.model.TpFundVolRec">
    <!--@mbg.generated-->
    <!--@Table TP_FUND_VOL_REC-->
    <id column="RECORD_NO" jdbcType="VARCHAR" property="recordNo" />
    <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo" />
    <result column="FUND_TX_ACCT_NO" jdbcType="VARCHAR" property="fundTxAcctNo" />
    <result column="DIS_CODE" jdbcType="VARCHAR" property="disCode" />
    <result column="CP_ACCT_NO" jdbcType="VARCHAR" property="cpAcctNo" />
    <result column="FUND_CODE" jdbcType="VARCHAR" property="fundCode" />
    <result column="FUND_SHARE_CLASS" jdbcType="CHAR" property="fundShareClass" />
    <result column="TA_CODE" jdbcType="VARCHAR" property="taCode" />
    <result column="TA_DATE" jdbcType="VARCHAR" property="taDate" />
    <result column="BALANCE_VOL" jdbcType="DECIMAL" property="balanceVol" />
    <result column="AVAIL_VOL" jdbcType="DECIMAL" property="availVol" />
    <result column="FRZN_VOL" jdbcType="DECIMAL" property="frznVol" />
    <result column="JUST_FRZN_VOL" jdbcType="DECIMAL" property="justFrznVol" />
    <result column="PROTOCOL_NO" jdbcType="VARCHAR" property="protocolNo" />
    <result column="CREATE_DTM" jdbcType="TIMESTAMP" property="createDtm" />
    <result column="UPDATE_DTM" jdbcType="TIMESTAMP" property="updateDtm" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    RECORD_NO, TX_ACCT_NO, FUND_TX_ACCT_NO, DIS_CODE, CP_ACCT_NO, FUND_CODE, FUND_SHARE_CLASS, 
    TA_CODE, TA_DATE, BALANCE_VOL, AVAIL_VOL, FRZN_VOL, JUST_FRZN_VOL, PROTOCOL_NO, CREATE_DTM, 
    UPDATE_DTM
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from TP_FUND_VOL_REC
    where RECORD_NO = #{recordNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from TP_FUND_VOL_REC
    where RECORD_NO = #{recordNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.cs.task.model.TpFundVolRec">
    <!--@mbg.generated-->
    insert into TP_FUND_VOL_REC (RECORD_NO, TX_ACCT_NO, FUND_TX_ACCT_NO, 
      DIS_CODE, CP_ACCT_NO, FUND_CODE, 
      FUND_SHARE_CLASS, TA_CODE, TA_DATE, 
      BALANCE_VOL, AVAIL_VOL, FRZN_VOL, 
      JUST_FRZN_VOL, PROTOCOL_NO, CREATE_DTM, 
      UPDATE_DTM)
    values (#{recordNo,jdbcType=VARCHAR}, #{txAcctNo,jdbcType=VARCHAR}, #{fundTxAcctNo,jdbcType=VARCHAR}, 
      #{disCode,jdbcType=VARCHAR}, #{cpAcctNo,jdbcType=VARCHAR}, #{fundCode,jdbcType=VARCHAR}, 
      #{fundShareClass,jdbcType=CHAR}, #{taCode,jdbcType=VARCHAR}, #{taDate,jdbcType=VARCHAR}, 
      #{balanceVol,jdbcType=DECIMAL}, #{availVol,jdbcType=DECIMAL}, #{frznVol,jdbcType=DECIMAL}, 
      #{justFrznVol,jdbcType=DECIMAL}, #{protocolNo,jdbcType=VARCHAR}, #{createDtm,jdbcType=TIMESTAMP}, 
      #{updateDtm,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.cs.task.model.TpFundVolRec">
    <!--@mbg.generated-->
    insert into TP_FUND_VOL_REC
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="recordNo != null">
        RECORD_NO,
      </if>
      <if test="txAcctNo != null">
        TX_ACCT_NO,
      </if>
      <if test="fundTxAcctNo != null">
        FUND_TX_ACCT_NO,
      </if>
      <if test="disCode != null">
        DIS_CODE,
      </if>
      <if test="cpAcctNo != null">
        CP_ACCT_NO,
      </if>
      <if test="fundCode != null">
        FUND_CODE,
      </if>
      <if test="fundShareClass != null">
        FUND_SHARE_CLASS,
      </if>
      <if test="taCode != null">
        TA_CODE,
      </if>
      <if test="taDate != null">
        TA_DATE,
      </if>
      <if test="balanceVol != null">
        BALANCE_VOL,
      </if>
      <if test="availVol != null">
        AVAIL_VOL,
      </if>
      <if test="frznVol != null">
        FRZN_VOL,
      </if>
      <if test="justFrznVol != null">
        JUST_FRZN_VOL,
      </if>
      <if test="protocolNo != null">
        PROTOCOL_NO,
      </if>
      <if test="createDtm != null">
        CREATE_DTM,
      </if>
      <if test="updateDtm != null">
        UPDATE_DTM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="recordNo != null">
        #{recordNo,jdbcType=VARCHAR},
      </if>
      <if test="txAcctNo != null">
        #{txAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAcctNo != null">
        #{fundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="disCode != null">
        #{disCode,jdbcType=VARCHAR},
      </if>
      <if test="cpAcctNo != null">
        #{cpAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="fundShareClass != null">
        #{fundShareClass,jdbcType=CHAR},
      </if>
      <if test="taCode != null">
        #{taCode,jdbcType=VARCHAR},
      </if>
      <if test="taDate != null">
        #{taDate,jdbcType=VARCHAR},
      </if>
      <if test="balanceVol != null">
        #{balanceVol,jdbcType=DECIMAL},
      </if>
      <if test="availVol != null">
        #{availVol,jdbcType=DECIMAL},
      </if>
      <if test="frznVol != null">
        #{frznVol,jdbcType=DECIMAL},
      </if>
      <if test="justFrznVol != null">
        #{justFrznVol,jdbcType=DECIMAL},
      </if>
      <if test="protocolNo != null">
        #{protocolNo,jdbcType=VARCHAR},
      </if>
      <if test="createDtm != null">
        #{createDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDtm != null">
        #{updateDtm,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.cs.task.model.TpFundVolRec">
    <!--@mbg.generated-->
    update TP_FUND_VOL_REC
    <set>
      <if test="txAcctNo != null">
        TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAcctNo != null">
        FUND_TX_ACCT_NO = #{fundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="disCode != null">
        DIS_CODE = #{disCode,jdbcType=VARCHAR},
      </if>
      <if test="cpAcctNo != null">
        CP_ACCT_NO = #{cpAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        FUND_CODE = #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="fundShareClass != null">
        FUND_SHARE_CLASS = #{fundShareClass,jdbcType=CHAR},
      </if>
      <if test="taCode != null">
        TA_CODE = #{taCode,jdbcType=VARCHAR},
      </if>
      <if test="taDate != null">
        TA_DATE = #{taDate,jdbcType=VARCHAR},
      </if>
      <if test="balanceVol != null">
        BALANCE_VOL = #{balanceVol,jdbcType=DECIMAL},
      </if>
      <if test="availVol != null">
        AVAIL_VOL = #{availVol,jdbcType=DECIMAL},
      </if>
      <if test="frznVol != null">
        FRZN_VOL = #{frznVol,jdbcType=DECIMAL},
      </if>
      <if test="justFrznVol != null">
        JUST_FRZN_VOL = #{justFrznVol,jdbcType=DECIMAL},
      </if>
      <if test="protocolNo != null">
        PROTOCOL_NO = #{protocolNo,jdbcType=VARCHAR},
      </if>
      <if test="createDtm != null">
        CREATE_DTM = #{createDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDtm != null">
        UPDATE_DTM = #{updateDtm,jdbcType=TIMESTAMP},
      </if>
    </set>
    where RECORD_NO = #{recordNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.cs.task.model.TpFundVolRec">
    <!--@mbg.generated-->
    update TP_FUND_VOL_REC
    set TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR},
      FUND_TX_ACCT_NO = #{fundTxAcctNo,jdbcType=VARCHAR},
      DIS_CODE = #{disCode,jdbcType=VARCHAR},
      CP_ACCT_NO = #{cpAcctNo,jdbcType=VARCHAR},
      FUND_CODE = #{fundCode,jdbcType=VARCHAR},
      FUND_SHARE_CLASS = #{fundShareClass,jdbcType=CHAR},
      TA_CODE = #{taCode,jdbcType=VARCHAR},
      TA_DATE = #{taDate,jdbcType=VARCHAR},
      BALANCE_VOL = #{balanceVol,jdbcType=DECIMAL},
      AVAIL_VOL = #{availVol,jdbcType=DECIMAL},
      FRZN_VOL = #{frznVol,jdbcType=DECIMAL},
      JUST_FRZN_VOL = #{justFrznVol,jdbcType=DECIMAL},
      PROTOCOL_NO = #{protocolNo,jdbcType=VARCHAR},
      CREATE_DTM = #{createDtm,jdbcType=TIMESTAMP},
      UPDATE_DTM = #{updateDtm,jdbcType=TIMESTAMP}
    where RECORD_NO = #{recordNo,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" useGeneratedKeys="false">
    insert all
    <foreach collection="list" item="item">
      into TP_FUND_VOL_REC
      (RECORD_NO, TX_ACCT_NO, FUND_TX_ACCT_NO, DIS_CODE, CP_ACCT_NO, FUND_CODE, FUND_SHARE_CLASS,
        TA_CODE, TA_DATE, BALANCE_VOL, AVAIL_VOL, FRZN_VOL, JUST_FRZN_VOL, PROTOCOL_NO,
        CREATE_DTM, UPDATE_DTM)
      values
      (#{item.recordNo,jdbcType=VARCHAR}, #{item.txAcctNo,jdbcType=VARCHAR}, #{item.fundTxAcctNo,jdbcType=VARCHAR},
        #{item.disCode,jdbcType=VARCHAR}, #{item.cpAcctNo,jdbcType=VARCHAR}, #{item.fundCode,jdbcType=VARCHAR}, 
        #{item.fundShareClass,jdbcType=CHAR}, #{item.taCode,jdbcType=VARCHAR}, #{item.taDate,jdbcType=VARCHAR}, 
        #{item.balanceVol,jdbcType=DECIMAL}, #{item.availVol,jdbcType=DECIMAL}, #{item.frznVol,jdbcType=DECIMAL}, 
        #{item.justFrznVol,jdbcType=DECIMAL}, #{item.protocolNo,jdbcType=VARCHAR}, #{item.createDtm,jdbcType=TIMESTAMP}, 
        #{item.updateDtm,jdbcType=TIMESTAMP})
    </foreach>
    select 1 from dual
  </insert>

  <delete id="deleteByFundCode">
    delete from TP_FUND_VOL_REC
    where FUND_CODE = #{fundCode,jdbcType=VARCHAR}
  </delete>
</mapper>