/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.buss;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.cs.base.Constants;
import com.howbuy.cs.common.Constant.MessageBusinessIdConstant;
import com.howbuy.cs.outservice.accenter.QueryAccCenterOuterService;
import com.howbuy.cs.outservice.auth.EncryptOuterService;
import com.howbuy.cs.outservice.crm.core.CrmJjxxOuterService;
import com.howbuy.cs.outservice.crm.message.SendMessageOuterService;
import com.howbuy.cs.task.dao.CmDailyCmsReportStreamMapper;
import com.howbuy.cs.task.model.CmDailyCmsReportStream;
import com.howbuy.cs.task.model.ReportWithFunds;
import com.howbuy.cs.task.util.StaticVar;
import com.howbuy.member.dto.report.ProductReportUpdatePushDTO;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/7/21 14:07
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CustDailyReportPushBuss {

    @Autowired
    private CmDailyCmsReportStreamMapper cmDailyCmsReportStreamMapper;

    @Autowired
    private SendMessageOuterService sendMessageOuterService;

    @Autowired
    private CrmJjxxOuterService crmJjxxOuterService;

    @Autowired
    private QueryAccCenterOuterService queryAccCenterOuterService;

    @Autowired
    private EncryptOuterService encryptOuterService;

    /**
     * 基金信息临时缓存，任务执行期间避免重复查询
     */
    private final Map<String, JjxxInfo> fundInfoCache = new ConcurrentHashMap<>();

    /**
     * 分销渠道信息临时缓存，任务执行期间避免重复查询
     */
    private final Map<String, DisChannelCodeEnum> disChannelCache = new ConcurrentHashMap<>();

    /**
     * 臻财公众号 appId
     */
    @Value("${zc.official.account.app.id}")
    private String ZC_OFFICIAL_ACCOUNT_APP_ID;


    @Value("${app.message.url.single.report.template}")
    private String singleReportUrlTemplate;

    @Value("${app.message.url.report.list}")
    private String hbAppReportListUrl;

    /**
     * 臻财公众号单条报告推送模板
     */
    @Value("${app.message.zc.url.report.list}")
    private String zcSingleReportUrlTemplate;


    /**
     * 持仓报告推送流水表标识
     * 1- 查询、插入流水表
     * 0- 不查询、不插入流水表（用于上线时，先进行测试的开关）
     *
     */
    @Value("${cust.daily.report.stream.flag}")
    private String customReportStreamFlag;

    /**
     * @description: 遍历客户报告映射，进行幂等性检查和推送处理
     * @param hboneNoAndReportMap
     * @return void
     * @author: jin.wang03
     * @date: 2025/6/26 14:03
     * @since JDK 1.8
     */
    public void pushMessage(Map<String, List<ReportWithFunds>> hboneNoAndReportMap) {
        // 初始化缓存
        initCache();

        String sendDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        int processedCount = 0;
        int skippedCount = 0;
        int failedCount = 0;

        for (Map.Entry<String, List<ReportWithFunds>> entry : hboneNoAndReportMap.entrySet()) {
            String hboneNo = entry.getKey();
            List<ReportWithFunds> reportWithFundsList = entry.getValue();
            if (CollectionUtils.isEmpty(reportWithFundsList)) {
                log.debug("客户{}没有需要推送的报告，跳过处理", hboneNo);
                skippedCount++;
                continue;
            }

            try {
                // a.幂等性，查询[持仓报告推送流水表]，如果该hboneNo当天已存在，则不处理
                if (isSended(hboneNo, sendDate)) {
                    log.debug("客户{}在{}已推送过报告，跳过处理", hboneNo, sendDate);
                    skippedCount++;
                    continue;
                }

                // b.如果不存在，则进行后续处理
                log.debug("开始处理客户{}的报告推送，关联报告数量: {}", hboneNo, reportWithFundsList.size());

                // 发送[好买基金APP]
                pushHbApp(hboneNo, reportWithFundsList);

                // 是否关注了[臻财公众号]
                boolean followOfficialAccount =
                        queryAccCenterOuterService.isFollowOfficialAccount(hboneNo, ZC_OFFICIAL_ACCOUNT_APP_ID);
                if (followOfficialAccount) {
                    // 推送[臻财公众号]
                    log.info("客户{}已关注[臻财公众号]，开始推送[臻财公众号]", hboneNo);
                    pushZcGzh(hboneNo, reportWithFundsList);
                } else {
                    // 发送[短信]
                    log.info("客户{}未关注[臻财公众号]，开始发送[短信]", hboneNo);
                    pushSms(hboneNo, reportWithFundsList);
                }
                // 插入[持仓报告推送流水表]
                streamInsertToDb(hboneNo, sendDate);

                processedCount++;
                log.info("处理客户{}的报告推送成功", hboneNo);
            } catch (Exception e) {
                log.error("处理客户{}的报告推送失败", hboneNo, e);
                failedCount++;
                // 继续处理下一个客户
            }
        }

        log.info("客户每日报告任务执行成功，处理客户数: {}, 跳过客户数: {}, 失败客户数： {}", processedCount, skippedCount, failedCount);

        // 任务结束，清理缓存
        clearCache();
    }


    /**
     * @description: 判断[持仓报告推送流水表]中是否已存在该hboneNo的记录
     * @param hboneNo
     * @param sendDate
     * @return boolean
     * @author: jin.wang03
     * @date: 2025/6/26 14:19
     * @since JDK 1.8
     */
    private boolean isSended(String hboneNo, String sendDate) {
        if (!Constants.YES.equals(customReportStreamFlag)) {
            // 开关关闭，不查询流水表，默认返回：未插入流水表
            return false;
        }
        CmDailyCmsReportStream queryRecord = new CmDailyCmsReportStream();
        queryRecord.setHboneNo(hboneNo);
        queryRecord.setSendDate(sendDate);
        int existCount = cmDailyCmsReportStreamMapper.countByHboneNoAndSendDate(queryRecord);
        return existCount > 0;
    }

    /**
     * @description: 插入[持仓报告推送流水表]
     * @param hboneNo
     * @param sendDate
     * @return void
     * @author: jin.wang03
     * @date: 2025/6/26 14:19
     * @since JDK 1.8
     */
    private void streamInsertToDb(String hboneNo, String sendDate) {
        if (!Constants.YES.equals(customReportStreamFlag)) {
            // 开关关闭，不插入流水表
            return;
        }
        CmDailyCmsReportStream cmDailyCmsReportStream = new CmDailyCmsReportStream();
        cmDailyCmsReportStream.setHboneNo(hboneNo);
        cmDailyCmsReportStream.setSendDate(sendDate);
        cmDailyCmsReportStreamMapper.insert(cmDailyCmsReportStream);
        log.info("插入SEQ_DAILY_CMS_REPORT_STREAM[持仓报告推送流水表]成功, hboneNo: {}, sendDate: {}", hboneNo, sendDate);
    }

    /**
     * @description: 推送[好买基金App]
     * @param hboneNo
     * @param reportWithFundsList
     * @return void
     * @author: jin.wang03
     * @date: 2025/6/26 14:22
     * @since JDK 1.8
     */
    private void pushHbApp(String hboneNo, List<ReportWithFunds> reportWithFundsList) {
        // 获取所有关联的基金代码
        Set<String> uniqueFundCodes = getUniqueFundCodes(reportWithFundsList);

        // 只有一个基金代码时，走单个报告场景
        if (uniqueFundCodes.size() == 1) {
            String fundCode = uniqueFundCodes.iterator().next();
            JjxxInfo jjxxInfo = getJjxxInfoWithCache(fundCode);

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("productName", jjxxInfo.getJjjc());
            jsonObject.put("url", String.format(singleReportUrlTemplate, fundCode));
            sendMessageOuterService.sendHbApp(hboneNo, MessageBusinessIdConstant.FUND_APP_REPORT_UPDATE_ONE_FUND, jsonObject);
            log.info("hbone: {} 单个报告 推送[好买基金App]，模版id:{}, 参数: {}", hboneNo, MessageBusinessIdConstant.FUND_APP_REPORT_UPDATE_ONE_FUND, JSON.toJSONString(jsonObject));
        } else {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("url", hbAppReportListUrl);
            sendMessageOuterService.sendHbApp(hboneNo, MessageBusinessIdConstant.FUND_APP_REPORT_UPDATE_MORE_THAN_ONE_FUND, jsonObject);
            log.info("hbone: {} 多个报告 推送[好买基金App]，模版id:{}, 参数: {}", hboneNo, MessageBusinessIdConstant.FUND_APP_REPORT_UPDATE_MORE_THAN_ONE_FUND, JSON.toJSONString(jsonObject));
        }
    }



    /**
     * @description: 推送[臻财公众号]
     * @param hboneNo
     * @param reportWithFundsList
     * @return void
     * @author: jin.wang03
     * @date: 2025/6/26 14:20
     * @since JDK 1.8
     */
    private void pushZcGzh(String hboneNo, List<ReportWithFunds> reportWithFundsList) {
        for (ReportWithFunds reportWithFunds : reportWithFundsList) {
            ProductReportUpdatePushDTO report = reportWithFunds.getReport();

            for (String fund : reportWithFunds.getFunds()) {
                JjxxInfo jjxxInfo = getJjxxInfoWithCache(fund);

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("productName", jjxxInfo.getJjjc());
                jsonObject.put("reportType", report.getReportTypeName());
                jsonObject.put("reportTitle", report.getReportTitle());
                jsonObject.put("url", String.format(zcSingleReportUrlTemplate, jjxxInfo.getJjdm()));

                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("params", jsonObject);
                jsonObject1.put("wechat_url", report.getReportUrl());

                sendMessageOuterService.sendAutoChannel(hboneNo, MessageBusinessIdConstant.ZC_OFFICIAL_ACCOUNT_REPORT_UPDATE, jsonObject1);
                log.info("hboneNo: {} 推送臻财公众号，参数: {}", hboneNo, JSON.toJSONString(jsonObject));
            }
        }
    }

    /**
     * @description: 推送[短信]
     * @param hboneNo
     * @param reportWithFundsList
     * @return void
     * @author: jin.wang03
     * @date: 2025/6/26 14:20
     * @since JDK 1.8
     */
    private void pushSms(String hboneNo, List<ReportWithFunds> reportWithFundsList) {
        String mobile = queryAccCenterOuterService.getMobile(hboneNo);
        if (StringUtils.isEmpty(mobile)) {
            log.warn("客户{}手机号码为空，跳过发送短信", hboneNo);
            return;
        }
        String mobileCipher = encryptOuterService.encrypt(mobile);

        // 将List<报告>按照基金的分销渠道，分为 好买、好臻、香港三个List。
        Map<String, ProductReportUpdatePushDTO> hbMap = new HashMap<>();
        Map<String, ProductReportUpdatePushDTO> hzMap = new HashMap<>();
        Map<String, ProductReportUpdatePushDTO> hkMap = new HashMap<>();

        for (ReportWithFunds reportWithFunds : reportWithFundsList) {
            ProductReportUpdatePushDTO report = reportWithFunds.getReport();
            for (String fund : reportWithFunds.getFunds()) {
                JjxxInfo jjxxInfo = getJjxxInfoWithCache(fund);
                if (StaticVar.SFXG_YES.equals(jjxxInfo.getSfxg())) {
                    hkMap.put(fund, report);
                    continue;
                }

                DisChannelCodeEnum disCodeEnumByJjdm = getDisChannelWithCache(fund);
                if (Objects.nonNull(disCodeEnumByJjdm) && disCodeEnumByJjdm == DisChannelCodeEnum.HZ) {
                    hzMap.put(fund, report);
                    continue;
                }
                hbMap.put(fund, report);

            }
        }
        log.info("hboneNo: {}发送短信，根据分销渠道汇总，好买渠道:{}, 好臻渠道:{} , 香港渠道: {}", hboneNo,
                JSON.toJSONString(hbMap), JSON.toJSONString(hzMap), JSON.toJSONString(hkMap));

        // 如果每个List<报告>.size == 1且List<持仓基金>.size == 1，这发送具体报告的reportUrl，
        // 如果每个List.size > 1，则发送跳转到全部产品报告列表的ur
        // 好买
        pushSMSByDisCode(mobileCipher, hbMap,
                MessageBusinessIdConstant.SMS_REPORT_UPDATE_ONE_FUND_DISTRIBUTION_FUND,
                MessageBusinessIdConstant.SMS_REPORT_UPDATE_MORE_THAN_ONE_FUND_DISTRIBUTION_FUND);
        // 好臻
        pushSMSByDisCode(mobileCipher, hzMap,
                MessageBusinessIdConstant.SMS_REPORT_UPDATE_ONE_FUND_DISTRIBUTION_HZ,
                MessageBusinessIdConstant.SMS_REPORT_UPDATE_MORE_THAN_ONE_FUND_DISTRIBUTION_HZ);
        // 香港
        pushSMSByDisCode(mobileCipher, hkMap,
                MessageBusinessIdConstant.SMS_REPORT_UPDATE_ONE_FUND_DISTRIBUTION_FUND_HK,
                MessageBusinessIdConstant.SMS_REPORT_UPDATE_MORE_THAN_ONE_FUND_DISTRIBUTION_FUND_HK);
    }

    /**
     * @description: 获取报告列表中所有关联的基金代码
     * @param reportWithFundsList 报告与基金关联列表
     * @return Set<String> 唯一基金代码集合
     * @author: jin.wang03
     * @date: 2025-07-15 09:34:40
     * @since JDK 1.8
     */
    private Set<String> getUniqueFundCodes(List<ReportWithFunds> reportWithFundsList) {
        Set<String> uniqueFundCodes = new HashSet<>();
        if (CollectionUtils.isNotEmpty(reportWithFundsList)) {
            for (ReportWithFunds reportWithFunds : reportWithFundsList) {
                if (CollectionUtils.isNotEmpty(reportWithFunds.getFunds())) {
                    uniqueFundCodes.addAll(reportWithFunds.getFunds());
                }
            }
        }
        return uniqueFundCodes;
    }

    /**
     * @param fundCode 基金代码
     * @return JjxxInfo 基金信息
     * @description: 带缓存的基金信息查询
     * @author: jin.wang03
     * @date: 2025-06-27 09:35:38
     * @since JDK 1.8
     */
    private JjxxInfo getJjxxInfoWithCache(String fundCode) {
        return fundInfoCache.computeIfAbsent(fundCode, code -> {
            log.debug("查询基金{}信息，加入缓存", code);
            return crmJjxxOuterService.getJjxxByFundCode(code);
        });
    }

    /**
     * @param fundCode 基金代码
     * @return DisChannelCodeEnum 分销渠道枚举
     * @description: 带缓存的分销渠道查询
     * @author: jin.wang03
     * @date: 2025-06-27 09:35:38
     * @since JDK 1.8
     */
    private DisChannelCodeEnum getDisChannelWithCache(String fundCode) {
        return disChannelCache.computeIfAbsent(fundCode, code -> {
            log.debug("查询基金{}分销渠道，加入缓存", code);
            return crmJjxxOuterService.getDisCodeEnumByJjdm(code);
        });
    }


    /**
     * @description: 推送[短信]
     * @param mobileCipher
     * @param channelMap
     * @param singleFundTemplateId
     * @param multipleFundTemplateId
     * @return void
     * @author: jin.wang03
     * @date: 2025/6/26 14:47
     * @since JDK 1.8
     */
    private void pushSMSByDisCode(String mobileCipher, Map<String, ProductReportUpdatePushDTO> channelMap,
                                  String singleFundTemplateId, String multipleFundTemplateId) {
        if (MapUtil.isNotEmpty(channelMap)) {
            if (channelMap.size() == 1) {
                String fundCode = new ArrayList<>(channelMap.keySet()).get(0);
                JjxxInfo jjxxInfo = getJjxxInfoWithCache(fundCode);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("productName", jjxxInfo.getJjjc());
                sendMessageOuterService.sendMobileCipher(mobileCipher, singleFundTemplateId, jsonObject);
            } else {
                JSONObject jsonObject = new JSONObject();
                sendMessageOuterService.sendMobileCipher(mobileCipher, multipleFundTemplateId, jsonObject);
            }
        }
    }

    /**
     * @description: 初始化缓存
     * @author: jin.wang03
     * @date: 2025-06-27 09:35:38
     * @since JDK 1.8
     */
    private void initCache() {
        fundInfoCache.clear();
        disChannelCache.clear();
        log.debug("基金信息缓存已初始化");
    }

    /**
     * @description: 清理缓存，释放内存
     * @author: jin.wang03
     * @date: 2025-06-27 09:35:38
     * @since JDK 1.8
     */
    private void clearCache() {
        int fundCacheSize = fundInfoCache.size();
        int disCacheSize = disChannelCache.size();

        fundInfoCache.clear();
        disChannelCache.clear();

        log.info("基金信息缓存已清理，基金信息缓存条数: {}, 分销渠道缓存条数: {}", fundCacheSize, disCacheSize);
    }

}