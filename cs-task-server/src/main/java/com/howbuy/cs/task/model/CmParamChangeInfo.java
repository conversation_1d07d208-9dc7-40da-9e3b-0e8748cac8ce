package com.howbuy.cs.task.model;

import java.util.Date;

/**
 * 参数变更审核表
 */
public class CmParamChangeInfo {
    /**
    * 参数变动表ID
    */
    private String id;

    /**
    * 参数类型：01-配置-存量分成;
    */
    private String paramType;

    /**
    * 对应参数类型的ID
    */
    private String paramId;

    /**
    * 属性变动值[json字符串]
    */
    private String changeValue;

    /**
    * 变动类型 0-新增 1-修改 2-删除
    */
    private String changeType;

    /**
    * 审核状态 0-待审核 1-已审核
    */
    private String auditStatus;

    /**
    * 审核结果 1-审核通过 0-审核拒绝
    */
    private String auditPass;

    /**
    * 记录有效状态（1-正常  0-删除）
    */
    private String recStat;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 时间戳
    */
    private Date createTimestamp;

    /**
    * 审核人
    */
    private String auditor;

    /**
    * 更新时间
    */
    private Date updatedStimestamp;

    /**
    * 备注-审核意见
    */
    private String auditRemark;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getParamType() {
        return paramType;
    }

    public void setParamType(String paramType) {
        this.paramType = paramType;
    }

    public String getParamId() {
        return paramId;
    }

    public void setParamId(String paramId) {
        this.paramId = paramId;
    }

    public String getChangeValue() {
        return changeValue;
    }

    public void setChangeValue(String changeValue) {
        this.changeValue = changeValue;
    }

    public String getChangeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getAuditPass() {
        return auditPass;
    }

    public void setAuditPass(String auditPass) {
        this.auditPass = auditPass;
    }

    public String getRecStat() {
        return recStat;
    }

    public void setRecStat(String recStat) {
        this.recStat = recStat;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTimestamp() {
        return createTimestamp;
    }

    public void setCreateTimestamp(Date createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    public String getAuditor() {
        return auditor;
    }

    public void setAuditor(String auditor) {
        this.auditor = auditor;
    }

    public Date getUpdatedStimestamp() {
        return updatedStimestamp;
    }

    public void setUpdatedStimestamp(Date updatedStimestamp) {
        this.updatedStimestamp = updatedStimestamp;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }
}