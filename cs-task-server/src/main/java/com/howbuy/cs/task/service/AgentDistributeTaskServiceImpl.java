package com.howbuy.cs.task.service;

import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.howbuy.cs.task.dao.AgentDao;
import com.howbuy.cs.task.model.CsCalloutWaitDistribute;
import com.howbuy.cs.task.model.CsTaskAssignDay;
import com.howbuy.cs.task.model.CsTaskAssignModel;
import com.howbuy.cs.task.model.CsTaskTargetSource;
import com.howbuy.cs.task.model.ValidateAgentVO;
import com.howbuy.cs.task.util.DisposeComparator;
import com.howbuy.cs.task.util.DisposeMode;
import com.howbuy.cs.task.util.DistributeMode;

@Slf4j
@Service("agentDistributeTaskService")
public class AgentDistributeTaskServiceImpl implements AgentDistributeTaskService {
    public Map<String, List<CsCalloutWaitDistribute>> taskMap = null;

    @Autowired
    private AgentDao agentDao;

    /**
     * 坐席任务分配
     */
    @Override
    public void agentTaskDistribute(String arg) {
        log.info("座席分配任务开始。。。");

        try{

            taskMap = new HashMap<>();
            for (DisposeMode tempDisposeMode : DisposeMode.values()) {
                String disposeType = String.valueOf(tempDisposeMode.getDisposeType());
                taskMap.put(disposeType, new LinkedList<CsCalloutWaitDistribute>());
            }
            log.info("1============");

            // 获取今日已签到，并且已经在客服配置表中存在的座席信息
            List<CsTaskTargetSource> validateCsTaskTargetSourceList = agentDao.getValidateAgent();
            log.info("2============");
            // 已finalSource为键值
            Map<String, List<String>> finalSourceMap = new HashMap<>();
            initFinalSourceMap(finalSourceMap, validateCsTaskTargetSourceList);
            log.info("3=============");
            // 获取座席以分配率升序排序(已签到座席)
            List<ValidateAgentVO> validateAgentByAssignRates = agentDao.getValidateAgentByAssignRate();
            log.info("4=============");
            if (validateAgentByAssignRates.size() == 0) {
                log.info("没有满足条件的座席可以分配！");
            } else {
                // 用于存放所有待分配的任务 等待时间最早的排在最前面
                LinkedList<CsCalloutWaitDistribute> allWaitTask = new LinkedList<>();
                // 根据等待时间查找任务
                List<String> allWaitDates = agentDao.getAllWaitDate();
                for (String waitDate : allWaitDates) {
                    Map<String, Object> param = new HashMap<>();
                    param.put("waitDate", waitDate);
                    // 获取所有未分配的任务
                    List<CsCalloutWaitDistribute> csCalloutWaitDistributes = agentDao.getAllWaitCsCalloutWaitDistribute(param);
                    // 获取最新的分配规则
                    CsTaskAssignModel csTaskAssignModel = agentDao.getValidDisposeMode();

                    String modeDetail = csTaskAssignModel.getModeDetail();
                    String[] modeDetails = modeDetail.split(",");

                    List<CsCalloutWaitDistribute> resultMergeTask = mergeWaitDistributeTask(csCalloutWaitDistributes, modeDetails);
                    allWaitTask.addAll(resultMergeTask);

                }
                log.info("5=============");

                // 用于存放已经分配好的任务数据
                List<CsTaskAssignDay> finishTasks = new LinkedList<>();
                while (allWaitTask.size() > 0) {
                    // 取出第一个元素
                    CsCalloutWaitDistribute firstTask = allWaitTask.pop();

                    long firstWaitId = firstTask.getWaitId();

                    // 预约回拨标识符
                    int orderFlag = firstTask.getOrderFlag();
                    String firstSubTaskType = firstTask.getTaskType() + "-" + firstTask.getSubTaskType();

                    // 表示今日签到的座席中有可以处理该来源类型的座席
                    if (finalSourceMap.containsKey(firstSubTaskType)) {
                        List<String> consCodeLists = finalSourceMap.get(firstSubTaskType);

                        // 表示是预约回拨的
                        if (orderFlag == 1) {
                            // 预约回拨座席
                            String orderUserId = firstTask.getOrderUserId();

                            // 预约任务是否已经分配标识符
                            boolean orderAgentFlag = false;
                            for (ValidateAgentVO tempValidateAgentVO : validateAgentByAssignRates) {
                                // 预约回拨座席能匹配的到
                                if (tempValidateAgentVO.getConscode().equals(orderUserId)) {
                                    String tempConsCode = tempValidateAgentVO.getConscode();

                                    // 已分配数量
                                    int assignCount = tempValidateAgentVO.getAssignCount();

                                    // 目标数量
                                    int targetCount = tempValidateAgentVO.getTargetCount();

                                    // 存在客户号，且分配数小于目标数
                                    if (consCodeLists.contains(tempConsCode) && (assignCount < targetCount)) {
                                        CsTaskAssignDay csTaskAssignDay = new CsTaskAssignDay();
                                        csTaskAssignDay.setWaitId(firstWaitId);
                                        csTaskAssignDay.setUserId(tempConsCode);

                                        // 1 表示自动分配
                                        csTaskAssignDay.setDistributeMode(DistributeMode.AUTO.getDistributeModeVal());
                                        csTaskAssignDay.setHandleFlag(firstTask.getHandleState());
                                        csTaskAssignDay.setOrderDate(firstTask.getDisposeDate());
                                        finishTasks.add(csTaskAssignDay);

                                        // 分配数量加1
                                        tempValidateAgentVO.setAssignCount(tempValidateAgentVO.getAssignCount() + 1);

                                        // 当分配的数量等于目标数量时，就将客服从当前比率队列中删除掉
                                        if (tempValidateAgentVO.getAssignCount() == tempValidateAgentVO.getTargetCount()) {
                                            validateAgentByAssignRates.remove(tempValidateAgentVO);

                                            // 刷新map,去掉已经接满任务的座席
                                            refreshFinalSourceMap(finalSourceMap, tempConsCode, firstSubTaskType);
                                        }

                                        // 预约任务已经分配
                                        orderAgentFlag = true;

                                        // 根据分配比率排序
                                        sortAgentList(validateAgentByAssignRates);
                                        break;
                                    }
                                }
                            }
                            if (orderAgentFlag) {
                                continue;
                            }
                        }

                        log.info("8=============");

                        for (ValidateAgentVO tempValidateAgentVO : validateAgentByAssignRates) {
                            String tempConscode = tempValidateAgentVO.getConscode();

                            // 已分配数量
                            int assignCount = tempValidateAgentVO.getAssignCount();

                            // 目标数量
                            int targetCount = tempValidateAgentVO.getTargetCount();

                            // 存在客户号，且分配数小于目标数
                            if (consCodeLists.contains(tempConscode) && (assignCount < targetCount)) {
                                CsTaskAssignDay csTaskAssignDay = new CsTaskAssignDay();
                                csTaskAssignDay.setWaitId(firstWaitId);
                                csTaskAssignDay.setUserId(tempConscode);

                                // 1表示自动分配
                                csTaskAssignDay.setDistributeMode(DistributeMode.AUTO.getDistributeModeVal());
                                csTaskAssignDay.setHandleFlag(firstTask.getHandleState());

                                // 表示是预约回拨的
                                if (orderFlag == 1) {
                                    // 如果是预约回拨的，则将当天表中的预约时间置成待分配表中的再处理时间
                                    csTaskAssignDay.setOrderDate(firstTask.getDisposeDate());
                                }
                                finishTasks.add(csTaskAssignDay);

                                // 分配数量加1
                                tempValidateAgentVO.setAssignCount(tempValidateAgentVO.getAssignCount() + 1);

                                // 当分配的数量等于目标数量时，就将客服从当前比率队列中删除掉
                                if (tempValidateAgentVO.getAssignCount() == tempValidateAgentVO.getTargetCount()) {
                                    validateAgentByAssignRates.remove(tempValidateAgentVO);

                                    // 刷新map,去掉已经接满任务的座席
                                    refreshFinalSourceMap(finalSourceMap, tempConscode, firstSubTaskType);
                                }
                                break;
                            }
                        }
                        sortAgentList(validateAgentByAssignRates);
                    }
                    log.info("6=============");
                    // 如果座席比率队列为空了，就退出循环
                    if (validateAgentByAssignRates.size() == 0) {
                        break;
                    }
                }
                log.info("7=============");
                for (CsTaskAssignDay csTaskAssignDay : finishTasks) {
                    long finishWaitId = csTaskAssignDay.getWaitId();
                    String finishConscode = csTaskAssignDay.getUserId();
                    log.info("finishWaitId = " + finishWaitId + " finishConscode = " + finishConscode);
                }

                if (finishTasks.size() > 0) {
                    log.info("开始更新数据 finishTasks size = " + finishTasks.size());
                    boolean addFlag = agentDao.insertCsTaskAssignDayBatch(finishTasks) == finishTasks.size();
                    if (addFlag) {
                        log.info("成功更新任务今日表");
                        boolean updateFlag = agentDao.updateDistributeFlag() > 0;
                        if (updateFlag) {
                            log.info("成功更新待分配任务表");
                        }
                    }
                }
            }
        } catch (Exception e){
            log.error("", e);
            log.error("", Throwables.getStackTraceAsString(e));
        }
        log.info("座席分配任务结束。。。");
    }

    /**
     * 初始化以finalSource为key的Map
     *
     * @param finalSourceMap
     * @param validateCsTaskTargetSourceList
     */
    private void initFinalSourceMap(Map<String, List<String>> finalSourceMap,
                                    List<CsTaskTargetSource> validateCsTaskTargetSourceList) {
        for (CsTaskTargetSource tempCsTaskTargetSource : validateCsTaskTargetSourceList) {
            String conscode = tempCsTaskTargetSource.getConscode();
            String finalSource = tempCsTaskTargetSource.getFinalSource();
            if (finalSourceMap.containsKey(finalSource)) {
                List<String> conscodeLists = finalSourceMap.get(finalSource);
                conscodeLists.add(conscode);
            } else {
                List<String> conscodeLists = new LinkedList<>();
                conscodeLists.add(conscode);
                finalSourceMap.put(finalSource, conscodeLists);
            }
        }
    }

    /**
     * 如果对应的座席已经接满任务就会重新刷新该map，将该座席从队列中删除掉
     *
     * @param finalSourceMap
     * @param conscode
     * @param finalSource
     */
    private void refreshFinalSourceMap(Map<String, List<String>> finalSourceMap,
                                       String conscode,
                                       String finalSource) {
        List<String> conscodeLists = finalSourceMap.get(finalSource);
        if (conscodeLists != null && conscodeLists.size() > 0) {
            conscodeLists.remove(conscode);
            if (conscodeLists.size() == 0) {
                finalSourceMap.remove(finalSource);
            }
        }
    }

    /**
     * 座席根据分配比率升序排序
     *
     * @param agentLists
     */
    private void sortAgentList(List<ValidateAgentVO> agentLists) {
        Collections.sort(agentLists, new Comparator<ValidateAgentVO>() {
            @Override
            public int compare(ValidateAgentVO agentVO1, ValidateAgentVO agentVO2) {
                double assginRate1 = (double) agentVO1.getAssignCount() / agentVO1.getTargetCount();
                agentVO1.setAssignRate(assginRate1);

                double assginRate2 = (double) agentVO2.getAssignCount() / agentVO2.getTargetCount();
                agentVO2.setAssignRate(assginRate2);

                int flag = 0;
                if (assginRate1 > assginRate2) {
                    flag = 1;
                }
                return flag;
            }

        });
    }

    /**
     * 根据最新的分配规则返回对应的有序的list
     *
     * @param csCalloutWaitDistributes
     * @param modeDetails
     * @return
     */
    public List<CsCalloutWaitDistribute> mergeWaitDistributeTask(List<CsCalloutWaitDistribute> csCalloutWaitDistributes,
                                                                 String[] modeDetails) {
        LinkedList<CsCalloutWaitDistribute> resultWaitDistributes = new LinkedList<>();

        // 对各个类型的list做数据初始化
        initTaskTypeList(csCalloutWaitDistributes);

        // 对各个类型的再处理数据根据再处理个数进行排序
        Collections.sort(taskMap.get(String.valueOf(DisposeMode.TRADE_ADISPOSE.getDisposeType())), new DisposeComparator());
        Collections.sort(taskMap.get(String.valueOf(DisposeMode.ORDER_ADISPOSE.getDisposeType())), new DisposeComparator());
        Collections.sort(taskMap.get(String.valueOf(DisposeMode.SUBSCRIBE_ADISPOSE.getDisposeType())), new DisposeComparator());
        Collections.sort(taskMap.get(String.valueOf(DisposeMode.VOICE_ADISPOSE.getDisposeType())), new DisposeComparator());
        Collections.sort(taskMap.get(String.valueOf(DisposeMode.CALLLOSS_ADISPOSE.getDisposeType())), new DisposeComparator());
        for (String tempModeDetail : modeDetails) {
            resultWaitDistributes.addAll(taskMap.get(tempModeDetail));
        }

        // 最后清空所有list中的数据
        initAllTaskTypeCollection();

        return resultWaitDistributes;
    }

    /**
     * 将数据初始化到几个不同类型的list中
     *
     * @param csCalloutWaitDistributes
     */
    public void initTaskTypeList(List<CsCalloutWaitDistribute> csCalloutWaitDistributes) {
        for (CsCalloutWaitDistribute csCalloutWaitDistribute : csCalloutWaitDistributes) {
            String taskType = String.valueOf(csCalloutWaitDistribute.getTaskType());
            String handleState = String.valueOf(csCalloutWaitDistribute.getHandleState());

            String taskMapFlag = taskType + handleState;
            if(taskMap.get(taskMapFlag) != null ){
                taskMap.get(taskMapFlag).add(csCalloutWaitDistribute);
            }
        }
    }

    /**
     * 清空所有list中的数据
     */
    public void initAllTaskTypeCollection() {
        for (DisposeMode tempDisposeMode : DisposeMode.values()) {
            String disposeType = String.valueOf(tempDisposeMode.getDisposeType());
            taskMap.get(disposeType).clear();
        }
    }

}