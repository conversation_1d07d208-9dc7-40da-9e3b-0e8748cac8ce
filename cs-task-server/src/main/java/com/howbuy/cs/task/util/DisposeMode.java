package com.howbuy.cs.task.util;

public enum DisposeMode {
    TRADE_UNDISPOSE(10000, "交易[未]"), TRADE_ADISPOSE(10001, "交易[再]"),
    ORDER_UNDISPOSE(10010, "预约[未]"), ORDER_ADISPOSE(10011, "预约[再]"),
    SUBSCRIBE_UNDISPOSE(10100, "订阅[未]"), SUBSCRIBE_ADISPOSE(10101, "订阅[再]"),
    VOICE_UNDISPOSE(10110, "语音[未]"), VOICE_ADISPOSE(10111, "语音[再]"),
    CALLLOSS_UNDISPOSE(11000, "呼损[未]"), CALLLOSS_ADISPOSE(11001, "呼损[再]");

    private int disposeType;
    private String disposeName;

    DisposeMode(int disposeType, String disposeName) {
        this.disposeType = disposeType;
        this.disposeName = disposeName;
    }

    public static DisposeMode getDisposeMode(int disposeType) {
        switch (disposeType) {
            case 10000:
                return TRADE_UNDISPOSE;
            case 10001:
                return TRADE_ADISPOSE;
            case 10010:
                return ORDER_UNDISPOSE;
            case 10011:
                return ORDER_ADISPOSE;
            case 10100:
                return SUBSCRIBE_UNDISPOSE;
            case 10101:
                return SUBSCRIBE_ADISPOSE;
            case 10110:
                return VOICE_UNDISPOSE;
            case 10111:
                return VOICE_ADISPOSE;
            case 11000:
                return CALLLOSS_UNDISPOSE;
            case 11001:
                return CALLLOSS_ADISPOSE;
            default:
                return null;
        }
    }

    public static String getDesc(int disposeType) {
        switch (disposeType) {
            case 10000:
                return TRADE_UNDISPOSE.getDisposeName();
            case 10001:
                return TRADE_ADISPOSE.getDisposeName();
            case 10010:
                return ORDER_UNDISPOSE.getDisposeName();
            case 10011:
                return ORDER_ADISPOSE.getDisposeName();
            case 10100:
                return SUBSCRIBE_UNDISPOSE.getDisposeName();
            case 10101:
                return SUBSCRIBE_ADISPOSE.getDisposeName();
            case 10110:
                return VOICE_UNDISPOSE.getDisposeName();
            case 10111:
                return VOICE_ADISPOSE.getDisposeName();
            case 11000:
                return CALLLOSS_UNDISPOSE.getDisposeName();
            case 11001:
                return CALLLOSS_ADISPOSE.getDisposeName();
            default:
                return "";
        }
    }

    public int getDisposeType() {
        return disposeType;
    }

    public void setDisposeType(int disposeType) {
        this.disposeType = disposeType;
    }

    public String getDisposeName() {
        return disposeName;
    }

    public void setDisposeName(String disposeName) {
        this.disposeName = disposeName;
    }
}
