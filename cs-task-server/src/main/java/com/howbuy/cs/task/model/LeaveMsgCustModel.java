package com.howbuy.cs.task.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 实体类LeaveMsgCustModel.java
 * @version 1.0
 */
@Data
public class LeaveMsgCustModel implements Serializable {

	private static final long serialVersionUID = 1L;

    private BigDecimal id;
	
	private Date calldate;
	
    private String callerno;
    
    private String calleeno;
    
    private String agentno;
    
    private String agentname;
    
    private String msg;
    
    private Integer readflag;
    
    private Integer losstype;
    
    private Date dumpdate;
    
    private String keynum;
    
    private BigDecimal customercallid;
    
    private String callernomask;
    
    private String callernodigest;
    
    private String callernocipher;
	
}
