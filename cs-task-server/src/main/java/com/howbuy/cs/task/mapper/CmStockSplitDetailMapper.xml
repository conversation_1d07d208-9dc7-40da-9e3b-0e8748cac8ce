<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.cs.task.dao.CmStockSplitDetailMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.cs.task.model.CmStockSplitDetail">
    <!--@mbg.generated-->
    <!--@Table CM_STOCK_SPLIT_DETAIL-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CONFIG_ID" jdbcType="VARCHAR" property="configId" />
    <result column="CUST_NO" jdbcType="VARCHAR" property="custNo" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIMESTAMP" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIMESTAMP" jdbcType="TIMESTAMP" property="modifyTimestamp" />
    <result column="REC_STAT" jdbcType="VARCHAR" property="recStat" />
    <result column="UPPER_LIMIT" jdbcType="DECIMAL" property="upperLimit" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CONFIG_ID, CUST_NO, CREATOR, CREATE_TIMESTAMP, MODIFIER, MODIFY_TIMESTAMP, REC_STAT, 
    UPPER_LIMIT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_STOCK_SPLIT_DETAIL
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CM_STOCK_SPLIT_DETAIL
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.cs.task.model.CmStockSplitDetail">
    <!--@mbg.generated-->
    insert into CM_STOCK_SPLIT_DETAIL (ID, CONFIG_ID, CUST_NO, 
      CREATOR, CREATE_TIMESTAMP, MODIFIER, 
      MODIFY_TIMESTAMP, REC_STAT, UPPER_LIMIT
      )
    values (#{id,jdbcType=VARCHAR}, #{configId,jdbcType=VARCHAR}, #{custNo,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{createTimestamp,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, 
      #{modifyTimestamp,jdbcType=TIMESTAMP}, #{recStat,jdbcType=VARCHAR}, #{upperLimit,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.cs.task.model.CmStockSplitDetail">
    <!--@mbg.generated-->
    insert into CM_STOCK_SPLIT_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="configId != null">
        CONFIG_ID,
      </if>
      <if test="custNo != null">
        CUST_NO,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="createTimestamp != null">
        CREATE_TIMESTAMP,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="modifyTimestamp != null">
        MODIFY_TIMESTAMP,
      </if>
      <if test="recStat != null">
        REC_STAT,
      </if>
      <if test="upperLimit != null">
        UPPER_LIMIT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="configId != null">
        #{configId,jdbcType=VARCHAR},
      </if>
      <if test="custNo != null">
        #{custNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTimestamp != null">
        #{modifyTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="upperLimit != null">
        #{upperLimit,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.cs.task.model.CmStockSplitDetail">
    <!--@mbg.generated-->
    update CM_STOCK_SPLIT_DETAIL
    <set>
      <if test="configId != null">
        CONFIG_ID = #{configId,jdbcType=VARCHAR},
      </if>
      <if test="custNo != null">
        CUST_NO = #{custNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTimestamp != null">
        MODIFY_TIMESTAMP = #{modifyTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="recStat != null">
        REC_STAT = #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="upperLimit != null">
        UPPER_LIMIT = #{upperLimit,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.cs.task.model.CmStockSplitDetail">
    <!--@mbg.generated-->
    update CM_STOCK_SPLIT_DETAIL
    set CONFIG_ID = #{configId,jdbcType=VARCHAR},
      CUST_NO = #{custNo,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODIFY_TIMESTAMP = #{modifyTimestamp,jdbcType=TIMESTAMP},
      REC_STAT = #{recStat,jdbcType=VARCHAR},
      UPPER_LIMIT = #{upperLimit,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <select id="getId" parameterType="map" resultType="String"  useCache="false" flushCache="true">
    select to_char(SEQ_PARAM_ID.nextval) FROM dual
  </select>

  <!-- 从临时表插入状态有效的明细数据到正式表 -->
  <insert id="insertValidDetailsFromTemp" parameterType="map" useGeneratedKeys="false">
    INSERT INTO CM_STOCK_SPLIT_DETAIL (
      ID, CONFIG_ID, CUST_NO, CREATOR, CREATE_TIMESTAMP, MODIFIER, MODIFY_TIMESTAMP, REC_STAT, UPPER_LIMIT
    )
    select t.ID, t.CONFIG_ID, t.CUST_NO, t.CREATOR, t.CREATE_TIMESTAMP, t.MODIFIER, t.MODIFY_TIMESTAMP, t.REC_STAT, t.UPPER_LIMIT
    from (
      SELECT
        d.ID, d.CONFIG_ID, d.CUST_NO, d.CREATOR, d.CREATE_TIMESTAMP, d.MODIFIER, d.MODIFY_TIMESTAMP, d.REC_STAT, d.UPPER_LIMIT
      FROM CM_STOCK_SPLIT_DETAIL_TMP d
      JOIN CM_STOCK_SPLIT_CONFIG_TMP c ON d.CONFIG_ID = c.ID
      WHERE
        d.REC_STAT = '1'
        AND c.REC_STAT = '1'
        AND c.VALID_FLAG = '1'
        AND c.CONFIG_TYPE = '10'
        AND c.CAL_START_DT between #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
    ) t
  </insert>

</mapper>