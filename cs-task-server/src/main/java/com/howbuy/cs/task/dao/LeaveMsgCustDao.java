package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.LeaveMsgCustModel;

import java.util.List;
import java.util.Map;

import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface LeaveMsgCustDao {

	/**
	 * 获取需要插入的呼损客户
	 */
	List<LeaveMsgCustModel> listLeaveMsgCustLimit100(Map<String,Object> param);
	
	/**
	 * 批量插入呼损数据
	 */
	Integer insertLeaveMsgCustBatch(final List<LeaveMsgCustModel> leaveMsgCustModels);
    
}
