package com.howbuy.cs.task.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 存量分成-客户明细临时表
 */
@Setter
@Getter
public class CmStockSplitDetailTmp {
    /**
    * 主键
    */
    private String id;

    /**
    * 配置-存量分成的ID主键
    */
    private String configId;

    /**
    * 客户号
    */
    private String custNo;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private Date createTimestamp;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 修改时间
    */
    private Date modifyTimestamp;

    /**
    * 记录有效状态（1-正常  0-删除）
    */
    private String recStat;

    /**
    * 分配上限_原
    */
    private BigDecimal upperLimit;

}