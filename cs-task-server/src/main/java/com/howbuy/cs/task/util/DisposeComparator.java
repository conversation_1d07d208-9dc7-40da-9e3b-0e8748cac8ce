package com.howbuy.cs.task.util;


import com.howbuy.cs.task.model.CsCalloutWaitDistribute;

import java.util.Comparator;

public class DisposeComparator implements Comparator<CsCalloutWaitDistribute> {

	@Override
	public int compare(CsCalloutWaitDistribute obj1, CsCalloutWaitDistribute obj2) {
		CsCalloutWaitDistribute csCalloutWaitDistribute1 = (CsCalloutWaitDistribute)obj1;
		CsCalloutWaitDistribute csCalloutWaitDistribute2 = (CsCalloutWaitDistribute)obj2;
		return csCalloutWaitDistribute1.getDisposeNum() - csCalloutWaitDistribute2.getDisposeNum();
	}
}
