package com.howbuy.cs.task.util;

import java.io.UnsupportedEncodingException;

/**
 * 转换字符串的编码
 */
public class ChangeCharset {
	/** 中文超大字符集 */
	public static final String GBK = "GBK";
	
	/** 8 位 UCS 转换格式 */
	public static final String UTF_8 = "UTF-8";
	
	/** 中文GB2312字符集 */
	public static final String GB2312 = "GB2312";
	
	/** ISO 拉丁字母表 No.1，也叫作 ISO-LATIN-1 */
	public static final String ISO_8859_1 = "ISO-8859-1";
	
	
	public static String getEncoding(String str) {
		//System.out.println("==========encoding String："+str);
		ChangeCharset changeCharset = new ChangeCharset();
		String encode = "";
		if (changeCharset.isMatch(str, GBK)) {
			encode = GBK;
		} else if (changeCharset.isMatch(str, GB2312)) {
			encode = GB2312;
		} else if (changeCharset.isMatch(str, ISO_8859_1)) {
			encode = ISO_8859_1;
		}
		//System.out.println("==========getEncoding："+encode);
		return encode;
	}
	
	
	/**
	 * 字符串编码匹配判断方法
	 * @param str 待转换编码的字符串
	 * @param newCharset 目标编码
	 * @return String
	 * @throws UnsupportedEncodingException
	 */
	public boolean isMatch(String str, String newCharset) {
		try {
			if (str.equals(new String(str.getBytes(newCharset), newCharset))) {
				return true;
			}
		} catch (UnsupportedEncodingException e) {
			System.err.println("changeCharset error!");
		}
		return false;
	}
	
	/**
	 * 将字符编码转换成UTF-8码
	 */
	public static String toUTF_8(String str, String oldCharset) {
		if (str != null) {
			try {
				return new String(str.getBytes(oldCharset), "UTF-8");
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			}
		}
		return null;
	}


}