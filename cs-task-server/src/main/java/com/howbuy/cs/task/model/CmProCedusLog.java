/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.model;

import lombok.Data;

import java.util.Date;

/**
 * @description: 存储过程日志表实体类
 * <AUTHOR>
 * @date 2023/11/15 13:45
 * @since JDK 1.8
 */
@Data
public class CmProCedusLog {

    /**
     * 存储过程名称
     */
    private String poName;

    /**
     * 错误标识
     */
    private String poMsg;

    /**
     * 错误时间
     */
    private Date poDateTime;

}