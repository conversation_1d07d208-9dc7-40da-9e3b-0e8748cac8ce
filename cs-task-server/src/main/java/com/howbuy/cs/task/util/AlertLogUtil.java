package com.howbuy.cs.task.util;

import com.alibaba.fastjson.JSONObject;
import com.howbuy.common.util.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 告警日志工具类
 *
 * @author: shaoyang.li
 * @date: 2024/4/19 19:12
 * @since JDK 1.8
 */
public final class AlertLogUtil {

    private static final Logger alertLog = LoggerFactory.getLogger("alertJsonLog");

    /**
     * 警告主题
     */
    private static final String ALERT_TOPIC = "CS_TASK_SERVER";
    /**
     * 告警日志
     *
     * @param clazzName 告警来源类名
     * @param msg       告警信息
     */
    public static void alert(String clazzName, String msg) {
        alert(ALERT_TOPIC, clazzName, msg);
    }

    /**
     * 告警日志
     *
     * @param topic     告警主题
     * @param clazzName 告警来源类名
     * @param msg       告警信息
     */
    public static void alert(String topic, String clazzName, String msg) {
        String traceId = LoggerUtils.getUuid();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("time", DateUtils.getDateFormat(DateUtils.FULL_TIME_FORMAT));
        jsonObject.put("topic", topic);
        jsonObject.put("tid", traceId);
        jsonObject.put("class", clazzName);
        jsonObject.put("msg", msg);
        alertLog.info("{}", jsonObject.toJSONString());
    }

    private AlertLogUtil() {
    }
}
