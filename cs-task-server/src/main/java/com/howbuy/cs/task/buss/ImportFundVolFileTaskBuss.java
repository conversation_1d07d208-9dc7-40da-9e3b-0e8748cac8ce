/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.buss;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.cs.base.Constants;
import com.howbuy.cs.cacheService.lock.LockService;
import com.howbuy.cs.task.model.FundVolFileVo;
import com.howbuy.cs.task.model.TpFundVolRec;
import com.howbuy.cs.task.service.FundFileProcessDtlRecServiceImpl;
import com.howbuy.cs.task.service.TpFundVolRecServiceImpl;
import com.howbuy.cs.task.util.LoggerUtils;
import com.howbuy.cs.task.util.MathUtils;
import com.howbuy.dfile.HFileService;
import com.howbuy.dfile.HTextFileReader;
import com.howbuy.dfile.internal.dto.FileConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @description: 导入份额对账文件任务业务处理类
 * <AUTHOR>
 * @date 2024/11/21 14:39
 * @since JDK 1.8
 */
@Slf4j
@Service("importFundVolFileTaskBuss")
public class ImportFundVolFileTaskBuss extends AbstractTaskBuss {

    @Autowired
    private TpFundVolRecServiceImpl tpFundVolRecServiceImpl;

    @Autowired
    private LockService lockService;

    /**
     * @description: 导入份额对账文件
     * @param tradeDt 交易日期
     * @return void
     * @author: hongdong.xie
     * @date: 2024/11/22 10:54
     * @since JDK 1.8
     */
    public void importFundVolFile(String tradeDt,List<FundVolFileVo> fileVoList){

        String uuid = LoggerUtils.getUuid();
        List<Boolean> futureList = fileVoList.stream().map(fileRec -> CompletableFuture.supplyAsync(() -> {
            LoggerUtils.setChildUUID(uuid);
            try {
                // 根据基金代码处理文件
                processByFundCode(fileRec,tradeDt);
            }catch (Exception e){
                log.error("import fundVolFile process error", e);
                return Boolean.FALSE;
            }
            return Boolean.TRUE;
        }, THREAD_POOL)).collect(Collectors.toList()).stream().map(CompletableFuture::join).collect(Collectors.toList());

        boolean allResult = futureList.stream().allMatch(result -> result);

        if (!allResult) {
            log.error("import fundVolFile process error");
        }

        log.info("importFundVolFile result :{}", allResult);
    }

    /**
     * @description: 根据基金维度处理份额对账文件
     * @param fileRec 文件处理记录
     * @param tradeDt 对账日期
     * @return void
     */
    private void processByFundCode(FundVolFileVo fileRec,String tradeDt) {
        log.info("处理份额对账文件,fundFileProcessDtlRec:{}", JSON.toJSONString(fileRec));

        String fundCode = fileRec.getFundCode();
        // 删除历史数据
        tpFundVolRecServiceImpl.deleteByFundCode(fundCode);
        log.info("删除基金份额记录成功,fundCode:{}", fundCode);
        // 解析并保存文件内容
        parseFile(fileRec,tradeDt);
        log.info("fundCode:{} import success", fundCode);
    }

    /**
     * @description: 解析份额对账文件
     * @param fileRec 文件处理记录
     * @return int
     * @author: hongdong.xie
     * @date: 2024/11/22 10:47
     * @since JDK 1.8
     */
    public int parseFile(FundVolFileVo fileRec, String tradeDt) {
        String key = Constants.FUND_VOL_CHECK_STORECONFIG;
        // 总记录数
        int recNum = 0;
        List<TpFundVolRec> recList = new ArrayList<TpFundVolRec>();
        BusinessException ex = null;
        try {
            String[] strArray = HFileService.getInstance().readLines(key, tradeDt + File.separator + fileRec.getTaCode(), fileRec.getFileName());
            if (strArray == null || strArray.length == 0) {
                // 无效的文件
                ex = new BusinessException("", "无效的文件");
                throw ex;
            }

            // 总笔数|总份额|总可用份额|总冻结份额|总司法冻结份额|版本号|
            String[] header = strArray[0].split("\\|");
            // 总笔数
            int totalRecNum = Integer.parseInt(header[0]);
            int realRecNum = 0;
            recNum = totalRecNum;
            // 实际 笔数
            if (totalRecNum > 0) {
                // 第一行为文件头，第二行为，字段描述，第三行开始为份额对账文件数据
                for (int i = 2; i < strArray.length; i++) {
                    String recLine = strArray[i];
                    if (StringUtils.isEmpty(recLine)) {
                        // 文件头标识笔数处理完成，或读取记录为空时直接返回
                        break;
                    }

                    // 好买交易账号|基金交易账号|分销机构代码|客户银行编号|TA代码|基金代码|份额类型|总余额|可用余额|冻结余额|司法冻结份额份额|协议号
                    String[] recDtl = recLine.split("\\|");
                    if (recDtl.length < 12) {
                        log.error("文件格式错误：{}", JSON.toJSONString(recDtl));
                        // 文件格式错误
                        ex = new BusinessException("", "文件格式错误");
                        throw ex;
                    }
                    totalRecNum--;
                    realRecNum++;

                    TpFundVolRec fundVolCheckFileRecPo = buildTpFundVolRec(recDtl, tradeDt);
                    recList.add(fundVolCheckFileRecPo);
                    // 指定200笔数提交一次批量存储
                    if (i%200 == 0) {
                        tpFundVolRecServiceImpl.insertBatch(recList);
                        recList.clear();
                    }
                }

                // 小于200笔的情况
                if (!recList.isEmpty()) {
                    tpFundVolRecServiceImpl.insertBatch(recList);
                }

                if (totalRecNum != 0 || realRecNum > recNum) {
                    log.error("总记录数与文件内容数不一致,实际笔数,totalRecNum:{},realRecNum:{},recNum:{}", totalRecNum, realRecNum, recNum);
                    // 总记录数与文件内容数不一致
                    ex = new BusinessException("", "总记录数与文件内容数不一致");
                    throw ex;
                }
            }
        } catch (BusinessException e) {
            assert ex != null;
            ex.addSuppressed(e);
            log.error("", e);
        } catch (Exception e) {
            ex = new BusinessException("", "份额对账文件处理失败");
            ex.addSuppressed(e);
            log.error("", e);
        }
        return recNum;
    }

    /**
     * @description: 构建份额对账文件记录
     * @param recDtl 文件内容
     * @param checkDt 对账日期
     * @return com.howbuy.cs.task.model.TpFundVolRec
     * @author: hongdong.xie
     * @date: 2024/11/22 08:52
     * @since JDK 1.8
     */
    private TpFundVolRec buildTpFundVolRec(String[] recDtl, String checkDt) {
        TpFundVolRec po = new TpFundVolRec();
        // 好买交易账号|基金交易账号|分销机构代码|客户银行编号|TA代码|基金代码|份额类型|总余额|可用余额|冻结余额|司法冻结份额份额
        po.setRecordNo(getRecordCode(checkDt, recDtl[5]));
        po.setTaDate(checkDt);
        po.setTxAcctNo(StringUtils.trimToNull(recDtl[0]));
        po.setFundTxAcctNo(StringUtils.trimToNull(recDtl[1]));
        po.setDisCode(StringUtils.trimToNull(recDtl[2]));
        po.setCpAcctNo(StringUtils.trimToNull(recDtl[3]));
        po.setTaCode(StringUtils.trimToNull(recDtl[4]));
        po.setFundCode(StringUtils.trimToNull(recDtl[5]));
        po.setFundShareClass(StringUtils.trimToNull(recDtl[6]));
        po.setBalanceVol(MathUtils.getBigDecimal(StringUtils.trimToNull(recDtl[7])));
        po.setAvailVol(MathUtils.getBigDecimal(StringUtils.trimToNull(recDtl[8])));
        po.setFrznVol(MathUtils.getBigDecimal(StringUtils.trimToNull(recDtl[9])));
        po.setJustFrznVol(MathUtils.getBigDecimal(StringUtils.trimToNull(recDtl[10])));
        po.setProtocolNo(StringUtils.trimToNull(recDtl[11]));
        po.setCreateDtm(new Date());
        po.setUpdateDtm(new Date());
        return po;
    }

    /**
     * @description: 生成记录号
     * @param date
     * @param fundCode
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2024/11/22 13:18
     * @since JDK 1.8
     */
    private String getRecordCode(String date, String fundCode) {
        return date + fundCode + lockService.getSequence();
    }



}