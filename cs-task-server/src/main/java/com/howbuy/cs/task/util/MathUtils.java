/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cs.task.util;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @description: 数字工具类
 * <AUTHOR>
 * @date 2024/11/22 08:51
 * @since JDK 1.8
 */
public class MathUtils {
    /**
     * @description: 获取BigDecimal，如果n为null则返回0
     * @param n
     * @return java.math.BigDecimal
     * @author: hongdong.xie
     * @date: 2024/11/22 08:53
     * @since JDK 1.8
     */
    public static BigDecimal getBigDecimal(BigDecimal n) {
        return n == null ? BigDecimal.ZERO : n;
    }

    /**
     * @description: 获取BigDecimal，如果s为空则返回0
     * @param s
     * @return java.math.BigDecimal
     * @author: hongdong.xie
     * @date: 2024/11/22 08:54
     * @since JDK 1.8
     */
    public static BigDecimal getBigDecimal(String s) {
        return StringUtils.isEmpty(s) ? BigDecimal.ZERO : new BigDecimal(s);
    }

    /**
     * @description: 加法
     * @param b1
     * @param b2
     * @return java.math.BigDecimal
     * @author: hongdong.xie
     * @date: 2024/11/22 08:54
     * @since JDK 1.8
     */
    public static BigDecimal add(BigDecimal b1, BigDecimal b2) {
        return getBigDecimal(b1).add(getBigDecimal(b2));
    }

    /**
     * @description: 减法
     * @param b1
     * @param b2
     * @return java.math.BigDecimal
     * @author: hongdong.xie
     * @date: 2024/11/22 08:54
     * @since JDK 1.8
     */
    public static BigDecimal subtract(BigDecimal b1, BigDecimal b2) {
        return getBigDecimal(b1).subtract(getBigDecimal(b2));
    }

    /**
     * @description: 乘法
     * @param b1
     * @param b2
     * @return java.math.BigDecimal
     * @author: hongdong.xie
     * @date: 2024/11/22 08:54
     * @since JDK 1.8
     */
    public static BigDecimal multiply(BigDecimal b1, BigDecimal b2) {
        return getBigDecimal(b1).multiply(getBigDecimal(b2));
    }

    /**
     * @description: 除法
     * @param b1
     * @param b2
     * @param roundDownNum	保留小数位
     * @return java.math.BigDecimal
     * @author: hongdong.xie
     * @date: 2024/11/22 08:54
     * @since JDK 1.8
     */
    public static BigDecimal divide(BigDecimal b1, BigDecimal b2, int roundDownNum) {
        if (getBigDecimal(b2).compareTo(BigDecimal.ZERO) == 0) {
            throw new RuntimeException("Cannot divide into 0");
        } else {
            return getBigDecimal(b1).divide(getBigDecimal(b2), roundDownNum, 1);
        }
    }

    public static BigDecimal divideRoundUp(BigDecimal b1, BigDecimal b2, int roundDownNum) {
        if (getBigDecimal(b2).compareTo(BigDecimal.ZERO) == 0) {
            throw new RuntimeException("Cannot divide into 0");
        } else {
            return getBigDecimal(b1).divide(getBigDecimal(b2), roundDownNum, 0);
        }
    }

    public static BigDecimal divideRoundHalfUp(BigDecimal b1, BigDecimal b2, int roundDownNum) {
        if (getBigDecimal(b2).compareTo(BigDecimal.ZERO) == 0) {
            throw new RuntimeException("Cannot divide into 0");
        } else {
            return getBigDecimal(b1).divide(getBigDecimal(b2), roundDownNum, 4);
        }
    }

    public static BigDecimal scaleWithRoundDown(BigDecimal n) {
        return scaleWithRoundDown(n, 2);
    }

    public static BigDecimal scaleWithRoundDown(BigDecimal n, Integer newScale) {
        BigDecimal number = getBigDecimal(n);
        return number.setScale(newScale, 1);
    }

    public static BigDecimal scaleWithRoundHalfUp(BigDecimal n) {
        return scaleWithRoundHalfUp(n, 2);
    }

    public static BigDecimal scaleWithRoundHalfUp(BigDecimal n, Integer newScale) {
        BigDecimal number = getBigDecimal(n);
        return number.setScale(newScale, 4);
    }

    public static String bigDecimalToString(BigDecimal bd) {
        return bd == null ? "" : bd.toString();
    }

    public static BigDecimal bigDecimalToStr(String bd) {
        return StringUtils.isEmpty(bd) ? BigDecimal.ZERO : new BigDecimal(bd);
    }

    public static String bigDecimalToStringScaleTwo(BigDecimal bd) {
        return bd == null ? "" : bd.setScale(2, RoundingMode.FLOOR).toString();
    }

    public static String bigDecimalMultiply100ToString(BigDecimal bd) {
        bd = bd.multiply(new BigDecimal("100"));
        return bd == null ? "" : bd.toString();
    }

    public static String bigDecimalMultiply100ToStringAndTwoScale(BigDecimal bd) {
        bd = bd.multiply(new BigDecimal("100")).setScale(2, 1);
        return bd == null ? "" : bd.toString();
    }

    public static BigDecimal getBigDecimalPoint(String s, int point) {
        if (StringUtils.isEmpty(s)) {
            return BigDecimal.ZERO;
        } else {
            BigDecimal b = new BigDecimal(s);
            b = b.setScale(point, 1);
            return b;
        }
    }

    public static BigDecimal getMin(BigDecimal b1, BigDecimal b2) {
        if (null == b1) {
            return b2;
        } else if (null == b2) {
            return b1;
        } else {
            return b1.compareTo(b2) < 0 ? b1 : b2;
        }
    }

    public static BigDecimal bigDecimalToAdd(BigDecimal a1, BigDecimal a2) {
        return a1 != null && a2 != null ? a1.add(a2) : null;
    }

    public static BigDecimal bigDecimalToAddZero(BigDecimal a1, BigDecimal a2) {
        return getBigDecimal(a1).add(getBigDecimal(a2));
    }

    public static BigDecimal getMax(BigDecimal b1, BigDecimal b2) {
        if (null == b1) {
            return b2;
        } else if (null == b2) {
            return b1;
        } else {
            return b1.compareTo(b2) < 0 ? b2 : b1;
        }
    }
}