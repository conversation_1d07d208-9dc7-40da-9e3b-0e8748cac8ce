package com.howbuy.cs.task.dao;

import com.howbuy.cs.task.model.CmConsultantExp;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface CmConsultantExpMapper {
    int deleteByPrimaryKey(String userid);

    int insert(CmConsultantExp record);

    int insertSelective(CmConsultantExp record);

    CmConsultantExp selectByPrimaryKey(String userid);

    int updateByPrimaryKeySelective(CmConsultantExp record);

    int updateByPrimaryKey(CmConsultantExp record);
}