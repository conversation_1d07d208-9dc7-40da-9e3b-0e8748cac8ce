package com.howbuy.cs.task.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @version 1.0
 * @Description: 实体类CsDeptAssignRuleConfig.java
 */
@Data
public class CsDeptAssignRuleConfig implements Serializable {

	private static final long serialVersionUID = -6550185230648029807L;

	private BigDecimal id;

    private String attributioncodeflag;

    private String attributionnameflag;

    private BigDecimal levelnum;

    private Date createdt;

    private Date modifydt;

    private String modifier;

    private String creator;

    private String isdel;
    
    private List<CsDeptAssignRuleOrgConfig> listCsDeptAssignRuleOrgConfig;
    
    private BigDecimal step;
    
    private String steptype;//1:上移；2：下移
    
    /**
     * 规则类型：1-归属地；2-标签
     */
    private String ruletype;
    
    /**
     * 标签编码
     */
    private String labelcode;
    
    /**
     * 便签名称
     */
    private String labelname;

}
