package com.howbuy.cs.task.util;

public enum ValidCalleeNO {
    SEM("3240", "SEM来电"),
    BAIDU("3222", "百度金融之心"),
    PHOENIX_HIGH("3230", "凤凰高端"),
    TENCENT("3300", "腾讯高端"),
    TENCENT_400("3310", "腾讯400-1"),
    HOWBUY("3400", "howbuy-1"),
    AON("3320", "同程产品合作"),
    TENCENT_HIGH_ZHANGJI("4023", "好买高端(掌基)"),
    BAIDU_LOW("3223", "百度低端"),
    PHOENIX_LOW("3231", "凤凰低端"),
    TENCENT_HH("3499", "腾讯"),
	HOWBUY_HK("3403", "换卡"),
	HOWBUY_DK("3404", "盗卡"),
	TENCENT_LCT("3405", "腾讯理财通");

    private String calleeNO;
    private String calleeName;

    private ValidCalleeNO(String calleeNO, String calleeName) {
        this.calleeNO = calleeNO;
        this.calleeName = calleeName;
    }

    public static boolean isValidCalleeNO(String calleeNO) {
        boolean validFlag = false;

        for (ValidCalleeNO tempValidCalleeNO : ValidCalleeNO.values()) {
            if (tempValidCalleeNO.getCalleeNO().equals(calleeNO)) {
                validFlag = true;
                break;
            }
        }

        return validFlag;
    }

    public String getCalleeNO() {
        return calleeNO;
    }

    public void setCalleeNO(String calleeNO) {
        this.calleeNO = calleeNO;
    }

    public String getCalleeName() {
        return calleeName;
    }

    public void setCalleeName(String calleeName) {
        this.calleeName = calleeName;
    }


}
