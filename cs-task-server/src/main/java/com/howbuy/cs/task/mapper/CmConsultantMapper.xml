<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.cs.task.dao.CmConsultantMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.cs.task.model.CmConsultant">
    <!--@mbg.generated-->
    <!--@Table CM_CONSULTANT-->
    <result column="CONSCODE" jdbcType="VARCHAR" property="conscode" />
    <result column="CONSNAME" jdbcType="VARCHAR" property="consname" />
    <result column="CONSLEVEL" jdbcType="VARCHAR" property="conslevel" />
    <result column="TEAMCODE" jdbcType="VARCHAR" property="teamcode" />
    <result column="CHARACTER" jdbcType="VARCHAR" property="character" />
    <result column="CONSSTATUS" jdbcType="VARCHAR" property="consstatus" />
    <result column="RECSTAT" jdbcType="VARCHAR" property="recstat" />
    <result column="CHECKFLAG" jdbcType="VARCHAR" property="checkflag" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="CHECKER" jdbcType="VARCHAR" property="checker" />
    <result column="CREDT" jdbcType="VARCHAR" property="credt" />
    <result column="MODDT" jdbcType="VARCHAR" property="moddt" />
    <result column="TELNO" jdbcType="VARCHAR" property="telno" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="DEPTCODE" jdbcType="VARCHAR" property="deptcode" />
    <result column="OUTLETCODE" jdbcType="VARCHAR" property="outletcode" />
    <result column="PICTUREURL" jdbcType="VARCHAR" property="pictureurl" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="RESUME" jdbcType="CLOB" property="resume" />
    <result column="STARTDT" jdbcType="VARCHAR" property="startdt" />
    <result column="ENDDT" jdbcType="VARCHAR" property="enddt" />
    <result column="EMPCARDNO" jdbcType="VARCHAR" property="empcardno" />
    <result column="LOGINFLAG" jdbcType="VARCHAR" property="loginflag" />
    <result column="ISSENIORMGR" jdbcType="VARCHAR" property="isseniormgr" />
    <result column="WORKPLACE" jdbcType="VARCHAR" property="workplace" />
    <result column="DIALCHANNEL" jdbcType="VARCHAR" property="dialchannel" />
    <result column="IP" jdbcType="VARCHAR" property="ip" />
    <result column="SALARYAMTBEFORE" jdbcType="DECIMAL" property="salaryamtbefore" />
    <result column="ISVIRTUAL" jdbcType="VARCHAR" property="isvirtual" />
    <result column="ISINSIDE" jdbcType="VARCHAR" property="isinside" />
    <result column="PICADDR" jdbcType="VARCHAR" property="picaddr" />
    <result column="PICADDR1" jdbcType="VARCHAR" property="picaddr1" />
    <result column="CHARACTER1" jdbcType="VARCHAR" property="character1" />
    <result column="POSITION" jdbcType="VARCHAR" property="position" />
    <result column="OWNERTYPE" jdbcType="VARCHAR" property="ownertype" />
    <result column="CODEPICADDR" jdbcType="VARCHAR" property="codepicaddr" />
    <result column="MOBILE_DIGEST" jdbcType="VARCHAR" property="mobileDigest" />
    <result column="AGENTNO" jdbcType="VARCHAR" property="agentno" />
    <result column="WECHATCONSCODE" jdbcType="VARCHAR" property="wechatconscode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CONSCODE, CONSNAME, CONSLEVEL, TEAMCODE, "CHARACTER", CONSSTATUS, RECSTAT, CHECKFLAG, 
    CREATOR, MODIFIER, CHECKER, CREDT, MODDT, TELNO, MOBILE, DEPTCODE, OUTLETCODE, PICTUREURL, 
    EMAIL, RESUME, STARTDT, ENDDT, EMPCARDNO, LOGINFLAG, ISSENIORMGR, WORKPLACE, DIALCHANNEL, 
    IP, SALARYAMTBEFORE, ISVIRTUAL, ISINSIDE, PICADDR, PICADDR1, CHARACTER1, "POSITION", 
    OWNERTYPE, CODEPICADDR, MOBILE_DIGEST, AGENTNO, WECHATCONSCODE
  </sql>
  <insert id="insert" parameterType="com.howbuy.cs.task.model.CmConsultant">
    <!--@mbg.generated-->
    insert into CM_CONSULTANT (CONSCODE, CONSNAME, CONSLEVEL, 
      TEAMCODE, "CHARACTER", CONSSTATUS, 
      RECSTAT, CHECKFLAG, CREATOR, 
      MODIFIER, CHECKER, CREDT, 
      MODDT, TELNO, MOBILE, 
      DEPTCODE, OUTLETCODE, PICTUREURL, 
      EMAIL, RESUME, STARTDT, 
      ENDDT, EMPCARDNO, LOGINFLAG, 
      ISSENIORMGR, WORKPLACE, DIALCHANNEL, 
      IP, SALARYAMTBEFORE, ISVIRTUAL, 
      ISINSIDE, PICADDR, PICADDR1, 
      CHARACTER1, "POSITION", OWNERTYPE, 
      CODEPICADDR, MOBILE_DIGEST, AGENTNO, 
      WECHATCONSCODE)
    values (#{conscode,jdbcType=VARCHAR}, #{consname,jdbcType=VARCHAR}, #{conslevel,jdbcType=VARCHAR}, 
      #{teamcode,jdbcType=VARCHAR}, #{character,jdbcType=VARCHAR}, #{consstatus,jdbcType=VARCHAR}, 
      #{recstat,jdbcType=VARCHAR}, #{checkflag,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{checker,jdbcType=VARCHAR}, #{credt,jdbcType=VARCHAR}, 
      #{moddt,jdbcType=VARCHAR}, #{telno,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, 
      #{deptcode,jdbcType=VARCHAR}, #{outletcode,jdbcType=VARCHAR}, #{pictureurl,jdbcType=VARCHAR}, 
      #{email,jdbcType=VARCHAR}, #{resume,jdbcType=CLOB}, #{startdt,jdbcType=VARCHAR}, 
      #{enddt,jdbcType=VARCHAR}, #{empcardno,jdbcType=VARCHAR}, #{loginflag,jdbcType=VARCHAR}, 
      #{isseniormgr,jdbcType=VARCHAR}, #{workplace,jdbcType=VARCHAR}, #{dialchannel,jdbcType=VARCHAR}, 
      #{ip,jdbcType=VARCHAR}, #{salaryamtbefore,jdbcType=DECIMAL}, #{isvirtual,jdbcType=VARCHAR}, 
      #{isinside,jdbcType=VARCHAR}, #{picaddr,jdbcType=VARCHAR}, #{picaddr1,jdbcType=VARCHAR}, 
      #{character1,jdbcType=VARCHAR}, #{position,jdbcType=VARCHAR}, #{ownertype,jdbcType=VARCHAR}, 
      #{codepicaddr,jdbcType=VARCHAR}, #{mobileDigest,jdbcType=VARCHAR}, #{agentno,jdbcType=VARCHAR}, 
      #{wechatconscode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.cs.task.model.CmConsultant">
    <!--@mbg.generated-->
    insert into CM_CONSULTANT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="conscode != null">
        CONSCODE,
      </if>
      <if test="consname != null">
        CONSNAME,
      </if>
      <if test="conslevel != null">
        CONSLEVEL,
      </if>
      <if test="teamcode != null">
        TEAMCODE,
      </if>
      <if test="character != null">
        "CHARACTER",
      </if>
      <if test="consstatus != null">
        CONSSTATUS,
      </if>
      <if test="recstat != null">
        RECSTAT,
      </if>
      <if test="checkflag != null">
        CHECKFLAG,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="credt != null">
        CREDT,
      </if>
      <if test="moddt != null">
        MODDT,
      </if>
      <if test="telno != null">
        TELNO,
      </if>
      <if test="mobile != null">
        MOBILE,
      </if>
      <if test="deptcode != null">
        DEPTCODE,
      </if>
      <if test="outletcode != null">
        OUTLETCODE,
      </if>
      <if test="pictureurl != null">
        PICTUREURL,
      </if>
      <if test="email != null">
        EMAIL,
      </if>
      <if test="resume != null">
        RESUME,
      </if>
      <if test="startdt != null">
        STARTDT,
      </if>
      <if test="enddt != null">
        ENDDT,
      </if>
      <if test="empcardno != null">
        EMPCARDNO,
      </if>
      <if test="loginflag != null">
        LOGINFLAG,
      </if>
      <if test="isseniormgr != null">
        ISSENIORMGR,
      </if>
      <if test="workplace != null">
        WORKPLACE,
      </if>
      <if test="dialchannel != null">
        DIALCHANNEL,
      </if>
      <if test="ip != null">
        IP,
      </if>
      <if test="salaryamtbefore != null">
        SALARYAMTBEFORE,
      </if>
      <if test="isvirtual != null">
        ISVIRTUAL,
      </if>
      <if test="isinside != null">
        ISINSIDE,
      </if>
      <if test="picaddr != null">
        PICADDR,
      </if>
      <if test="picaddr1 != null">
        PICADDR1,
      </if>
      <if test="character1 != null">
        CHARACTER1,
      </if>
      <if test="position != null">
        "POSITION",
      </if>
      <if test="ownertype != null">
        OWNERTYPE,
      </if>
      <if test="codepicaddr != null">
        CODEPICADDR,
      </if>
      <if test="mobileDigest != null">
        MOBILE_DIGEST,
      </if>
      <if test="agentno != null">
        AGENTNO,
      </if>
      <if test="wechatconscode != null">
        WECHATCONSCODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="conscode != null">
        #{conscode,jdbcType=VARCHAR},
      </if>
      <if test="consname != null">
        #{consname,jdbcType=VARCHAR},
      </if>
      <if test="conslevel != null">
        #{conslevel,jdbcType=VARCHAR},
      </if>
      <if test="teamcode != null">
        #{teamcode,jdbcType=VARCHAR},
      </if>
      <if test="character != null">
        #{character,jdbcType=VARCHAR},
      </if>
      <if test="consstatus != null">
        #{consstatus,jdbcType=VARCHAR},
      </if>
      <if test="recstat != null">
        #{recstat,jdbcType=VARCHAR},
      </if>
      <if test="checkflag != null">
        #{checkflag,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        #{credt,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        #{moddt,jdbcType=VARCHAR},
      </if>
      <if test="telno != null">
        #{telno,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="deptcode != null">
        #{deptcode,jdbcType=VARCHAR},
      </if>
      <if test="outletcode != null">
        #{outletcode,jdbcType=VARCHAR},
      </if>
      <if test="pictureurl != null">
        #{pictureurl,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="resume != null">
        #{resume,jdbcType=CLOB},
      </if>
      <if test="startdt != null">
        #{startdt,jdbcType=VARCHAR},
      </if>
      <if test="enddt != null">
        #{enddt,jdbcType=VARCHAR},
      </if>
      <if test="empcardno != null">
        #{empcardno,jdbcType=VARCHAR},
      </if>
      <if test="loginflag != null">
        #{loginflag,jdbcType=VARCHAR},
      </if>
      <if test="isseniormgr != null">
        #{isseniormgr,jdbcType=VARCHAR},
      </if>
      <if test="workplace != null">
        #{workplace,jdbcType=VARCHAR},
      </if>
      <if test="dialchannel != null">
        #{dialchannel,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="salaryamtbefore != null">
        #{salaryamtbefore,jdbcType=DECIMAL},
      </if>
      <if test="isvirtual != null">
        #{isvirtual,jdbcType=VARCHAR},
      </if>
      <if test="isinside != null">
        #{isinside,jdbcType=VARCHAR},
      </if>
      <if test="picaddr != null">
        #{picaddr,jdbcType=VARCHAR},
      </if>
      <if test="picaddr1 != null">
        #{picaddr1,jdbcType=VARCHAR},
      </if>
      <if test="character1 != null">
        #{character1,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        #{position,jdbcType=VARCHAR},
      </if>
      <if test="ownertype != null">
        #{ownertype,jdbcType=VARCHAR},
      </if>
      <if test="codepicaddr != null">
        #{codepicaddr,jdbcType=VARCHAR},
      </if>
      <if test="mobileDigest != null">
        #{mobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="agentno != null">
        #{agentno,jdbcType=VARCHAR},
      </if>
      <if test="wechatconscode != null">
        #{wechatconscode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert">
    <!--@mbg.generated-->
    insert all 
    <foreach collection="list" item="item">
      into CM_CONSULTANT
      (CONSCODE, CONSNAME, CONSLEVEL, TEAMCODE, "CHARACTER", CONSSTATUS, RECSTAT, CHECKFLAG, 
        CREATOR, MODIFIER, CHECKER, CREDT, MODDT, TELNO, MOBILE, DEPTCODE, OUTLETCODE, 
        PICTUREURL, EMAIL, RESUME, STARTDT, ENDDT, EMPCARDNO, LOGINFLAG, ISSENIORMGR, WORKPLACE, 
        DIALCHANNEL, IP, SALARYAMTBEFORE, ISVIRTUAL, ISINSIDE, PICADDR, PICADDR1, CHARACTER1, 
        "POSITION", OWNERTYPE, CODEPICADDR, MOBILE_DIGEST, AGENTNO, WECHATCONSCODE)
      values
      (#{item.conscode,jdbcType=VARCHAR}, #{item.consname,jdbcType=VARCHAR}, #{item.conslevel,jdbcType=VARCHAR}, 
        #{item.teamcode,jdbcType=VARCHAR}, #{item.character,jdbcType=VARCHAR}, #{item.consstatus,jdbcType=VARCHAR}, 
        #{item.recstat,jdbcType=VARCHAR}, #{item.checkflag,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.checker,jdbcType=VARCHAR}, #{item.credt,jdbcType=VARCHAR}, 
        #{item.moddt,jdbcType=VARCHAR}, #{item.telno,jdbcType=VARCHAR}, #{item.mobile,jdbcType=VARCHAR}, 
        #{item.deptcode,jdbcType=VARCHAR}, #{item.outletcode,jdbcType=VARCHAR}, #{item.pictureurl,jdbcType=VARCHAR}, 
        #{item.email,jdbcType=VARCHAR}, #{item.resume,jdbcType=CLOB}, #{item.startdt,jdbcType=VARCHAR}, 
        #{item.enddt,jdbcType=VARCHAR}, #{item.empcardno,jdbcType=VARCHAR}, #{item.loginflag,jdbcType=VARCHAR}, 
        #{item.isseniormgr,jdbcType=VARCHAR}, #{item.workplace,jdbcType=VARCHAR}, #{item.dialchannel,jdbcType=VARCHAR}, 
        #{item.ip,jdbcType=VARCHAR}, #{item.salaryamtbefore,jdbcType=DECIMAL}, #{item.isvirtual,jdbcType=VARCHAR}, 
        #{item.isinside,jdbcType=VARCHAR}, #{item.picaddr,jdbcType=VARCHAR}, #{item.picaddr1,jdbcType=VARCHAR}, 
        #{item.character1,jdbcType=VARCHAR}, #{item.position,jdbcType=VARCHAR}, #{item.ownertype,jdbcType=VARCHAR}, 
        #{item.codepicaddr,jdbcType=VARCHAR}, #{item.mobileDigest,jdbcType=VARCHAR}, #{item.agentno,jdbcType=VARCHAR}, 
        #{item.wechatconscode,jdbcType=VARCHAR})
    </foreach>
    select 1 from dual
  </insert>

  <select id="selectByConsCode" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from CM_CONSULTANT
    where CONSCODE = #{consCode,jdbcType=VARCHAR}
    </select>
</mapper>