package com.howbuy.cs.task.dao;


import java.util.List;
import java.util.Map;

import com.howbuy.cs.task.model.HbUserRole;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2019/7/11 13:18
 */
public interface HbUserRoleDao {
    /**
     * @Description:查询角色对应用户数据对象
     * @param param
     * @return List<HbUserRole>
     */
    List<HbUserRole> listHbRoleUser(Map<String, Object> param);
    
    
}
