package com.howbuy.cs.task.model;

import java.util.Date;

/**
 * 产品文件处理记录表
 */
public class FundFileProcessDtlRec {
    /**
    * 记录号
    */
    private String recordNo;

    /**
    * TA代码
    */
    private String taCode;

    /**
    * 产品代码
    */
    private String fundCode;

    /**
    * TA交易日期
    */
    private String taTradeDt;

    /**
    * 文件类型:1-清算份额文件
    */
    private String fileType;

    /**
    * 文件名称
    */
    private String fileName;

    /**
    * 文件操作选择：1-生成，2-导入
    */
    private String fileOption;

    /**
    * 文件操作状态：0-未处理，1-导入成功，2-导入失败，3-生成成功，4-生成失败，5-处理中，6-重新导入
    */
    private String fileOpStatus;

    /**
    * 创建日期时间
    */
    private Date createDtm;

    /**
    * 更新日期时间
    */
    private Date updateDtm;

    /**
    * 操作人
    */
    private String operator;

    /**
    * 备注
    */
    private String memo;

    public String getRecordNo() {
        return recordNo;
    }

    public void setRecordNo(String recordNo) {
        this.recordNo = recordNo;
    }

    public String getTaCode() {
        return taCode;
    }

    public void setTaCode(String taCode) {
        this.taCode = taCode;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getTaTradeDt() {
        return taTradeDt;
    }

    public void setTaTradeDt(String taTradeDt) {
        this.taTradeDt = taTradeDt;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileOption() {
        return fileOption;
    }

    public void setFileOption(String fileOption) {
        this.fileOption = fileOption;
    }

    public String getFileOpStatus() {
        return fileOpStatus;
    }

    public void setFileOpStatus(String fileOpStatus) {
        this.fileOpStatus = fileOpStatus;
    }

    public Date getCreateDtm() {
        return createDtm;
    }

    public void setCreateDtm(Date createDtm) {
        this.createDtm = createDtm;
    }

    public Date getUpdateDtm() {
        return updateDtm;
    }

    public void setUpdateDtm(Date updateDtm) {
        this.updateDtm = updateDtm;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
}