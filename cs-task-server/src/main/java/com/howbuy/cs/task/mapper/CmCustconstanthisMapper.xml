<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.cs.task.dao.CmCustconstanthisMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.cs.task.model.CmCustconstanthis">
    <!--@mbg.generated-->
    <!--@Table CM_CUSTCONSTANTHIS-->
    <id column="CUSTCONSHISID" jdbcType="VARCHAR" property="custconshisid" />
    <result column="CUSTNO" jdbcType="VARCHAR" property="custno" />
    <result column="CONSCODE" jdbcType="VARCHAR" property="conscode" />
    <result column="STARTDT" jdbcType="VARCHAR" property="startdt" />
    <result column="ENDDT" jdbcType="VARCHAR" property="enddt" />
    <result column="MEMO" jdbcType="VARCHAR" property="memo" />
    <result column="RECSTAT" jdbcType="VARCHAR" property="recstat" />
    <result column="CHECKFLAG" jdbcType="VARCHAR" property="checkflag" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="CHECKER" jdbcType="VARCHAR" property="checker" />
    <result column="CREDT" jdbcType="VARCHAR" property="credt" />
    <result column="MODDT" jdbcType="VARCHAR" property="moddt" />
    <result column="PCUSTID" jdbcType="VARCHAR" property="pcustid" />
    <result column="BINDDATE" jdbcType="TIMESTAMP" property="binddate" />
    <result column="UNBUNDDATE" jdbcType="TIMESTAMP" property="unbunddate" />
    <result column="NEXTCONS" jdbcType="VARCHAR" property="nextcons" />
    <result column="REASON" jdbcType="VARCHAR" property="reason" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CITYCODE_MOBILE" jdbcType="VARCHAR" property="citycodeMobile" />
    <result column="WIFICHANNEL" jdbcType="VARCHAR" property="wifichannel" />
    <result column="CUSTSOURCE" jdbcType="VARCHAR" property="custsource" />
    <result column="DEALTYPE" jdbcType="VARCHAR" property="dealtype" />
    <result column="AREA" jdbcType="VARCHAR" property="area" />
    <result column="CUSTTYPE" jdbcType="VARCHAR" property="custtype" />
    <result column="CUSTTRADETYPE" jdbcType="VARCHAR" property="custtradetype" />
    <result column="BEFOREHISID" jdbcType="VARCHAR" property="beforehisid" />
    <result column="REPEATCUSTLOGID" jdbcType="DECIMAL" property="repeatcustlogid" />
    <result column="TRANSF_LOG_ID" jdbcType="DECIMAL" property="transfLogId" />
    <result column="OPERATE_DATE" jdbcType="TIMESTAMP" property="operateDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CUSTCONSHISID, CUSTNO, CONSCODE, STARTDT, ENDDT, MEMO, RECSTAT, CHECKFLAG, CREATOR, 
    MODIFIER, CHECKER, CREDT, MODDT, PCUSTID, BINDDATE, UNBUNDDATE, NEXTCONS, REASON, 
    REMARK, CITYCODE_MOBILE, WIFICHANNEL, CUSTSOURCE, DEALTYPE, AREA, CUSTTYPE, CUSTTRADETYPE, 
    BEFOREHISID, REPEATCUSTLOGID, TRANSF_LOG_ID, OPERATE_DATE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_CUSTCONSTANTHIS
    where CUSTCONSHISID = #{custconshisid,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CM_CUSTCONSTANTHIS
    where CUSTCONSHISID = #{custconshisid,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.cs.task.model.CmCustconstanthis">
    <!--@mbg.generated-->
    insert into CM_CUSTCONSTANTHIS (CUSTCONSHISID, CUSTNO, CONSCODE, 
      STARTDT, ENDDT, MEMO, 
      RECSTAT, CHECKFLAG, CREATOR, 
      MODIFIER, CHECKER, CREDT, 
      MODDT, PCUSTID, BINDDATE, 
      UNBUNDDATE, NEXTCONS, REASON, 
      REMARK, CITYCODE_MOBILE, WIFICHANNEL, 
      CUSTSOURCE, DEALTYPE, AREA, 
      CUSTTYPE, CUSTTRADETYPE, BEFOREHISID, 
      REPEATCUSTLOGID, TRANSF_LOG_ID, OPERATE_DATE
      )
    values (#{custconshisid,jdbcType=VARCHAR}, #{custno,jdbcType=VARCHAR}, #{conscode,jdbcType=VARCHAR}, 
      #{startdt,jdbcType=VARCHAR}, #{enddt,jdbcType=VARCHAR}, #{memo,jdbcType=VARCHAR}, 
      #{recstat,jdbcType=VARCHAR}, #{checkflag,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{checker,jdbcType=VARCHAR}, #{credt,jdbcType=VARCHAR}, 
      #{moddt,jdbcType=VARCHAR}, #{pcustid,jdbcType=VARCHAR}, #{binddate,jdbcType=TIMESTAMP}, 
      #{unbunddate,jdbcType=TIMESTAMP}, #{nextcons,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{citycodeMobile,jdbcType=VARCHAR}, #{wifichannel,jdbcType=VARCHAR}, 
      #{custsource,jdbcType=VARCHAR}, #{dealtype,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, 
      #{custtype,jdbcType=VARCHAR}, #{custtradetype,jdbcType=VARCHAR}, #{beforehisid,jdbcType=VARCHAR}, 
      #{repeatcustlogid,jdbcType=DECIMAL}, #{transfLogId,jdbcType=DECIMAL}, #{operateDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.cs.task.model.CmCustconstanthis">
    <!--@mbg.generated-->
    insert into CM_CUSTCONSTANTHIS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="custconshisid != null">
        CUSTCONSHISID,
      </if>
      <if test="custno != null">
        CUSTNO,
      </if>
      <if test="conscode != null">
        CONSCODE,
      </if>
      <if test="startdt != null">
        STARTDT,
      </if>
      <if test="enddt != null">
        ENDDT,
      </if>
      <if test="memo != null">
        MEMO,
      </if>
      <if test="recstat != null">
        RECSTAT,
      </if>
      <if test="checkflag != null">
        CHECKFLAG,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="credt != null">
        CREDT,
      </if>
      <if test="moddt != null">
        MODDT,
      </if>
      <if test="pcustid != null">
        PCUSTID,
      </if>
      <if test="binddate != null">
        BINDDATE,
      </if>
      <if test="unbunddate != null">
        UNBUNDDATE,
      </if>
      <if test="nextcons != null">
        NEXTCONS,
      </if>
      <if test="reason != null">
        REASON,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="citycodeMobile != null">
        CITYCODE_MOBILE,
      </if>
      <if test="wifichannel != null">
        WIFICHANNEL,
      </if>
      <if test="custsource != null">
        CUSTSOURCE,
      </if>
      <if test="dealtype != null">
        DEALTYPE,
      </if>
      <if test="area != null">
        AREA,
      </if>
      <if test="custtype != null">
        CUSTTYPE,
      </if>
      <if test="custtradetype != null">
        CUSTTRADETYPE,
      </if>
      <if test="beforehisid != null">
        BEFOREHISID,
      </if>
      <if test="repeatcustlogid != null">
        REPEATCUSTLOGID,
      </if>
      <if test="transfLogId != null">
        TRANSF_LOG_ID,
      </if>
      <if test="operateDate != null">
        OPERATE_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="custconshisid != null">
        #{custconshisid,jdbcType=VARCHAR},
      </if>
      <if test="custno != null">
        #{custno,jdbcType=VARCHAR},
      </if>
      <if test="conscode != null">
        #{conscode,jdbcType=VARCHAR},
      </if>
      <if test="startdt != null">
        #{startdt,jdbcType=VARCHAR},
      </if>
      <if test="enddt != null">
        #{enddt,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="recstat != null">
        #{recstat,jdbcType=VARCHAR},
      </if>
      <if test="checkflag != null">
        #{checkflag,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        #{credt,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        #{moddt,jdbcType=VARCHAR},
      </if>
      <if test="pcustid != null">
        #{pcustid,jdbcType=VARCHAR},
      </if>
      <if test="binddate != null">
        #{binddate,jdbcType=TIMESTAMP},
      </if>
      <if test="unbunddate != null">
        #{unbunddate,jdbcType=TIMESTAMP},
      </if>
      <if test="nextcons != null">
        #{nextcons,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="citycodeMobile != null">
        #{citycodeMobile,jdbcType=VARCHAR},
      </if>
      <if test="wifichannel != null">
        #{wifichannel,jdbcType=VARCHAR},
      </if>
      <if test="custsource != null">
        #{custsource,jdbcType=VARCHAR},
      </if>
      <if test="dealtype != null">
        #{dealtype,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="custtype != null">
        #{custtype,jdbcType=VARCHAR},
      </if>
      <if test="custtradetype != null">
        #{custtradetype,jdbcType=VARCHAR},
      </if>
      <if test="beforehisid != null">
        #{beforehisid,jdbcType=VARCHAR},
      </if>
      <if test="repeatcustlogid != null">
        #{repeatcustlogid,jdbcType=DECIMAL},
      </if>
      <if test="transfLogId != null">
        #{transfLogId,jdbcType=DECIMAL},
      </if>
      <if test="operateDate != null">
        #{operateDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.cs.task.model.CmCustconstanthis">
    <!--@mbg.generated-->
    update CM_CUSTCONSTANTHIS
    <set>
      <if test="custno != null">
        CUSTNO = #{custno,jdbcType=VARCHAR},
      </if>
      <if test="conscode != null">
        CONSCODE = #{conscode,jdbcType=VARCHAR},
      </if>
      <if test="startdt != null">
        STARTDT = #{startdt,jdbcType=VARCHAR},
      </if>
      <if test="enddt != null">
        ENDDT = #{enddt,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        MEMO = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="recstat != null">
        RECSTAT = #{recstat,jdbcType=VARCHAR},
      </if>
      <if test="checkflag != null">
        CHECKFLAG = #{checkflag,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="checker != null">
        CHECKER = #{checker,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        CREDT = #{credt,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        MODDT = #{moddt,jdbcType=VARCHAR},
      </if>
      <if test="pcustid != null">
        PCUSTID = #{pcustid,jdbcType=VARCHAR},
      </if>
      <if test="binddate != null">
        BINDDATE = #{binddate,jdbcType=TIMESTAMP},
      </if>
      <if test="unbunddate != null">
        UNBUNDDATE = #{unbunddate,jdbcType=TIMESTAMP},
      </if>
      <if test="nextcons != null">
        NEXTCONS = #{nextcons,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        REASON = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="citycodeMobile != null">
        CITYCODE_MOBILE = #{citycodeMobile,jdbcType=VARCHAR},
      </if>
      <if test="wifichannel != null">
        WIFICHANNEL = #{wifichannel,jdbcType=VARCHAR},
      </if>
      <if test="custsource != null">
        CUSTSOURCE = #{custsource,jdbcType=VARCHAR},
      </if>
      <if test="dealtype != null">
        DEALTYPE = #{dealtype,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        AREA = #{area,jdbcType=VARCHAR},
      </if>
      <if test="custtype != null">
        CUSTTYPE = #{custtype,jdbcType=VARCHAR},
      </if>
      <if test="custtradetype != null">
        CUSTTRADETYPE = #{custtradetype,jdbcType=VARCHAR},
      </if>
      <if test="beforehisid != null">
        BEFOREHISID = #{beforehisid,jdbcType=VARCHAR},
      </if>
      <if test="repeatcustlogid != null">
        REPEATCUSTLOGID = #{repeatcustlogid,jdbcType=DECIMAL},
      </if>
      <if test="transfLogId != null">
        TRANSF_LOG_ID = #{transfLogId,jdbcType=DECIMAL},
      </if>
      <if test="operateDate != null">
        OPERATE_DATE = #{operateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where CUSTCONSHISID = #{custconshisid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.cs.task.model.CmCustconstanthis">
    <!--@mbg.generated-->
    update CM_CUSTCONSTANTHIS
    set CUSTNO = #{custno,jdbcType=VARCHAR},
      CONSCODE = #{conscode,jdbcType=VARCHAR},
      STARTDT = #{startdt,jdbcType=VARCHAR},
      ENDDT = #{enddt,jdbcType=VARCHAR},
      MEMO = #{memo,jdbcType=VARCHAR},
      RECSTAT = #{recstat,jdbcType=VARCHAR},
      CHECKFLAG = #{checkflag,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      CHECKER = #{checker,jdbcType=VARCHAR},
      CREDT = #{credt,jdbcType=VARCHAR},
      MODDT = #{moddt,jdbcType=VARCHAR},
      PCUSTID = #{pcustid,jdbcType=VARCHAR},
      BINDDATE = #{binddate,jdbcType=TIMESTAMP},
      UNBUNDDATE = #{unbunddate,jdbcType=TIMESTAMP},
      NEXTCONS = #{nextcons,jdbcType=VARCHAR},
      REASON = #{reason,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      CITYCODE_MOBILE = #{citycodeMobile,jdbcType=VARCHAR},
      WIFICHANNEL = #{wifichannel,jdbcType=VARCHAR},
      CUSTSOURCE = #{custsource,jdbcType=VARCHAR},
      DEALTYPE = #{dealtype,jdbcType=VARCHAR},
      AREA = #{area,jdbcType=VARCHAR},
      CUSTTYPE = #{custtype,jdbcType=VARCHAR},
      CUSTTRADETYPE = #{custtradetype,jdbcType=VARCHAR},
      BEFOREHISID = #{beforehisid,jdbcType=VARCHAR},
      REPEATCUSTLOGID = #{repeatcustlogid,jdbcType=DECIMAL},
      TRANSF_LOG_ID = #{transfLogId,jdbcType=DECIMAL},
      OPERATE_DATE = #{operateDate,jdbcType=TIMESTAMP}
    where CUSTCONSHISID = #{custconshisid,jdbcType=VARCHAR}
  </update>

  <select id="selectLastMonthTransferConscodes" resultType="java.lang.String">
    SELECT DISTINCT CONSCODE
    FROM CM_CUSTCONSTANT
    WHERE STARTDT BETWEEN #{startDate,jdbcType=VARCHAR} AND #{endDate,jdbcType=VARCHAR}
    and CONSCODE in (select distinct CONSCODE from CM_CONSULTANT_CHANGEORG_REC)
  </select>
  <select id="selectLastMonthTransferRecordsByConscode" resultMap="BaseResultMap">
    SELECT q.CUSTCONSHISID, q.CUSTNO, q.CONSCODE, q.STARTDT, t.startdt as ENDDT, q.MEMO, q.RECSTAT, q.CHECKFLAG, q.CREATOR, 
    q.MODIFIER, q.CHECKER, q.CREDT, q.MODDT, q.PCUSTID, q.BINDDATE, q.UNBUNDDATE, t.conscode as NEXTCONS, q.REASON, 
    q.REMARK, q.CITYCODE_MOBILE, q.WIFICHANNEL, q.CUSTSOURCE, q.DEALTYPE, q.AREA, q.CUSTTYPE, q.CUSTTRADETYPE, 
    q.BEFOREHISID, q.REPEATCUSTLOGID, q.TRANSF_LOG_ID, q.OPERATE_DATE
    FROM CM_CUSTCONSTANT t,(
      select * from CM_CUSTCONSTANTHIS p
      where p.ENDDT BETWEEN #{startDate,jdbcType=VARCHAR} AND #{endDate,jdbcType=VARCHAR}
      and p.STARTDT &lt; #{startDate,jdbcType=VARCHAR}
      and p.CONSCODE in(
      select distinct t.conscode from cm_consultant t,(
            SELECT 
                ORGCODE AS DEPARTMENT_CODE,
                CONNECT_BY_ROOT ORGCODE AS TOP_DEPARTMENT_CODE
            FROM 
                HB_ORGANIZATION
            WHERE 
                LEVEL &gt; 0 
            CONNECT BY NOCYCLE
                PRIOR ORGCODE = PARENTORGCODE
            START WITH 
                PARENTORGCODE = '0' 
            ORDER BY 
                DEPARTMENT_CODE
          ) p1 where t.outletcode = p1.department_code
          and p1.top_department_code in ('1000005627', '10', '1')
      )
      and p.nextcons is not null
    ) q
    WHERE t.CUSTNO = q.CUSTNO
    and t.CONSCODE = #{conscode,jdbcType=VARCHAR}
    and t.STARTDT between #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
    and exists(
      select 1 from CM_HIGH_CUSTINFO w where w.CONSCUSTNO = t.CUSTNO and (w.LSCLLABEL='1' OR w.GDCLLABEL='1')
    )
  </select>
</mapper>